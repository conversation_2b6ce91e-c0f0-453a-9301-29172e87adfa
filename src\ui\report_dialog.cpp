#include "report_dialog.h"
#include <QApplication>
#include <QDebug>
#include <QStandardPaths>
#include <QDir>
#include <QDateTime>
#include <QTimer>
#include <QProcess>
#include <QDesktopServices>
#include <QUrl>

ReportDialog::ReportDialog(QWidget *parent)
    : QDialog(parent)
    , m_reportGenerator(new ReportGenerator(this))
    , m_isGenerating(false)
    , m_progressTimer(new QTimer(this))
{
    setupUI();
    setupConnections();
    
    // Initialize with default data
    m_competitionData["name"] = "High Jump Competition";
    m_competitionData["date"] = QDateTime::currentDateTime().toString("yyyy-MM-dd");
    m_competitionData["location"] = "Competition Venue";
    
    updateOutputPath();
    populateTemplates();
}

ReportDialog::~ReportDialog()
{
}

void ReportDialog::setCompetitionData(const QVariantMap &competitionData)
{
    m_competitionData = competitionData;
    updatePreview();
}

void ReportDialog::setAthletesData(const QVariantList &athletesData)
{
    m_athletesData = athletesData;
    populateAthletesTable();
    updatePreview();
}

void ReportDialog::setResultsData(const QVariantList &resultsData)
{
    m_resultsData = resultsData;
    updatePreview();
}

void ReportDialog::setStatistics(const QVariantMap &statistics)
{
    m_statistics = statistics;
    populateStatisticsTable();
    updatePreview();
}

void ReportDialog::setupUI()
{
    setWindowTitle("Generate Report - High Jump Competition Management System");
    setModal(true);
    resize(800, 600);
    
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    
    // Create tab widget
    m_tabWidget = new QTabWidget(this);
    mainLayout->addWidget(m_tabWidget);
    
    // Setup tabs
    setupReportTypeGroup();
    setupFormatGroup();
    setupTemplateGroup();
    setupOutputGroup();
    setupOptionsGroup();
    setupPreviewGroup();
    
    // Setup buttons and progress
    setupButtons();
    
    mainLayout->addWidget(m_progressBar);
    
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    buttonLayout->addStretch();
    buttonLayout->addWidget(m_previewButton);
    buttonLayout->addWidget(m_generateButton);
    buttonLayout->addWidget(m_cancelButton);
    mainLayout->addLayout(buttonLayout);
}

void ReportDialog::setupReportTypeGroup()
{
    QWidget *reportTypeWidget = new QWidget();
    QVBoxLayout *layout = new QVBoxLayout(reportTypeWidget);
    
    m_reportTypeGroup = new QGroupBox("Report Type", reportTypeWidget);
    QVBoxLayout *groupLayout = new QVBoxLayout(m_reportTypeGroup);
    
    m_reportTypeCombo = new QComboBox(m_reportTypeGroup);
    m_reportTypeCombo->addItem("Competition Summary", ReportGenerator::CompetitionSummary);
    m_reportTypeCombo->addItem("Athlete Details", ReportGenerator::AthleteDetails);
    m_reportTypeCombo->addItem("Results Table", ReportGenerator::ResultsTable);
    m_reportTypeCombo->addItem("Performance Analysis", ReportGenerator::PerformanceAnalysis);
    m_reportTypeCombo->addItem("Custom Report", ReportGenerator::CustomReport);
    
    m_reportTypeDescription = new QLabel("Select the type of report you want to generate.", m_reportTypeGroup);
    m_reportTypeDescription->setWordWrap(true);
    
    groupLayout->addWidget(m_reportTypeCombo);
    groupLayout->addWidget(m_reportTypeDescription);
    
    layout->addWidget(m_reportTypeGroup);
    layout->addStretch();
    
    m_tabWidget->addTab(reportTypeWidget, "Report Type");
}

void ReportDialog::setupFormatGroup()
{
    QWidget *formatWidget = new QWidget();
    QVBoxLayout *layout = new QVBoxLayout(formatWidget);
    
    m_formatGroup = new QGroupBox("Output Format", formatWidget);
    QVBoxLayout *groupLayout = new QVBoxLayout(m_formatGroup);
    
    m_formatCombo = new QComboBox(m_formatGroup);
    m_formatCombo->addItem("PDF Document", ReportGenerator::PDF);
    m_formatCombo->addItem("Excel Spreadsheet", ReportGenerator::Excel);
    m_formatCombo->addItem("HTML Web Page", ReportGenerator::HTML);
    
    m_formatDescription = new QLabel("Choose the output format for your report.", m_formatGroup);
    m_formatDescription->setWordWrap(true);
    
    groupLayout->addWidget(m_formatCombo);
    groupLayout->addWidget(m_formatDescription);
    
    layout->addWidget(m_formatGroup);
    layout->addStretch();
    
    m_tabWidget->addTab(formatWidget, "Format");
}

void ReportDialog::setupTemplateGroup()
{
    QWidget *templateWidget = new QWidget();
    QVBoxLayout *layout = new QVBoxLayout(templateWidget);
    
    m_templateGroup = new QGroupBox("Report Template", templateWidget);
    QVBoxLayout *groupLayout = new QVBoxLayout(m_templateGroup);
    
    m_templateCombo = new QComboBox(m_templateGroup);
    m_templateDescription = new QLabel("Select a template for your report layout.", m_templateGroup);
    m_templateDescription->setWordWrap(true);
    
    m_manageTemplatesButton = new QPushButton("Manage Templates", m_templateGroup);
    
    groupLayout->addWidget(m_templateCombo);
    groupLayout->addWidget(m_templateDescription);
    groupLayout->addWidget(m_manageTemplatesButton);
    
    layout->addWidget(m_templateGroup);
    layout->addStretch();
    
    m_tabWidget->addTab(templateWidget, "Template");
}

void ReportDialog::setupOutputGroup()
{
    QWidget *outputWidget = new QWidget();
    QVBoxLayout *layout = new QVBoxLayout(outputWidget);
    
    m_outputGroup = new QGroupBox("Output Settings", outputWidget);
    QVBoxLayout *groupLayout = new QVBoxLayout(m_outputGroup);
    
    QHBoxLayout *pathLayout = new QHBoxLayout();
    m_outputPathEdit = new QLineEdit(m_outputGroup);
    m_browseButton = new QPushButton("Browse...", m_outputGroup);
    pathLayout->addWidget(new QLabel("Output Path:", m_outputGroup));
    pathLayout->addWidget(m_outputPathEdit);
    pathLayout->addWidget(m_browseButton);
    
    m_openAfterGenerationCheck = new QCheckBox("Open file after generation", m_outputGroup);
    m_openAfterGenerationCheck->setChecked(true);
    
    groupLayout->addLayout(pathLayout);
    groupLayout->addWidget(m_openAfterGenerationCheck);
    
    layout->addWidget(m_outputGroup);
    layout->addStretch();
    
    m_tabWidget->addTab(outputWidget, "Output");
}

void ReportDialog::setupOptionsGroup()
{
    QWidget *optionsWidget = new QWidget();
    QVBoxLayout *layout = new QVBoxLayout(optionsWidget);
    
    m_optionsGroup = new QGroupBox("Report Options", optionsWidget);
    QVBoxLayout *groupLayout = new QVBoxLayout(m_optionsGroup);
    
    m_includeStatisticsCheck = new QCheckBox("Include Statistics", m_optionsGroup);
    m_includeStatisticsCheck->setChecked(true);
    
    m_includeAthleteListCheck = new QCheckBox("Include Athlete List", m_optionsGroup);
    m_includeAthleteListCheck->setChecked(true);
    
    m_includeAttemptsCheck = new QCheckBox("Include Attempt Details", m_optionsGroup);
    m_includeAttemptsCheck->setChecked(true);
    
    m_includeTimingCheck = new QCheckBox("Include Timing Information", m_optionsGroup);
    m_includeTimingCheck->setChecked(false);
    
    m_includeChartsCheck = new QCheckBox("Include Charts (if supported)", m_optionsGroup);
    m_includeChartsCheck->setChecked(false);
    
    m_includeTrendsCheck = new QCheckBox("Include Performance Trends", m_optionsGroup);
    m_includeTrendsCheck->setChecked(false);
    
    groupLayout->addWidget(m_includeStatisticsCheck);
    groupLayout->addWidget(m_includeAthleteListCheck);
    groupLayout->addWidget(m_includeAttemptsCheck);
    groupLayout->addWidget(m_includeTimingCheck);
    groupLayout->addWidget(m_includeChartsCheck);
    groupLayout->addWidget(m_includeTrendsCheck);
    
    layout->addWidget(m_optionsGroup);
    layout->addStretch();
    
    m_tabWidget->addTab(optionsWidget, "Options");
}

void ReportDialog::setupPreviewGroup()
{
    QWidget *previewWidget = new QWidget();
    QVBoxLayout *layout = new QVBoxLayout(previewWidget);
    
    m_previewGroup = new QGroupBox("Report Preview", previewWidget);
    QVBoxLayout *groupLayout = new QVBoxLayout(m_previewGroup);
    
    // Create tab widget for preview
    QTabWidget *previewTabs = new QTabWidget(m_previewGroup);
    
    // Athletes table
    m_athletesTable = new QTableWidget(previewTabs);
    m_athletesTable->setColumnCount(6);
    m_athletesTable->setHorizontalHeaderLabels({"Rank", "Name", "Country", "Best Height", "Attempts", "Status"});
    m_athletesTable->horizontalHeader()->setStretchLastSection(true);
    previewTabs->addTab(m_athletesTable, "Athletes");
    
    // Statistics table
    m_statisticsTable = new QTableWidget(previewTabs);
    m_statisticsTable->setColumnCount(2);
    m_statisticsTable->setHorizontalHeaderLabels({"Metric", "Value"});
    m_statisticsTable->horizontalHeader()->setStretchLastSection(true);
    previewTabs->addTab(m_statisticsTable, "Statistics");
    
    // Text preview
    m_previewText = new QTextEdit(previewTabs);
    m_previewText->setReadOnly(true);
    previewTabs->addTab(m_previewText, "Text Preview");
    
    groupLayout->addWidget(previewTabs);
    
    layout->addWidget(m_previewGroup);
    
    m_tabWidget->addTab(previewWidget, "Preview");
}

void ReportDialog::setupButtons()
{
    m_progressBar = new QProgressBar(this);
    m_progressBar->setVisible(false);
    
    m_previewButton = new QPushButton("Preview", this);
    m_generateButton = new QPushButton("Generate Report", this);
    m_cancelButton = new QPushButton("Cancel", this);
    
    m_generateButton->setDefault(true);
}

void ReportDialog::setupConnections()
{
    // Report generator connections
    connect(m_reportGenerator, &ReportGenerator::reportGenerationStarted,
            this, &ReportDialog::onReportGenerationStarted);
    connect(m_reportGenerator, &ReportGenerator::reportGenerationProgress,
            this, &ReportDialog::onReportGenerationProgress);
    connect(m_reportGenerator, &ReportGenerator::reportGenerationCompleted,
            this, &ReportDialog::onReportGenerationCompleted);
    connect(m_reportGenerator, &ReportGenerator::reportGenerationFailed,
            this, &ReportDialog::onReportGenerationFailed);
    
    // UI connections
    connect(m_reportTypeCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &ReportDialog::onReportTypeChanged);
    connect(m_formatCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &ReportDialog::onFormatChanged);
    connect(m_templateCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &ReportDialog::onTemplateChanged);
    connect(m_browseButton, &QPushButton::clicked,
            this, &ReportDialog::onBrowseOutputPath);
    connect(m_generateButton, &QPushButton::clicked,
            this, &ReportDialog::onGenerateReport);
    connect(m_previewButton, &QPushButton::clicked,
            this, &ReportDialog::onPreviewReport);
    connect(m_cancelButton, &QPushButton::clicked,
            this, &ReportDialog::onCancel);
    
    // Progress timer
    connect(m_progressTimer, &QTimer::timeout, [this]() {
        if (m_progressBar->value() < 90) {
            m_progressBar->setValue(m_progressBar->value() + 5);
        }
    });
}

void ReportDialog::populateTemplates()
{
    m_templateCombo->clear();
    
    QList<ReportGenerator::ReportTemplate> templates = m_reportGenerator->getAvailableTemplates();
    for (const auto &template_ : templates) {
        m_templateCombo->addItem(template_.name, template_.name);
    }
    
    if (m_templateCombo->count() > 0) {
        m_templateCombo->setCurrentIndex(0);
    }
}

void ReportDialog::updateOutputPath()
{
    ReportGenerator::ReportType type = static_cast<ReportGenerator::ReportType>(
        m_reportTypeCombo->currentData().toInt());
    ReportGenerator::ReportFormat format = static_cast<ReportGenerator::ReportFormat>(
        m_formatCombo->currentData().toInt());
    
    QString defaultPath = m_reportGenerator->getDefaultOutputPath(type, format);
    m_outputPathEdit->setText(defaultPath);
}

void ReportDialog::updatePreview()
{
    // Update text preview
    ReportGenerator::ReportData data = prepareReportData();
    ReportGenerator::ReportType type = static_cast<ReportGenerator::ReportType>(
        m_reportTypeCombo->currentData().toInt());
    QString htmlContent = m_reportGenerator->generateHTMLContent(type, data);
    m_previewText->setHtml(htmlContent);
}

ReportGenerator::ReportData ReportDialog::prepareReportData()
{
    ReportGenerator::ReportData data;
    data.title = m_competitionData["name"].toString();
    data.subtitle = QString("Competition held on %1 at %2")
                   .arg(m_competitionData["date"].toString())
                   .arg(m_competitionData["location"].toString());
    data.generatedAt = QDateTime::currentDateTime();
    data.competitionData = m_competitionData;
    data.athletesData = m_athletesData;
    data.resultsData = m_resultsData;
    data.statistics = m_statistics;
    
    return data;
}

void ReportDialog::populateAthletesTable()
{
    m_athletesTable->setRowCount(m_athletesData.size());
    
    for (int i = 0; i < m_athletesData.size(); ++i) {
        QVariantMap athlete = m_athletesData[i].toMap();
        
        m_athletesTable->setItem(i, 0, new QTableWidgetItem(athlete["rank"].toString()));
        m_athletesTable->setItem(i, 1, new QTableWidgetItem(athlete["name"].toString()));
        m_athletesTable->setItem(i, 2, new QTableWidgetItem(athlete["country"].toString()));
        m_athletesTable->setItem(i, 3, new QTableWidgetItem(athlete["bestHeight"].toString()));
        m_athletesTable->setItem(i, 4, new QTableWidgetItem(athlete["attempts"].toString()));
        m_athletesTable->setItem(i, 5, new QTableWidgetItem(athlete["status"].toString()));
    }
}

void ReportDialog::populateStatisticsTable()
{
    m_statisticsTable->setRowCount(m_statistics.size());
    
    int row = 0;
    for (auto it = m_statistics.begin(); it != m_statistics.end(); ++it, ++row) {
        m_statisticsTable->setItem(row, 0, new QTableWidgetItem(it.key()));
        m_statisticsTable->setItem(row, 1, new QTableWidgetItem(it.value().toString()));
    }
}

bool ReportDialog::validateInputs()
{
    if (m_outputPathEdit->text().isEmpty()) {
        QMessageBox::warning(this, "Validation Error", "Please specify an output path.");
        return false;
    }
    
    if (m_athletesData.isEmpty() && m_competitionData.isEmpty()) {
        QMessageBox::warning(this, "Validation Error", "No data available for report generation.");
        return false;
    }
    
    return true;
}

QString ReportDialog::getDefaultOutputPath()
{
    return m_outputPathEdit->text();
}

void ReportDialog::onReportTypeChanged(int index)
{
    Q_UNUSED(index)
    
    // Update description
    QString descriptions[] = {
        "Generate a comprehensive summary of the competition including statistics and key highlights.",
        "Create detailed athlete profiles with individual performance data.",
        "Generate a formatted results table with rankings and scores.",
        "Create statistical analysis of competition performance and trends.",
        "Generate a custom report based on selected data and options."
    };
    
    if (index >= 0 && index < 5) {
        m_reportTypeDescription->setText(descriptions[index]);
    }
    
    updateOutputPath();
    updatePreview();
}

void ReportDialog::onFormatChanged(int index)
{
    Q_UNUSED(index)
    
    QString descriptions[] = {
        "Generate a professional PDF document suitable for printing and sharing.",
        "Create an Excel-compatible spreadsheet with data for further analysis.",
        "Generate an HTML web page that can be viewed in any web browser."
    };
    
    if (index >= 0 && index < 3) {
        m_formatDescription->setText(descriptions[index]);
    }
    
    updateOutputPath();
}

void ReportDialog::onTemplateChanged(int index)
{
    Q_UNUSED(index)
    
    if (m_templateCombo->count() > 0) {
        QString templateName = m_templateCombo->currentText();
        QList<ReportGenerator::ReportTemplate> templates = m_reportGenerator->getAvailableTemplates();
        
        for (const auto &template_ : templates) {
            if (template_.name == templateName) {
                m_templateDescription->setText(template_.description);
                break;
            }
        }
    }
}

void ReportDialog::onBrowseOutputPath()
{
    ReportGenerator::ReportFormat format = static_cast<ReportGenerator::ReportFormat>(
        m_formatCombo->currentData().toInt());
    
    QString filter;
    switch (format) {
        case ReportGenerator::PDF:
            filter = "PDF Files (*.pdf)";
            break;
        case ReportGenerator::Excel:
            filter = "Excel Files (*.xlsx *.csv)";
            break;
        case ReportGenerator::HTML:
            filter = "HTML Files (*.html *.htm)";
            break;
    }
    
    QString fileName = QFileDialog::getSaveFileName(this, "Save Report As",
                                                   getDefaultOutputPath(), filter);
    if (!fileName.isEmpty()) {
        m_outputPathEdit->setText(fileName);
    }
}

void ReportDialog::onGenerateReport()
{
    if (!validateInputs()) {
        return;
    }
    
    if (m_isGenerating) {
        return;
    }
    
    m_isGenerating = true;
    m_generateButton->setEnabled(false);
    m_previewButton->setEnabled(false);
    m_progressBar->setVisible(true);
    m_progressBar->setValue(0);
    m_progressTimer->start(100);
    
    ReportGenerator::ReportType type = static_cast<ReportGenerator::ReportType>(
        m_reportTypeCombo->currentData().toInt());
    ReportGenerator::ReportFormat format = static_cast<ReportGenerator::ReportFormat>(
        m_formatCombo->currentData().toInt());
    
    ReportGenerator::ReportData data = prepareReportData();
    
    bool success = m_reportGenerator->generateReport(type, format, getDefaultOutputPath(), data);
    
    if (!success) {
        m_isGenerating = false;
        m_generateButton->setEnabled(true);
        m_previewButton->setEnabled(true);
        m_progressBar->setVisible(false);
        m_progressTimer->stop();
    }
}

void ReportDialog::onPreviewReport()
{
    ReportGenerator::ReportType type = static_cast<ReportGenerator::ReportType>(
        m_reportTypeCombo->currentData().toInt());
    ReportGenerator::ReportData data = prepareReportData();
    
    m_reportGenerator->showPreviewDialog(type, data, this);
}

void ReportDialog::onCancel()
{
    if (m_isGenerating) {
        // Cancel report generation by stopping the process
        m_reportGenerator->cancelGeneration();
        m_isGenerating = false;
        m_generateButton->setEnabled(true);
        m_previewButton->setEnabled(true);
        m_progressBar->setVisible(false);
        m_progressTimer->stop();
    } else {
        reject();
    }
}

void ReportDialog::onReportGenerationStarted(const QString &reportName)
{
    m_progressBar->setValue(10);
}

void ReportDialog::onReportGenerationProgress(int percentage)
{
    m_progressBar->setValue(percentage);
}

void ReportDialog::onReportGenerationCompleted(const QString &outputPath)
{
    m_isGenerating = false;
    m_generateButton->setEnabled(true);
    m_previewButton->setEnabled(true);
    m_progressBar->setVisible(false);
    m_progressTimer->stop();
    m_progressBar->setValue(100);
    
    QMessageBox::information(this, "Report Generated", 
                           QString("Report successfully generated:\n%1").arg(outputPath));
    
    if (m_openAfterGenerationCheck->isChecked()) {
        QDesktopServices::openUrl(QUrl::fromLocalFile(outputPath));
    }
    
    emit reportGenerated(outputPath);
    accept();
}

void ReportDialog::onReportGenerationFailed(const QString &error)
{
    m_isGenerating = false;
    m_generateButton->setEnabled(true);
    m_previewButton->setEnabled(true);
    m_progressBar->setVisible(false);
    m_progressTimer->stop();
    
    QMessageBox::critical(this, "Report Generation Failed", 
                         QString("Failed to generate report:\n%1").arg(error));
    
    emit reportGenerationFailed(error);
} 