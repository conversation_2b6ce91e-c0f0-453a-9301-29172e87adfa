#ifndef PERFORMANCE_MONITOR_H
#define PERFORMANCE_MONITOR_H

#include <QObject>
#include <QString>
#include <QMap>
#include <QList>
#include <QTimer>
#include <QElapsedTimer>
#include <QDateTime>
#include <QMutex>
#include <QThread>
#include <QDebug>

/**
 * @brief Advanced performance monitoring and optimization system for High Jump Competition Management
 * 
 * This class provides comprehensive performance monitoring, profiling, and optimization
 * capabilities for the High Jump Competition Management System. It implements a sophisticated
 * performance tracking system that monitors various aspects of application performance
 * including execution time, memory usage, database performance, and system resources.
 * 
 * The PerformanceMonitor provides:
 * - Real-time performance metric tracking and analysis
 * - Memory usage monitoring and optimization
 * - Database query performance analysis
 * - Automatic optimization recommendations
 * - Performance reporting and data export
 * - Configurable monitoring levels and thresholds
 * - Thread-safe performance data collection
 * 
 * The system supports multiple optimization levels and can automatically
 * apply performance improvements based on collected metrics and thresholds.
 * 
 * @note This class is designed to be thread-safe and can be used across
 *       multiple threads for comprehensive performance monitoring.
 */
class PerformanceMonitor : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief Performance metric structure
     * 
     * Contains detailed information about a single performance measurement,
     * including timing data, categorization, and additional metadata.
     */
    struct PerformanceMetric {
        QString name;           ///< Metric name/identifier
        QString category;       ///< Metric category for grouping
        qint64 startTime;       ///< Start time in nanoseconds
        qint64 endTime;         ///< End time in nanoseconds
        qint64 duration;        ///< Duration in nanoseconds
        QString description;    ///< Human-readable description
        QDateTime timestamp;    ///< When the metric was recorded
        QVariantMap additionalData; ///< Additional metric data
    };

    /**
     * @brief Performance summary structure
     * 
     * Contains aggregated performance statistics for a category of metrics,
     * providing insights into performance patterns and trends.
     */
    struct PerformanceSummary {
        QString category;       ///< Category name
        qint64 totalTime;       ///< Total time for all calls
        qint64 averageTime;     ///< Average time per call
        qint64 minTime;         ///< Minimum time recorded
        qint64 maxTime;         ///< Maximum time recorded
        int callCount;          ///< Number of calls recorded
        QDateTime firstCall;    ///< First call timestamp
        QDateTime lastCall;     ///< Last call timestamp
    };

    /**
     * @brief Memory information structure
     * 
     * Contains detailed information about current memory usage,
     * including total, used, free, and peak memory values.
     */
    struct MemoryInfo {
        qint64 totalMemory;     ///< Total available memory
        qint64 usedMemory;      ///< Currently used memory
        qint64 freeMemory;      ///< Available free memory
        qint64 peakMemory;      ///< Peak memory usage
        double memoryUsagePercent; ///< Memory usage percentage
        QDateTime timestamp;    ///< When memory info was collected
    };

    /**
     * @brief Database performance metrics structure
     * 
     * Contains aggregated database performance statistics,
     * including query counts, timing, and performance analysis.
     */
    struct DatabaseMetrics {
        qint64 queryCount;      ///< Total number of queries
        qint64 totalQueryTime;  ///< Total time for all queries
        qint64 averageQueryTime; ///< Average query time
        qint64 slowestQueryTime; ///< Slowest query time
        QString slowestQuery;   ///< Slowest query text
        QDateTime lastQuery;    ///< Last query timestamp
    };

    /**
     * @brief Optimization level enumeration
     * 
     * Defines different levels of optimization aggressiveness
     * that can be applied to improve performance.
     */
    enum OptimizationLevel {
        None,       ///< No optimization applied
        Basic,      ///< Basic optimization techniques
        Aggressive, ///< Aggressive optimization techniques
        Custom      ///< Custom optimization configuration
    };

    /**
     * @brief Constructs the performance monitor
     * @param parent Parent QObject (default: nullptr)
     * 
     * Initializes the performance monitoring system with default settings.
     * Sets up timers, memory tracking, and optimization capabilities.
     */
    explicit PerformanceMonitor(QObject *parent = nullptr);
    
    /**
     * @brief Destructor
     * 
     * Ensures proper cleanup of monitoring resources and saves
     * performance data for analysis.
     */
    ~PerformanceMonitor();

    // Performance tracking
    /**
     * @brief Starts timing a performance metric
     * @param name Metric name/identifier
     * @param category Metric category (default: "General")
     * 
     * Starts timing a performance metric. The timing will continue
     * until endTimer() is called with the same name.
     */
    void startTimer(const QString &name, const QString &category = "General");
    
    /**
     * @brief Ends timing a performance metric
     * @param name Metric name/identifier
     * 
     * Ends timing a performance metric and records the duration.
     * The metric must have been started with startTimer().
     */
    void endTimer(const QString &name);
    
    /**
     * @brief Records a performance metric with known duration
     * @param name Metric name/identifier
     * @param duration Duration in nanoseconds
     * @param category Metric category (default: "General")
     * 
     * Records a performance metric with a pre-calculated duration.
     * Useful for metrics that are calculated externally.
     */
    void recordMetric(const QString &name, qint64 duration, const QString &category = "General");
    
    // Memory monitoring
    /**
     * @brief Gets current memory usage information
     * @return Current memory information
     * 
     * Returns detailed information about current memory usage
     * including total, used, free, and peak memory values.
     */
    MemoryInfo getCurrentMemoryInfo() const;
    
    /**
     * @brief Starts automatic memory tracking
     * 
     * Enables automatic memory usage monitoring at regular intervals.
     * Memory information will be collected and analyzed automatically.
     */
    void startMemoryTracking();
    
    /**
     * @brief Stops automatic memory tracking
     * 
     * Disables automatic memory usage monitoring.
     * Manual memory checks can still be performed.
     */
    void stopMemoryTracking();
    
    // Database performance
    /**
     * @brief Records a database query performance metric
     * @param query SQL query text
     * @param duration Query execution time in nanoseconds
     * 
     * Records performance data for a database query.
     * Used for database performance analysis and optimization.
     */
    void recordDatabaseQuery(const QString &query, qint64 duration);
    
    /**
     * @brief Gets database performance metrics
     * @return Aggregated database performance data
     * 
     * Returns comprehensive database performance statistics
     * including query counts, timing, and performance analysis.
     */
    DatabaseMetrics getDatabaseMetrics() const;
    
    // Performance analysis
    /**
     * @brief Gets performance summary for all categories
     * @return List of performance summaries
     * 
     * Returns aggregated performance statistics for all
     * monitored categories and metrics.
     */
    QList<PerformanceSummary> getPerformanceSummary() const;
    
    /**
     * @brief Gets performance metrics for a category
     * @param category Category name (empty for all categories)
     * @return List of performance metrics
     * 
     * Returns detailed performance metrics for the specified category.
     * If category is empty, returns metrics for all categories.
     */
    QList<PerformanceMetric> getMetrics(const QString &category = QString()) const;
    
    /**
     * @brief Gets performance summary for a specific category
     * @param category Category name
     * @return Performance summary for the category
     * 
     * Returns aggregated performance statistics for the specified category.
     */
    PerformanceSummary getCategorySummary(const QString &category) const;
    
    // Optimization
    /**
     * @brief Optimizes database performance
     * 
     * Applies database-specific optimizations based on collected
     * performance metrics and best practices.
     */
    void optimizeDatabase();
    
    /**
     * @brief Optimizes memory usage
     * 
     * Applies memory-specific optimizations to reduce memory usage
     * and improve overall system performance.
     */
    void optimizeMemory();
    
    /**
     * @brief Sets the optimization level
     * @param level Optimization level to apply
     * 
     * Sets the aggressiveness level for performance optimizations.
     * Higher levels may provide better performance but use more resources.
     */
    void setOptimizationLevel(OptimizationLevel level);
    
    /**
     * @brief Applies all configured optimizations
     * 
     * Applies all enabled optimizations based on current settings
     * and collected performance data.
     */
    void applyOptimizations();
    
    // Reporting and export
    /**
     * @brief Generates a performance report
     * @return Formatted performance report string
     * 
     * Generates a comprehensive performance report including
     * metrics, summaries, and optimization recommendations.
     */
    QString generatePerformanceReport() const;
    
    /**
     * @brief Exports performance data to file
     * @param filePath Path where to save the performance data
     * 
     * Exports all collected performance data to a file for
     * external analysis or archiving.
     */
    void exportPerformanceData(const QString &filePath) const;
    
    /**
     * @brief Clears all collected performance metrics
     * 
     * Removes all collected performance data and resets
     * the monitoring system to initial state.
     */
    void clearMetrics();
    
    // Configuration
    /**
     * @brief Enables or disables performance monitoring
     * @param enabled Whether monitoring should be enabled
     * 
     * Controls whether performance monitoring is active.
     * When disabled, no metrics are collected.
     */
    void setMonitoringEnabled(bool enabled);
    
    /**
     * @brief Enables or disables memory tracking
     * @param enabled Whether memory tracking should be enabled
     * 
     * Controls whether automatic memory usage monitoring is active.
     */
    void setMemoryTrackingEnabled(bool enabled);
    
    /**
     * @brief Enables or disables database tracking
     * @param enabled Whether database tracking should be enabled
     * 
     * Controls whether database query performance monitoring is active.
     */
    void setDatabaseTrackingEnabled(bool enabled);
    
    /**
     * @brief Enables or disables automatic optimization
     * @param enabled Whether automatic optimization should be enabled
     * 
     * Controls whether automatic performance optimization is active.
     */
    void setAutoOptimizationEnabled(bool enabled);
    
    // Getters
    /**
     * @brief Checks if monitoring is enabled
     * @return true if monitoring is enabled, false otherwise
     * 
     * Returns the current monitoring enabled state.
     */
    bool isMonitoringEnabled() const { return m_monitoringEnabled; }
    
    /**
     * @brief Checks if memory tracking is enabled
     * @return true if memory tracking is enabled, false otherwise
     * 
     * Returns the current memory tracking enabled state.
     */
    bool isMemoryTrackingEnabled() const { return m_memoryTrackingEnabled; }
    
    /**
     * @brief Checks if database tracking is enabled
     * @return true if database tracking is enabled, false otherwise
     * 
     * Returns the current database tracking enabled state.
     */
    bool isDatabaseTrackingEnabled() const { return m_databaseTrackingEnabled; }
    
    /**
     * @brief Checks if automatic optimization is enabled
     * @return true if automatic optimization is enabled, false otherwise
     * 
     * Returns the current automatic optimization enabled state.
     */
    bool isAutoOptimizationEnabled() const { return m_autoOptimizationEnabled; }
    
    /**
     * @brief Gets the current optimization level
     * @return Current optimization level
     * 
     * Returns the currently configured optimization level.
     */
    OptimizationLevel getOptimizationLevel() const { return m_optimizationLevel; }

signals:
    /**
     * @brief Emitted when a performance warning occurs
     * @param message Warning message
     * 
     * Signal emitted when performance metrics exceed warning thresholds.
     * Used for performance monitoring and alerting.
     */
    void performanceWarning(const QString &message);
    
    /**
     * @brief Emitted when optimization completes
     * @param details Optimization details and results
     * 
     * Signal emitted when performance optimization completes.
     * Used for optimization status updates.
     */
    void optimizationCompleted(const QString &details);
    
    /**
     * @brief Emitted when memory usage is high
     * @param usagePercent Current memory usage percentage
     * 
     * Signal emitted when memory usage exceeds warning thresholds.
     * Used for memory monitoring and alerting.
     */
    void memoryWarning(double usagePercent);
    
    /**
     * @brief Emitted when a slow database query is detected
     * @param query Slow query text
     * @param duration Query duration in nanoseconds
     * 
     * Signal emitted when a database query takes longer than expected.
     * Used for database performance monitoring.
     */
    void databaseSlowQuery(const QString &query, qint64 duration);

private slots:
    /**
     * @brief Handles periodic memory checks
     * 
     * Slot for periodic memory usage monitoring.
     * Checks memory usage and emits warnings if necessary.
     */
    void onMemoryCheck();
    
    /**
     * @brief Handles automatic optimization
     * 
     * Slot for automatic performance optimization.
     * Applies optimizations based on collected metrics.
     */
    void onAutoOptimization();
    
    /**
     * @brief Handles performance analysis
     * 
     * Slot for periodic performance analysis.
     * Analyzes collected metrics and generates reports.
     */
    void onPerformanceAnalysis();

private:
    // Performance tracking methods
    /**
     * @brief Updates performance summary with new metric
     * @param metric Performance metric to add
     * 
     * Updates the performance summary for a category with new metric data.
     * Maintains aggregated statistics for performance analysis.
     */
    void updateSummary(const PerformanceMetric &metric);
    
    /**
     * @brief Checks current memory usage
     * 
     * Checks current memory usage and updates memory tracking data.
     * Emits warnings if memory usage is high.
     */
    void checkMemoryUsage();
    
    /**
     * @brief Checks database performance
     * 
     * Analyzes database performance metrics and identifies
     * potential performance issues or optimization opportunities.
     */
    void checkDatabasePerformance();
    
    // Optimization methods
    /**
     * @brief Performs memory optimization
     * 
     * Applies memory-specific optimizations to reduce memory usage
     * and improve system performance.
     */
    void performMemoryOptimization();
    
    /**
     * @brief Performs database optimization
     * 
     * Applies database-specific optimizations based on collected
     * query performance data.
     */
    void performDatabaseOptimization();
    
    /**
     * @brief Performs general system optimization
     * 
     * Applies general system optimizations to improve overall
     * application performance.
     */
    void performGeneralOptimization();
    
    // Utility methods
    /**
     * @brief Formats a duration for display
     * @param duration Duration in nanoseconds
     * @return Formatted duration string
     * 
     * Converts a duration in nanoseconds to a human-readable format
     * with appropriate units (ns, μs, ms, s).
     */
    QString formatDuration(qint64 duration) const;
    
    /**
     * @brief Formats a memory size for display
     * @param bytes Memory size in bytes
     * @return Formatted memory size string
     * 
     * Converts a memory size in bytes to a human-readable format
     * with appropriate units (B, KB, MB, GB).
     */
    QString formatMemorySize(qint64 bytes) const;
    
    // Member variables
    QMap<QString, QElapsedTimer> m_activeTimers;    ///< Active timer objects
    QMap<QString, QString> m_timerCategories;       ///< Timer name to category mapping
    QList<PerformanceMetric> m_metrics;             ///< Collected performance metrics
    QMap<QString, PerformanceSummary> m_summaries;  ///< Performance summaries by category
    DatabaseMetrics m_databaseMetrics;              ///< Database performance metrics
    QList<MemoryInfo> m_memoryHistory;              ///< Memory usage history
    QScopedPointer<QTimer> m_memoryTimer;           ///< Memory tracking timer
    QScopedPointer<QTimer> m_optimizationTimer;     ///< Optimization timer
    QScopedPointer<QTimer> m_analysisTimer;         ///< Analysis timer
    mutable QMutex m_mutex;                         ///< Thread safety mutex
    
    // Configuration
    bool m_monitoringEnabled;                       ///< Whether monitoring is enabled
    bool m_memoryTrackingEnabled;                   ///< Whether memory tracking is enabled
    bool m_databaseTrackingEnabled;                 ///< Whether database tracking is enabled
    bool m_autoOptimizationEnabled;                 ///< Whether auto-optimization is enabled
    OptimizationLevel m_optimizationLevel;          ///< Current optimization level
    
    // Timing intervals
    int m_memoryCheckInterval;                      ///< Memory check interval in milliseconds
    int m_autoOptimizationInterval;                 ///< Auto-optimization interval in milliseconds
    
    // Thresholds and tracking
    double m_memoryWarningThreshold;                ///< Memory warning threshold percentage
    qint64 m_slowQueryThreshold;                    ///< Slow query threshold in milliseconds
    qint64 m_performanceWarningThreshold;           ///< Performance warning threshold
    qint64 m_peakMemory;                           ///< Peak memory usage recorded
    
    // Utility methods
    double calculateMemoryUsage() const;            ///< Calculate current memory usage percentage
    qint64 getCurrentMemoryUsage() const;           ///< Get current memory usage in bytes
};

/**
 * @brief RAII performance timer class
 * 
 * This class provides automatic performance timing using RAII principles.
 * Timing starts when the object is constructed and ends when it's destroyed.
 * Useful for timing function execution and code blocks.
 */
class PerformanceTimer
{
public:
    /**
     * @brief Constructs a performance timer
     * @param name Timer name/identifier
     * @param category Timer category (default: "General")
     * @param monitor Performance monitor instance (default: nullptr)
     * 
     * Creates a performance timer that automatically starts timing.
     * The timer will be recorded when the object is destroyed.
     */
    PerformanceTimer(const QString &name, const QString &category = "General", PerformanceMonitor *monitor = nullptr);
    
    /**
     * @brief Destructor
     * 
     * Automatically ends timing and records the metric with the performance monitor.
     */
    ~PerformanceTimer();

private:
    QString m_name;                    ///< Timer name
    QString m_category;                ///< Timer category
    PerformanceMonitor *m_monitor;     ///< Performance monitor instance
    QElapsedTimer m_timer;             ///< Internal timer
};

#endif // PERFORMANCE_MONITOR_H 