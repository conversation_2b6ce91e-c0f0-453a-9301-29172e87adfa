# 代码规范

## 组件标准

所有UI组件应遵循Qt的信号与槽机制进行解耦，并采用统一的命名约定。

## 命名约定

| 元素类型 | 命名规范 | 示例 |
|:---|:---|:---|
| 类名 | PascalCase | `CompetitionModel` |
| 文件名 | snake_case | `competition_model.h` |
| 函数名 | camelCase | `updateScore()` |
| 成员变量 | m_camelCase | `m_currentHeight` |
| 信号名 | camelCase + Signal | `dataChangedSignal()` |
| 槽函数名 | camelCase + Slot | `onDataUpdatedSlot()` |

## 关键开发规则

### Qt特定规则
- **信号槽连接**: 优先使用新式语法 `connect(sender, &Class::signal, receiver, &Class::slot)`
- **内存管理**: 使用Qt的父子对象系统，避免手动delete
- **字符串处理**: 统一使用QString，避免std::string与QString混用
- **国际化**: 所有用户可见字符串必须使用tr()包装

### 数据库操作规则
- **SQL查询**: 必须使用参数化查询防止SQL注入
- **事务管理**: 数据库写操作必须在事务中进行
- **错误处理**: 所有数据库操作必须检查返回值和错误状态

### 网络通信规则
- **API调用**: 必须通过ApiClient单例类，不允许直接网络调用
- **错误处理**: 网络请求必须包含超时和重试机制
- **数据验证**: 服务器响应数据必须验证格式和完整性

### 配置管理规则
- **配置访问**: 通过QSettings统一管理，不允许硬编码配置
- **敏感信息**: 不允许在代码中硬编码密码或密钥
- **路径处理**: 使用QDir和QStandardPaths处理文件路径

## 测试要求

### 单元测试规范
- 所有public方法必须有对应的单元测试
- 测试文件命名: `test_<类名>.cpp`
- 使用Qt Test框架的QCOMPARE和QVERIFY宏
- Mock外部依赖，确保测试隔离性

### 集成测试规范
- 数据库操作使用内存SQLite数据库
- 网络操作使用模拟服务器或离线模式
- 测试数据清理必须在每个测试后执行

## 项目结构规范

### 头文件组织
```cpp
// 1. 系统包含
#include <QtWidgets>
#include <QtSql>

// 2. 第三方库包含
// (无)

// 3. 项目内部包含
#include "models/competition_model.h"
#include "utils/database_manager.h"
```

### 类文件结构
```cpp
class ExampleClass : public QObject
{
    Q_OBJECT

public:
    explicit ExampleClass(QObject *parent = nullptr);
    ~ExampleClass();

signals:
    void dataChanged();

public slots:
    void updateData();

private slots:
    void onInternalUpdate();

private:
    // 成员变量
    QString m_data;
    QTimer *m_timer;
    
    // 私有方法
    void initialize();
};
```

## 错误处理标准

### 异常使用原则
- Qt应用中避免使用C++异常
- 使用返回值和错误状态进行错误处理
- 关键操作必须有明确的错误检查和用户反馈

### 日志记录规范
- 使用Qt的qDebug(), qWarning(), qCritical()
- 生产环境必须关闭调试日志
- 错误信息必须包含足够的上下文信息