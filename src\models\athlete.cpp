#include "athlete.h"
#include <QDate>

Athlete::Athlete(QObject *parent)
    : QObject(parent)
    , m_id(-1)
    , m_startNumber(0)
    , m_personalBest(0)
    , m_seasonBest(0)
{
}

void Athlete::setId(int id)
{
    if (m_id != id) {
        m_id = id;
        emit idChanged();
    }
}

void Athlete::setFirstName(const QString &firstName)
{
    if (m_firstName != firstName) {
        m_firstName = firstName;
        emit firstNameChanged();
    }
}

void Athlete::setLastName(const QString &lastName)
{
    if (m_lastName != lastName) {
        m_lastName = lastName;
        emit lastNameChanged();
    }
}

void Athlete::setCountry(const QString &country)
{
    if (m_country != country) {
        m_country = country;
        emit countryChanged();
    }
}

void Athlete::setClub(const QString &club)
{
    if (m_club != club) {
        m_club = club;
        emit clubChanged();
    }
}

void Athlete::setStartNumber(int startNumber)
{
    if (m_startNumber != startNumber) {
        m_startNumber = startNumber;
        emit startNumberChanged();
    }
}

void Athlete::setPersonalBest(int personalBest)
{
    if (m_personalBest != personalBest) {
        m_personalBest = personalBest;
        emit personalBestChanged();
    }
}

void Athlete::setSeasonBest(int seasonBest)
{
    if (m_seasonBest != seasonBest) {
        m_seasonBest = seasonBest;
        emit seasonBestChanged();
    }
}

void Athlete::setDateOfBirth(const QDateTime &dateOfBirth)
{
    if (m_dateOfBirth != dateOfBirth) {
        m_dateOfBirth = dateOfBirth;
        emit dateOfBirthChanged();
    }
}

bool Athlete::isValid() const
{
    return !m_firstName.isEmpty() && !m_lastName.isEmpty() && m_startNumber > 0;
}

int Athlete::age() const
{
    if (!m_dateOfBirth.isValid()) {
        return -1;
    }
    
    QDate currentDate = QDate::currentDate();
    QDate birthDate = m_dateOfBirth.date();
    
    int age = currentDate.year() - birthDate.year();
    if (currentDate.month() < birthDate.month() || 
        (currentDate.month() == birthDate.month() && currentDate.day() < birthDate.day())) {
        age--;
    }
    
    return age;
}

QString Athlete::displayName() const
{
    if (m_startNumber > 0) {
        return QString("#%1 %2").arg(m_startNumber).arg(fullName());
    }
    return fullName();
}