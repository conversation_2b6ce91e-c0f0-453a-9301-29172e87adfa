#!/usr/bin/env powershell

Write-Host "Debug Test Runner"
Write-Host "================="

$tests = @(
    "test_database_manager.exe",
    "test_config_manager.exe", 
    "test_api_client.exe"
)

$testDir = "build\bin\Release"

foreach ($test in $tests) {
    $testPath = Join-Path $testDir $test
    if (Test-Path $testPath) {
        Write-Host "Running $test..."
        
        # Run with timeout
        $job = Start-Job -ScriptBlock {
            param($path)
            & $path
        } -ArgumentList $testPath
        
        Wait-Job $job -Timeout 10
        $result = Receive-Job $job
        $exitCode = $job.State
        
        Remove-Job $job -Force
        
        Write-Host "  Exit State: $exitCode"
        if ($result) {
            Write-Host "  Output: $result"
        }
        Write-Host ""
    } else {
        Write-Host "Test $test not found at $testPath"
    }
}

Write-Host "Debug test run complete."