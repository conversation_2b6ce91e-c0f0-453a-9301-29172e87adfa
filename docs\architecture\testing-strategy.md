# 测试要求与策略

## 测试概述

必须包含针对`RulesEngine`和`DatabaseManager`的高覆盖率**单元测试**，以及验证API同步逻辑的**集成测试**。

## 测试框架与工具

### 核心测试框架
- **单元测试**: Qt Test (与Qt 6.9.1捆绑)
- **Mock框架**: 自定义Mock类 + Qt的QSignalSpy
- **测试运行器**: Qt Test框架内置运行器

### 测试工具配置
```cpp
// 测试基类
class BaseTest : public QObject
{
    Q_OBJECT

protected:
    virtual void initTestCase() {}    // 整个测试类开始前
    virtual void init() {}            // 每个测试方法开始前
    virtual void cleanup() {}         // 每个测试方法结束后
    virtual void cleanupTestCase() {} // 整个测试类结束后
};
```

## 单元测试策略

### RulesEngine 测试

```cpp
class TestRulesEngine : public BaseTest
{
    Q_OBJECT

private slots:
    void initTestCase();
    void init();
    void cleanup();
    
    // 排名算法测试
    void testRankingCalculation();
    void testRankingWithEqualHeights();
    void testRankingWithRetiredAthletes();
    
    // 试跳规则测试
    void testAttemptValidation();
    void testThreeFailureElimination();
    void testPassAttemptHandling();
    
    // 比赛状态测试
    void testCompetitionFinished();
    void testNextAthleteCalculation();

private:
    RulesEngine *m_rulesEngine;
    QList<Athlete*> m_testAthletes;
    Competition *m_testCompetition;
};
```

### DatabaseManager 测试

```cpp
class TestDatabaseManager : public BaseTest
{
    Q_OBJECT

private slots:
    void initTestCase();
    void init();
    void cleanup();
    void cleanupTestCase();
    
    // 基础数据库操作
    void testDatabaseConnection();
    void testTableCreation();
    void testSchemaVersion();
    
    // CRUD操作测试
    void testCreateCompetition();
    void testCreateAthlete();
    void testRecordAttempt();
    void testUpdateAthleteStatus();
    
    // 查询测试
    void testGetCurrentRanking();
    void testGetAttemptHistory();
    void testSyncQueueOperations();
    
    // 数据完整性测试
    void testForeignKeyConstraints();
    void testUniqueConstraints();
    void testTransactionRollback();

private:
    QString m_testDbPath;
    DatabaseManager *m_dbManager;
};
```

### CompetitionModel 测试

```cpp
class TestCompetitionModel : public BaseTest
{
    Q_OBJECT

private slots:
    void initTestCase();
    void init();
    void cleanup();
    
    // Model/View架构测试
    void testRowCount();
    void testColumnCount();
    void testDataRetrieval();
    void testHeaderData();
    
    // 数据更新测试
    void testLoadCompetition();
    void testRecordAttempt();
    void testModelSignals();
    
    // 性能测试
    void testLargeDatasetPerformance();

private:
    CompetitionModel *m_model;
    QSignalSpy *m_dataChangedSpy;
    QSignalSpy *m_rankingChangedSpy;
};
```

## 集成测试策略

### API集成测试

```cpp
class TestApiIntegration : public BaseTest
{
    Q_OBJECT

private slots:
    void initTestCase();
    void init();
    void cleanup();
    
    // 网络连接测试
    void testNetworkDetection();
    void testConnectionRetry();
    void testOfflineMode();
    
    // API同步测试
    void testDataSynchronization();
    void testSyncQueueProcessing();
    void testConflictResolution();
    
    // 错误处理测试
    void testNetworkTimeout();
    void testServerError();
    void testAuthenticationFailure();

private:
    MockApiServer *m_mockServer;
    ApiClient *m_apiClient;
    SyncManager *m_syncManager;
};
```

### UI集成测试

```cpp
class TestUIIntegration : public BaseTest
{
    Q_OBJECT

private slots:
    void initTestCase();
    void init();
    void cleanup();
    
    // 用户交互测试
    void testKeyboardShortcuts();
    void testTableInteraction();
    void testViewSwitching();
    
    // 数据流测试
    void testModelViewBinding();
    void testRealTimeUpdates();
    void testExportFunctionality();

private:
    MainWindow *m_mainWindow;
    QTestEventList m_eventList;
};
```

## 测试数据管理

### 测试数据工厂

```cpp
class TestDataFactory
{
public:
    // 创建测试比赛
    static Competition createTestCompetition(
        const QString &name = "Test Competition",
        const QDateTime &date = QDateTime::currentDateTime()
    );
    
    // 创建测试运动员
    static Athlete createTestAthlete(
        const QString &name = "Test Athlete",
        int number = 1,
        const QString &team = "Test Team"
    );
    
    // 创建测试试跳记录
    static AttemptRecord createTestAttempt(
        int athleteId,
        int height,
        AttemptRecord::AttemptResult result = AttemptRecord::Success
    );
    
    // 创建完整测试场景
    static QList<AttemptRecord> createCompetitionScenario();
};
```

### 测试数据库设置

```cpp
class TestDatabaseSetup
{
public:
    static QString createTempDatabase();
    static void populateTestData(const QString &dbPath);
    static void cleanupDatabase(const QString &dbPath);
    
    // 预定义测试场景
    static void setupBasicCompetition(const QString &dbPath);
    static void setupComplexRankingScenario(const QString &dbPath);
    static void setupSyncQueueData(const QString &dbPath);
};
```

## Mock对象设计

### MockApiClient

```cpp
class MockApiClient : public ApiClient
{
    Q_OBJECT

public:
    explicit MockApiClient(QObject *parent = nullptr);
    
    // 模拟网络状态
    void setNetworkStatus(bool isOnline);
    void simulateNetworkError();
    void simulateServerError();
    
    // 模拟API响应
    void setMockResponse(const QString &endpoint, const QJsonObject &response);
    void setResponseDelay(int msec);
    
    // 验证调用
    int getCallCount(const QString &endpoint) const;
    QJsonObject getLastRequestData(const QString &endpoint) const;

private:
    QHash<QString, QJsonObject> m_mockResponses;
    QHash<QString, int> m_callCounts;
    QHash<QString, QJsonObject> m_lastRequests;
    bool m_isOnline;
    int m_responseDelay;
};
```

### MockDatabaseManager

```cpp
class MockDatabaseManager : public DatabaseManager
{
    Q_OBJECT

public:
    explicit MockDatabaseManager(QObject *parent = nullptr);
    
    // 模拟数据库状态
    void setConnectionStatus(bool connected);
    void simulateDatabaseError();
    
    // 预设数据
    void setMockData(const QList<Competition> &competitions);
    void setMockAthletes(const QList<Athlete> &athletes);
    
    // 验证操作
    int getOperationCount(const QString &operation) const;
    QVariant getLastOperationData(const QString &operation) const;

private:
    QList<Competition> m_mockCompetitions;
    QList<Athlete> m_mockAthletes;
    QList<AttemptRecord> m_mockAttempts;
    QHash<QString, int> m_operationCounts;
};
```

## 测试覆盖率要求

### 代码覆盖率目标
- **核心业务逻辑**: 95%以上
- **数据库操作**: 90%以上
- **网络通信**: 85%以上
- **UI交互**: 70%以上

### 覆盖率测试工具
```bash
# 使用gcov生成覆盖率报告
cmake -DCOVERAGE=ON ..
make
make test
gcov *.gcno
lcov --capture --directory . --output-file coverage.info
genhtml coverage.info --output-directory coverage_report
```

## 性能测试

### 基准测试

```cpp
class BenchmarkTests : public BaseTest
{
    Q_OBJECT

private slots:
    void benchmarkRankingCalculation();
    void benchmarkDatabaseQueries();
    void benchmarkLargeDatasetHandling();

private:
    void setupLargeDataset(int athleteCount);
};

void BenchmarkTests::benchmarkRankingCalculation()
{
    QBENCHMARK {
        m_rulesEngine->calculateRanking(m_testAthletes);
    }
}
```

## 测试自动化

### 持续集成配置

```yaml
# .github/workflows/test.yml
name: Run Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Install Qt
        run: sudo apt-get install qt6-base-dev
      - name: Build and Test
        run: |
          mkdir build && cd build
          cmake ..
          make
          make test
          
  coverage:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Generate Coverage Report
        run: |
          mkdir build && cd build
          cmake -DCOVERAGE=ON ..
          make
          make test
          make coverage
```

## 测试最佳实践

### 测试命名规范
- 测试类: `Test{ClassName}`
- 测试方法: `test{FunctionName}_{Scenario}`
- 数据驱动测试: `test{Function}_{Condition}_data()`

### 断言指导原则
- 使用QCOMPARE而不是QVERIFY进行相等性测试
- 使用QVERIFY验证布尔条件
- 使用自定义比较器处理复杂对象

### 测试隔离
- 每个测试方法独立运行
- 使用setUp/tearDown确保清理
- 避免测试之间的依赖关系

### 错误场景测试
- 测试所有错误路径
- 验证错误消息的准确性
- 确保异常情况下的资源清理