# 🏃‍♂️ High Jump Competition Management System

A professional desktop application for managing high jump competitions, built with Qt6 and C++17. This system provides comprehensive tools for tracking athletes, recording jump attempts, and generating competition reports.

## ✨ Features

### 🏆 Competition Management
- **Create and manage competitions** with custom names, dates, and venues
- **Real-time competition status** tracking (Not Started, In Progress, Paused, Completed)
- **Dynamic height progression** with automatic advancement rules
- **Athlete elimination tracking** based on World Athletics rules

### 👥 Athlete Management
- **Add, edit, and remove athletes** during competitions
- **Comprehensive athlete profiles** with name, country, personal best, and notes
- **Real-time athlete statistics** including current height, attempts, and status
- **Quick athlete navigation** with next/previous selection

### 📊 Jump Recording
- **Three attempt types**: Pass, Fail, Skip
- **Automatic attempt counting** per height
- **Elimination tracking** after 3 consecutive failures
- **Height completion detection** when all athletes are eliminated or pass
- **Real-time statistics** and progress indicators

### 📈 Results and Reporting
- **Live results table** with current standings
- **CSV export** for competition results
- **HTML printing** with professional formatting
- **Detailed jump history** for each athlete
- **Competition summary** with final rankings

### 🎛️ User Interface
- **Intuitive three-panel layout**: Athletes, Competition, Results
- **Comprehensive menu system** with all major functions
- **Toolbar shortcuts** for common actions
- **Status bar** with real-time information
- **Keyboard shortcuts** for quick navigation

### 🛠️ Advanced Features
- **Custom height setting** with input validation
- **Competition pause/resume** functionality
- **Automatic data persistence** with SQLite database
- **Configuration management** for application settings
- **Help system** with detailed user guide and rules

## 🏗️ Technical Architecture

### Core Components
- **Models**: `Athlete`, `Competition`, `JumpAttempt` - Data structures
- **Core**: `JumpManager` - Business logic and competition rules
- **UI**: `MainWindow`, `AthleteDialog` - User interface components
- **Persistence**: `DatabaseManager` - Data storage and retrieval
- **Utils**: `ConfigManager` - Application configuration
- **API**: `ApiClient` - External service integration

### Design Patterns
- **Singleton Pattern**: Core managers for global access
- **Model-View-Controller**: Separation of data, logic, and presentation
- **Observer Pattern**: Signal-slot connections for UI updates
- **Factory Pattern**: Dynamic object creation for athletes and competitions

### Technologies
- **Qt6**: Modern C++ framework for cross-platform GUI
- **C++17**: Latest C++ standard with modern features
- **CMake**: Cross-platform build system
- **SQLite**: Lightweight database for data persistence
- **HTML/CSS**: Rich text formatting for reports

## 🚀 Quick Start

### Prerequisites
- Qt 6.9.1 or later
- CMake 3.20 or later
- C++17 compatible compiler

### Windows Build
```powershell
# Set Qt environment
$env:CMAKE_PREFIX_PATH = "C:\Qt\6.9.1\msvc2019_64"

# Build using provided script
.\build.ps1
```

### macOS Build
```bash
# Set Qt environment
export CMAKE_PREFIX_PATH="/opt/homebrew/opt/qt@6"

# Configure and build
cmake -B build -S .
cmake --build build --config Release
```

### Linux Build
```bash
# Install dependencies
sudo apt install build-essential cmake qt6-base-dev

# Configure and build
cmake -B build -S .
cmake --build build --config Release
```

## 📖 User Guide

### Starting a Competition
1. Launch the application
2. Click "New Competition" or use File → New
3. Enter competition details (name, date, venue)
4. Add athletes using the "Add Athlete" button
5. Set initial height using the height spin box
6. Click "Start Competition" to begin

### Recording Jumps
1. Select an athlete from the left panel
2. Use the Pass/Fail/Skip buttons to record attempts
3. Monitor attempt counts and elimination status
4. Advance height when all athletes complete current height
5. Continue until competition ends

### Managing Results
1. View live results in the right panel
2. Export results to CSV using File → Export Results
3. Print results using File → Print Results
4. Save competition data using File → Save

### Athlete Management
1. Add athletes during competition using "Add Athlete"
2. Edit athlete details by selecting and clicking "Edit Athlete"
3. Remove athletes using "Remove Athlete" (if not eliminated)
4. Navigate between athletes using "Next/Previous Athlete"

## 🏅 Competition Rules

The system implements World Athletics high jump rules:

### Basic Rules
- **Height Progression**: Minimum 3cm increments, can be adjusted
- **Attempts**: Maximum 3 attempts per height
- **Elimination**: Athlete eliminated after 3 consecutive failures
- **Advancement**: Height completed when all athletes eliminated or pass
- **Tie Breaking**: Based on failed attempts at highest cleared height

### Competition Flow
1. **Initial Height**: Set by competition organizers
2. **Height Progression**: Automatic advancement after completion
3. **Athlete Rotation**: Systematic order through all active athletes
4. **Elimination Tracking**: Automatic removal of eliminated athletes
5. **Final Ranking**: Based on highest cleared height and attempts

## 🔧 Configuration

### Application Settings
- **Database Path**: SQLite database location
- **Default Heights**: Predefined height increments
- **UI Preferences**: Window size, panel layouts
- **Export Settings**: Default file formats and locations

### Competition Settings
- **Height Increments**: Minimum and maximum height changes
- **Time Limits**: Optional time constraints per attempt
- **Scoring Rules**: Custom scoring systems
- **Export Formats**: Available report formats

## 🧪 Testing

### Manual Testing Checklist
- [ ] Application launches without errors
- [ ] New competition creation works
- [ ] Athlete addition/editing functions
- [ ] Jump recording (Pass/Fail/Skip)
- [ ] Height advancement logic
- [ ] Athlete elimination tracking
- [ ] Results table updates
- [ ] CSV export functionality
- [ ] HTML printing
- [ ] Competition pause/resume
- [ ] Data persistence (save/load)

### Automated Testing
```bash
# Run unit tests
cmake --build build --target test

# Run specific test suite
ctest --test-dir build -R "JumpManager"
```

## 📦 Deployment

### Windows
```powershell
# Deploy with Qt dependencies
windeployqt build/bin/Release/high-jump-scorer.exe

# Create installer (requires NSIS or similar)
makensis installer.nsi
```

### macOS
```bash
# Create app bundle
macdeployqt build/bin/high-jump-scorer.app

# Create DMG
hdiutil create -volname "High Jump Scorer" -srcfolder build/bin/high-jump-scorer.app high-jump-scorer.dmg
```

### Linux
```bash
# Create AppImage
appimagetool build/bin/high-jump-scorer.AppDir high-jump-scorer.AppImage
```

## 🤝 Contributing

### Development Setup
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

### Code Style
- Follow Qt coding conventions
- Use meaningful variable and function names
- Add comments for complex logic
- Ensure proper error handling
- Write unit tests for new features

### Testing Guidelines
- Test all user interactions
- Verify data persistence
- Check edge cases and error conditions
- Ensure cross-platform compatibility
- Validate competition rule compliance

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **World Athletics** for competition rules and regulations
- **Qt Company** for the excellent Qt framework
- **Open source community** for various tools and libraries

## 📞 Support

For issues and questions:
1. Check the [BUILD_GUIDE.md](BUILD_GUIDE.md) for build problems
2. Review the user guide for usage questions
3. Check existing issues in the repository
4. Create a new issue with detailed information

## 🎯 Roadmap

### Planned Features
- [ ] **Multi-language support** (Internationalization)
- [ ] **Network synchronization** for multi-device competitions
- [ ] **Advanced statistics** and analytics
- [ ] **Video integration** for jump review
- [ ] **Mobile companion app** for field use
- [ ] **Cloud backup** and synchronization
- [ ] **Custom competition formats** (indoor, outdoor, etc.)
- [ ] **Integration with timing systems**
- [ ] **Advanced reporting** with charts and graphs
- [ ] **User management** and access control

### Performance Improvements
- [ ] **Database optimization** for large competitions
- [ ] **Memory management** improvements
- [ ] **UI responsiveness** enhancements
- [ ] **Startup time** optimization
- [ ] **Export performance** improvements

---

**High Jump Competition Management System** - Professional tools for professional competitions. 