﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>ARM64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{8AF23479-573B-3E2D-BE2D-EF9BCFD68F7A}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>test_e2e_workflow</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\PROJECT\HighJump\build\bin\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">test_e2e_workflow.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">test_e2e_workflow</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\PROJECT\HighJump\build\bin\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">test_e2e_workflow.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">test_e2e_workflow</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\PROJECT\HighJump\build\bin\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">test_e2e_workflow.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">test_e2e_workflow</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\PROJECT\HighJump\build\bin\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">test_e2e_workflow.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">test_e2e_workflow</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\PROJECT\HighJump\build\tests\test_e2e_workflow_autogen\include_Debug;C:\PROJECT\HighJump\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Qt/install-x64/include/QtCore" /external:I "C:/Qt/install-x64/include" /external:I "C:/Qt/install-x64/mkspecs/win32-arm64-msvc" /external:I "C:/Qt/install-x64/include/QtWidgets" /external:I "C:/Qt/install-x64/include/QtGui" /external:I "C:/Qt/install-x64/include/QtSql" /external:I "C:/Qt/install-x64/include/QtNetwork" /external:I "C:/Qt/install-x64/include/QtTest" -Zc:__cplusplus -utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <ConformanceMode>true</ConformanceMode>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;QT_CORE_LIB;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;QT_SQL_LIB;QT_NETWORK_LIB;QT_TESTLIB_LIB;QT_TESTCASE_BUILDDIR="C:/PROJECT/HighJump/build/tests";QT_TESTCASE_SOURCEDIR="C:/PROJECT/HighJump/tests";CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;QT_CORE_LIB;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;QT_SQL_LIB;QT_NETWORK_LIB;QT_TESTLIB_LIB;QT_TESTCASE_BUILDDIR=\"C:/PROJECT/HighJump/build/tests\";QT_TESTCASE_SOURCEDIR=\"C:/PROJECT/HighJump/tests\";CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\PROJECT\HighJump\build\tests\test_e2e_workflow_autogen\include_Debug;C:\PROJECT\HighJump\src;C:\Qt\install-x64\include\QtCore;C:\Qt\install-x64\include;C:\Qt\install-x64\mkspecs\win32-arm64-msvc;C:\Qt\install-x64\include\QtWidgets;C:\Qt\install-x64\include\QtGui;C:\Qt\install-x64\include\QtSql;C:\Qt\install-x64\include\QtNetwork;C:\Qt\install-x64\include\QtTest;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\PROJECT\HighJump\build\tests\test_e2e_workflow_autogen\include_Debug;C:\PROJECT\HighJump\src;C:\Qt\install-x64\include\QtCore;C:\Qt\install-x64\include;C:\Qt\install-x64\mkspecs\win32-arm64-msvc;C:\Qt\install-x64\include\QtWidgets;C:\Qt\install-x64\include\QtGui;C:\Qt\install-x64\include\QtSql;C:\Qt\install-x64\include\QtNetwork;C:\Qt\install-x64\include\QtTest;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target test_e2e_workflow</Message>
      <Command>setlocal
cd C:\PROJECT\HighJump\build\tests
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E cmake_autogen C:/PROJECT/HighJump/build/tests/CMakeFiles/test_e2e_workflow_autogen.dir/AutogenInfo.json Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <PostBuildEvent>
      <Message>Deploying Qt libraries and plugins for test_e2e_workflow</Message>
      <Command>setlocal
C:\Qt\install-x64\bin\windeployqt.exe --sql C:/PROJECT/HighJump/build/bin/Debug/test_e2e_workflow.exe
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>C:\Qt\install-x64\lib\Qt6Widgets.lib;C:\Qt\install-x64\lib\Qt6Sql.lib;C:\Qt\install-x64\lib\Qt6Network.lib;C:\Qt\install-x64\lib\Qt6Test.lib;C:\Qt\install-x64\lib\Qt6Gui.lib;d3d11.lib;dxgi.lib;dxguid.lib;d3d12.lib;ws2_32.lib;C:\Qt\install-x64\lib\Qt6Core.lib;mpr.lib;userenv.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/PROJECT/HighJump/build/tests/Debug/test_e2e_workflow.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/PROJECT/HighJump/build/bin/Debug/test_e2e_workflow.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\PROJECT\HighJump\build\tests\test_e2e_workflow_autogen\include_Release;C:\PROJECT\HighJump\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Qt/install-x64/include/QtCore" /external:I "C:/Qt/install-x64/include" /external:I "C:/Qt/install-x64/mkspecs/win32-arm64-msvc" /external:I "C:/Qt/install-x64/include/QtWidgets" /external:I "C:/Qt/install-x64/include/QtGui" /external:I "C:/Qt/install-x64/include/QtSql" /external:I "C:/Qt/install-x64/include/QtNetwork" /external:I "C:/Qt/install-x64/include/QtTest" -Zc:__cplusplus -utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ConformanceMode>true</ConformanceMode>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;QT_SQL_LIB;QT_NETWORK_LIB;QT_TESTLIB_LIB;QT_TESTCASE_BUILDDIR="C:/PROJECT/HighJump/build/tests";QT_TESTCASE_SOURCEDIR="C:/PROJECT/HighJump/tests";CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;QT_SQL_LIB;QT_NETWORK_LIB;QT_TESTLIB_LIB;QT_TESTCASE_BUILDDIR=\"C:/PROJECT/HighJump/build/tests\";QT_TESTCASE_SOURCEDIR=\"C:/PROJECT/HighJump/tests\";CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\PROJECT\HighJump\build\tests\test_e2e_workflow_autogen\include_Release;C:\PROJECT\HighJump\src;C:\Qt\install-x64\include\QtCore;C:\Qt\install-x64\include;C:\Qt\install-x64\mkspecs\win32-arm64-msvc;C:\Qt\install-x64\include\QtWidgets;C:\Qt\install-x64\include\QtGui;C:\Qt\install-x64\include\QtSql;C:\Qt\install-x64\include\QtNetwork;C:\Qt\install-x64\include\QtTest;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\PROJECT\HighJump\build\tests\test_e2e_workflow_autogen\include_Release;C:\PROJECT\HighJump\src;C:\Qt\install-x64\include\QtCore;C:\Qt\install-x64\include;C:\Qt\install-x64\mkspecs\win32-arm64-msvc;C:\Qt\install-x64\include\QtWidgets;C:\Qt\install-x64\include\QtGui;C:\Qt\install-x64\include\QtSql;C:\Qt\install-x64\include\QtNetwork;C:\Qt\install-x64\include\QtTest;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target test_e2e_workflow</Message>
      <Command>setlocal
cd C:\PROJECT\HighJump\build\tests
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E cmake_autogen C:/PROJECT/HighJump/build/tests/CMakeFiles/test_e2e_workflow_autogen.dir/AutogenInfo.json Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <PostBuildEvent>
      <Message>Deploying Qt libraries and plugins for test_e2e_workflow</Message>
      <Command>setlocal
C:\Qt\install-x64\bin\windeployqt.exe --sql C:/PROJECT/HighJump/build/bin/Release/test_e2e_workflow.exe
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>C:\Qt\install-x64\lib\Qt6Widgets.lib;C:\Qt\install-x64\lib\Qt6Sql.lib;C:\Qt\install-x64\lib\Qt6Network.lib;C:\Qt\install-x64\lib\Qt6Test.lib;C:\Qt\install-x64\lib\Qt6Gui.lib;d3d11.lib;dxgi.lib;dxguid.lib;d3d12.lib;ws2_32.lib;C:\Qt\install-x64\lib\Qt6Core.lib;mpr.lib;userenv.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/PROJECT/HighJump/build/tests/Release/test_e2e_workflow.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/PROJECT/HighJump/build/bin/Release/test_e2e_workflow.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\PROJECT\HighJump\build\tests\test_e2e_workflow_autogen\include_MinSizeRel;C:\PROJECT\HighJump\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Qt/install-x64/include/QtCore" /external:I "C:/Qt/install-x64/include" /external:I "C:/Qt/install-x64/mkspecs/win32-arm64-msvc" /external:I "C:/Qt/install-x64/include/QtWidgets" /external:I "C:/Qt/install-x64/include/QtGui" /external:I "C:/Qt/install-x64/include/QtSql" /external:I "C:/Qt/install-x64/include/QtNetwork" /external:I "C:/Qt/install-x64/include/QtTest" -Zc:__cplusplus -utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ConformanceMode>true</ConformanceMode>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;QT_SQL_LIB;QT_NETWORK_LIB;QT_TESTLIB_LIB;QT_TESTCASE_BUILDDIR="C:/PROJECT/HighJump/build/tests";QT_TESTCASE_SOURCEDIR="C:/PROJECT/HighJump/tests";CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;QT_SQL_LIB;QT_NETWORK_LIB;QT_TESTLIB_LIB;QT_TESTCASE_BUILDDIR=\"C:/PROJECT/HighJump/build/tests\";QT_TESTCASE_SOURCEDIR=\"C:/PROJECT/HighJump/tests\";CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\PROJECT\HighJump\build\tests\test_e2e_workflow_autogen\include_MinSizeRel;C:\PROJECT\HighJump\src;C:\Qt\install-x64\include\QtCore;C:\Qt\install-x64\include;C:\Qt\install-x64\mkspecs\win32-arm64-msvc;C:\Qt\install-x64\include\QtWidgets;C:\Qt\install-x64\include\QtGui;C:\Qt\install-x64\include\QtSql;C:\Qt\install-x64\include\QtNetwork;C:\Qt\install-x64\include\QtTest;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\PROJECT\HighJump\build\tests\test_e2e_workflow_autogen\include_MinSizeRel;C:\PROJECT\HighJump\src;C:\Qt\install-x64\include\QtCore;C:\Qt\install-x64\include;C:\Qt\install-x64\mkspecs\win32-arm64-msvc;C:\Qt\install-x64\include\QtWidgets;C:\Qt\install-x64\include\QtGui;C:\Qt\install-x64\include\QtSql;C:\Qt\install-x64\include\QtNetwork;C:\Qt\install-x64\include\QtTest;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target test_e2e_workflow</Message>
      <Command>setlocal
cd C:\PROJECT\HighJump\build\tests
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E cmake_autogen C:/PROJECT/HighJump/build/tests/CMakeFiles/test_e2e_workflow_autogen.dir/AutogenInfo.json MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <PostBuildEvent>
      <Message>Deploying Qt libraries and plugins for test_e2e_workflow</Message>
      <Command>setlocal
C:\Qt\install-x64\bin\windeployqt.exe --sql C:/PROJECT/HighJump/build/bin/MinSizeRel/test_e2e_workflow.exe
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>C:\Qt\install-x64\lib\Qt6Widgets.lib;C:\Qt\install-x64\lib\Qt6Sql.lib;C:\Qt\install-x64\lib\Qt6Network.lib;C:\Qt\install-x64\lib\Qt6Test.lib;C:\Qt\install-x64\lib\Qt6Gui.lib;d3d11.lib;dxgi.lib;dxguid.lib;d3d12.lib;ws2_32.lib;C:\Qt\install-x64\lib\Qt6Core.lib;mpr.lib;userenv.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/PROJECT/HighJump/build/tests/MinSizeRel/test_e2e_workflow.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/PROJECT/HighJump/build/bin/MinSizeRel/test_e2e_workflow.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\PROJECT\HighJump\build\tests\test_e2e_workflow_autogen\include_RelWithDebInfo;C:\PROJECT\HighJump\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Qt/install-x64/include/QtCore" /external:I "C:/Qt/install-x64/include" /external:I "C:/Qt/install-x64/mkspecs/win32-arm64-msvc" /external:I "C:/Qt/install-x64/include/QtWidgets" /external:I "C:/Qt/install-x64/include/QtGui" /external:I "C:/Qt/install-x64/include/QtSql" /external:I "C:/Qt/install-x64/include/QtNetwork" /external:I "C:/Qt/install-x64/include/QtTest" -Zc:__cplusplus -utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ConformanceMode>true</ConformanceMode>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;QT_SQL_LIB;QT_NETWORK_LIB;QT_TESTLIB_LIB;QT_TESTCASE_BUILDDIR="C:/PROJECT/HighJump/build/tests";QT_TESTCASE_SOURCEDIR="C:/PROJECT/HighJump/tests";CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;QT_SQL_LIB;QT_NETWORK_LIB;QT_TESTLIB_LIB;QT_TESTCASE_BUILDDIR=\"C:/PROJECT/HighJump/build/tests\";QT_TESTCASE_SOURCEDIR=\"C:/PROJECT/HighJump/tests\";CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\PROJECT\HighJump\build\tests\test_e2e_workflow_autogen\include_RelWithDebInfo;C:\PROJECT\HighJump\src;C:\Qt\install-x64\include\QtCore;C:\Qt\install-x64\include;C:\Qt\install-x64\mkspecs\win32-arm64-msvc;C:\Qt\install-x64\include\QtWidgets;C:\Qt\install-x64\include\QtGui;C:\Qt\install-x64\include\QtSql;C:\Qt\install-x64\include\QtNetwork;C:\Qt\install-x64\include\QtTest;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\PROJECT\HighJump\build\tests\test_e2e_workflow_autogen\include_RelWithDebInfo;C:\PROJECT\HighJump\src;C:\Qt\install-x64\include\QtCore;C:\Qt\install-x64\include;C:\Qt\install-x64\mkspecs\win32-arm64-msvc;C:\Qt\install-x64\include\QtWidgets;C:\Qt\install-x64\include\QtGui;C:\Qt\install-x64\include\QtSql;C:\Qt\install-x64\include\QtNetwork;C:\Qt\install-x64\include\QtTest;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target test_e2e_workflow</Message>
      <Command>setlocal
cd C:\PROJECT\HighJump\build\tests
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E cmake_autogen C:/PROJECT/HighJump/build/tests/CMakeFiles/test_e2e_workflow_autogen.dir/AutogenInfo.json RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <PostBuildEvent>
      <Message>Deploying Qt libraries and plugins for test_e2e_workflow</Message>
      <Command>setlocal
C:\Qt\install-x64\bin\windeployqt.exe --sql C:/PROJECT/HighJump/build/bin/RelWithDebInfo/test_e2e_workflow.exe
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>C:\Qt\install-x64\lib\Qt6Widgets.lib;C:\Qt\install-x64\lib\Qt6Sql.lib;C:\Qt\install-x64\lib\Qt6Network.lib;C:\Qt\install-x64\lib\Qt6Test.lib;C:\Qt\install-x64\lib\Qt6Gui.lib;d3d11.lib;dxgi.lib;dxguid.lib;d3d12.lib;ws2_32.lib;C:\Qt\install-x64\lib\Qt6Core.lib;mpr.lib;userenv.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/PROJECT/HighJump/build/tests/RelWithDebInfo/test_e2e_workflow.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/PROJECT/HighJump/build/bin/RelWithDebInfo/test_e2e_workflow.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\PROJECT\HighJump\tests\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/PROJECT/HighJump/tests/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/PROJECT/HighJump -BC:/PROJECT/HighJump/build --check-stamp-file C:/PROJECT/HighJump/build/tests/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckCXXCompilerFlag.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6Config.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6Dependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6Targets.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6VersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtFeature.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtFeatureCommon.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtInstallPaths.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicAndroidHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicExternalProjectHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicGitHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicPluginHelpers_v2.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomAttributionHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomCpeHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomDepHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomFileHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomGenerationHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomLicenseHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomOpsHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomPurlHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomPythonHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomQtEntityHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomSystemDepHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicWindowsHelpers.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\PROJECT\HighJump\build\tests\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/PROJECT/HighJump/tests/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/PROJECT/HighJump -BC:/PROJECT/HighJump/build --check-stamp-file C:/PROJECT/HighJump/build/tests/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckCXXCompilerFlag.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6Config.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6Dependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6Targets.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6VersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtFeature.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtFeatureCommon.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtInstallPaths.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicAndroidHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicExternalProjectHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicGitHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicPluginHelpers_v2.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomAttributionHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomCpeHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomDepHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomFileHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomGenerationHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomLicenseHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomOpsHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomPurlHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomPythonHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomQtEntityHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomSystemDepHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicWindowsHelpers.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\PROJECT\HighJump\build\tests\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule C:/PROJECT/HighJump/tests/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/PROJECT/HighJump -BC:/PROJECT/HighJump/build --check-stamp-file C:/PROJECT/HighJump/build/tests/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckCXXCompilerFlag.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6Config.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6Dependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6Targets.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6VersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtFeature.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtFeatureCommon.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtInstallPaths.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicAndroidHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicExternalProjectHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicGitHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicPluginHelpers_v2.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomAttributionHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomCpeHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomDepHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomFileHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomGenerationHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomLicenseHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomOpsHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomPurlHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomPythonHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomQtEntityHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomSystemDepHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicWindowsHelpers.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\PROJECT\HighJump\build\tests\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule C:/PROJECT/HighJump/tests/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/PROJECT/HighJump -BC:/PROJECT/HighJump/build --check-stamp-file C:/PROJECT/HighJump/build/tests/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckCXXCompilerFlag.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6Config.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6Dependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6Targets.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6VersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtFeature.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtFeatureCommon.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtInstallPaths.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicAndroidHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicExternalProjectHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicGitHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicPluginHelpers_v2.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomAttributionHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomCpeHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomDepHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomFileHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomGenerationHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomLicenseHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomOpsHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomPurlHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomPythonHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomQtEntityHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomSystemDepHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicWindowsHelpers.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\PROJECT\HighJump\build\tests\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="C:\PROJECT\HighJump\build\tests\test_e2e_workflow_autogen\mocs_compilation_Debug.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="C:\PROJECT\HighJump\tests\integration\test_e2e_simple.cpp" />
    <ClCompile Include="C:\PROJECT\HighJump\src\utils\config_manager.cpp" />
    <ClCompile Include="C:\PROJECT\HighJump\src\persistence\database_manager.cpp" />
    <ClCompile Include="C:\PROJECT\HighJump\src\api\api_client.cpp" />
    <ClCompile Include="C:\PROJECT\HighJump\build\tests\test_e2e_workflow_autogen\mocs_compilation_Release.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="C:\PROJECT\HighJump\build\tests\test_e2e_workflow_autogen\mocs_compilation_MinSizeRel.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="C:\PROJECT\HighJump\build\tests\test_e2e_workflow_autogen\mocs_compilation_RelWithDebInfo.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
    </ClCompile>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="C:\PROJECT\HighJump\build\ZERO_CHECK.vcxproj">
      <Project>{F9C87542-EA78-3B7C-B54D-2CB91B9FD646}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>