# High Jump Competition Management System - Story 1.1 Validation Script
# This script validates all acceptance criteria for Story 1.1

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "High Jump Competition Management System" -ForegroundColor Cyan
Write-Host "Story 1.1 Validation Script" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

$allCriteriaPassed = $true

# AC 1: 一个空白的、可运行的Qt 6项目已创建
Write-Host "AC 1: 验证Qt 6项目结构..." -ForegroundColor Yellow

# Check CMakeLists.txt
if (Test-Path "CMakeLists.txt") {
    Write-Host "  ✓ CMakeLists.txt exists" -ForegroundColor Green
} else {
    Write-Host "  ✗ CMakeLists.txt missing" -ForegroundColor Red
    $allCriteriaPassed = $false
}

# Check main.cpp
if (Test-Path "src\main.cpp") {
    Write-Host "  ✓ main.cpp exists" -ForegroundColor Green
} else {
    Write-Host "  ✗ main.cpp missing" -ForegroundColor Red
    $allCriteriaPassed = $false
}

# Check directory structure
$requiredDirs = @("src\api", "src\persistence", "src\utils", "src\ui", "src\core", "src\models")
foreach ($dir in $requiredDirs) {
    if (Test-Path $dir) {
        Write-Host "  ✓ $dir exists" -ForegroundColor Green
    } else {
        Write-Host "  ✗ $dir missing" -ForegroundColor Red
        $allCriteriaPassed = $false
    }
}

Write-Host ""

# AC 2: 应用首次启动时，能自动在本地创建一个`competition_data.sqlite`数据库文件及所需的数据表
Write-Host "AC 2: 验证数据库初始化..." -ForegroundColor Yellow

# Check DatabaseManager files
if ((Test-Path "src\persistence\database_manager.h") -and (Test-Path "src\persistence\database_manager.cpp")) {
    Write-Host "  ✓ DatabaseManager files exist" -ForegroundColor Green
} else {
    Write-Host "  ✗ DatabaseManager files missing" -ForegroundColor Red
    $allCriteriaPassed = $false
}

# Check if database file would be created (this would happen at runtime)
Write-Host "  ℹ Database file will be created at runtime in application data directory" -ForegroundColor Blue

Write-Host ""

# AC 3: 项目中已建立一个API服务模块（ApiClient）
Write-Host "AC 3: 验证API服务模块..." -ForegroundColor Yellow

# Check ApiClient files
if ((Test-Path "src\api\api_client.h") -and (Test-Path "src\api\api_client.cpp")) {
    Write-Host "  ✓ ApiClient files exist" -ForegroundColor Green
} else {
    Write-Host "  ✗ ApiClient files missing" -ForegroundColor Red
    $allCriteriaPassed = $false
}

Write-Host ""

# AC 4: 该服务模块能成功调用服务器的一个测试接口并接收到成功的回应
Write-Host "AC 4: 验证API测试功能..." -ForegroundColor Yellow

# Check for test connection functionality in ApiClient
$apiClientContent = Get-Content "src\api\api_client.h" -Raw
if ($apiClientContent -match "testConnection") {
    Write-Host "  ✓ testConnection method exists" -ForegroundColor Green
} else {
    Write-Host "  ✗ testConnection method missing" -ForegroundColor Red
    $allCriteriaPassed = $false
}

if ($apiClientContent -match "connectionTestCompleted") {
    Write-Host "  ✓ connectionTestCompleted signal exists" -ForegroundColor Green
} else {
    Write-Host "  ✗ connectionTestCompleted signal missing" -ForegroundColor Red
    $allCriteriaPassed = $false
}

Write-Host ""

# AC 5: API地址等信息通过配置文件管理
Write-Host "AC 5: 验证配置文件管理..." -ForegroundColor Yellow

# Check ConfigManager files
if ((Test-Path "src\utils\config_manager.h") -and (Test-Path "src\utils\config_manager.cpp")) {
    Write-Host "  ✓ ConfigManager files exist" -ForegroundColor Green
} else {
    Write-Host "  ✗ ConfigManager files missing" -ForegroundColor Red
    $allCriteriaPassed = $false
}

# Check config.ini template
if (Test-Path "config.ini") {
    Write-Host "  ✓ config.ini template exists" -ForegroundColor Green
    
    # Check for API configuration in config.ini
    $configContent = Get-Content "config.ini" -Raw
    if ($configContent -match "base_url") {
        Write-Host "  ✓ API base_url configuration exists" -ForegroundColor Green
    } else {
        Write-Host "  ✗ API base_url configuration missing" -ForegroundColor Red
        $allCriteriaPassed = $false
    }
    
    if ($configContent -match "timeout") {
        Write-Host "  ✓ API timeout configuration exists" -ForegroundColor Green
    } else {
        Write-Host "  ✗ API timeout configuration missing" -ForegroundColor Red
        $allCriteriaPassed = $false
    }
} else {
    Write-Host "  ✗ config.ini template missing" -ForegroundColor Red
    $allCriteriaPassed = $false
}

Write-Host ""

# Check for unit tests
Write-Host "验证单元测试..." -ForegroundColor Yellow

# Check tests directory structure
if (Test-Path "tests\CMakeLists.txt") {
    Write-Host "  ✓ tests/CMakeLists.txt exists" -ForegroundColor Green
} else {
    Write-Host "  ✗ tests/CMakeLists.txt missing" -ForegroundColor Red
    $allCriteriaPassed = $false
}

# Check unit test files
$testFiles = @(
    "tests\unit\test_database_manager.cpp",
    "tests\unit\test_config_manager.cpp", 
    "tests\unit\test_api_client.cpp"
)

foreach ($testFile in $testFiles) {
    if (Test-Path $testFile) {
        Write-Host "  ✓ $testFile exists" -ForegroundColor Green
    } else {
        Write-Host "  ✗ $testFile missing" -ForegroundColor Red
        $allCriteriaPassed = $false
    }
}

Write-Host ""

# Check build scripts
Write-Host "验证构建脚本..." -ForegroundColor Yellow

if (Test-Path "build.ps1") {
    Write-Host "  ✓ build.ps1 exists" -ForegroundColor Green
} else {
    Write-Host "  ✗ build.ps1 missing" -ForegroundColor Red
    $allCriteriaPassed = $false
}

if (Test-Path "run_tests.ps1") {
    Write-Host "  ✓ run_tests.ps1 exists" -ForegroundColor Green
} else {
    Write-Host "  ✗ run_tests.ps1 missing" -ForegroundColor Red
    $allCriteriaPassed = $false
}

Write-Host ""

# Final validation result
Write-Host "========================================" -ForegroundColor Cyan
if ($allCriteriaPassed) {
    Write-Host "🎉 Story 1.1 验证通过！" -ForegroundColor Green
    Write-Host "所有验收标准都已满足" -ForegroundColor Green
    Write-Host ""
    Write-Host "下一步操作:" -ForegroundColor Cyan
    Write-Host "1. 运行 build.ps1 构建项目" -ForegroundColor White
    Write-Host "2. 运行 run_tests.ps1 执行单元测试" -ForegroundColor White
    Write-Host "3. 运行应用程序验证功能" -ForegroundColor White
} else {
    Write-Host "❌ Story 1.1 验证失败！" -ForegroundColor Red
    Write-Host "请检查上述失败的验收标准" -ForegroundColor Red
}
Write-Host "========================================" -ForegroundColor Cyan

Read-Host "Press Enter to exit" 