@echo off
REM CMake Wrapper Batch File for Visual Studio
REM This provides easy access to Visual Studio's CMake

set CMAKE_PATH="C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe"

if not exist %CMAKE_PATH% (
    echo ❌ CMake not found at: %CMAKE_PATH%
    echo Please check your Visual Studio installation
    exit /b 1
)

echo 🔧 Using Visual Studio CMake: %CMAKE_PATH%

REM Execute CMake with all provided arguments
%CMAKE_PATH% %*

if %ERRORLEVEL% equ 0 (
    echo ✅ CMake command completed successfully
) else (
    echo ❌ CMake command failed with exit code: %ERRORLEVEL%
)

exit /b %ERRORLEVEL%
