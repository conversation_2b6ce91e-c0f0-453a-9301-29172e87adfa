# Generated by CMake. Changes will be overwritten.
C:/PROJECT/HighJump/tests/unit/test_database_manager.cpp
 mmc:Q_OBJECT
 mid:test_database_manager.moc
 mdp:C:/PROJECT/HighJump/src/persistence/database_manager.h
 mdp:C:/PROJECT/HighJump/tests/unit/test_database_manager.cpp
 mdp:C:/Qt/install-x64/include/QtCore/QDir
 mdp:C:/Qt/install-x64/include/QtCore/QMutex
 mdp:C:/Qt/install-x64/include/QtCore/QObject
 mdp:C:/Qt/install-x64/include/QtCore/QStandardPaths
 mdp:C:/Qt/install-x64/include/QtCore/QString
 mdp:C:/Qt/install-x64/include/QtCore/QTemporaryDir
 mdp:C:/Qt/install-x64/include/QtCore/q17memory.h
 mdp:C:/Qt/install-x64/include/QtCore/q20functional.h
 mdp:C:/Qt/install-x64/include/QtCore/q20iterator.h
 mdp:C:/Qt/install-x64/include/QtCore/q20memory.h
 mdp:C:/Qt/install-x64/include/QtCore/q20type_traits.h
 mdp:C:/Qt/install-x64/include/QtCore/q20utility.h
 mdp:C:/Qt/install-x64/include/QtCore/q23functional.h
 mdp:C:/Qt/install-x64/include/QtCore/q23utility.h
 mdp:C:/Qt/install-x64/include/QtCore/qabstracteventdispatcher.h
 mdp:C:/Qt/install-x64/include/QtCore/qabstractitemmodel.h
 mdp:C:/Qt/install-x64/include/QtCore/qalgorithms.h
 mdp:C:/Qt/install-x64/include/QtCore/qanystringview.h
 mdp:C:/Qt/install-x64/include/QtCore/qarraydata.h
 mdp:C:/Qt/install-x64/include/QtCore/qarraydataops.h
 mdp:C:/Qt/install-x64/include/QtCore/qarraydatapointer.h
 mdp:C:/Qt/install-x64/include/QtCore/qassert.h
 mdp:C:/Qt/install-x64/include/QtCore/qatomic.h
 mdp:C:/Qt/install-x64/include/QtCore/qatomic_cxx11.h
 mdp:C:/Qt/install-x64/include/QtCore/qbasicatomic.h
 mdp:C:/Qt/install-x64/include/QtCore/qbasictimer.h
 mdp:C:/Qt/install-x64/include/QtCore/qbindingstorage.h
 mdp:C:/Qt/install-x64/include/QtCore/qbitarray.h
 mdp:C:/Qt/install-x64/include/QtCore/qbytearray.h
 mdp:C:/Qt/install-x64/include/QtCore/qbytearrayalgorithms.h
 mdp:C:/Qt/install-x64/include/QtCore/qbytearraylist.h
 mdp:C:/Qt/install-x64/include/QtCore/qbytearrayview.h
 mdp:C:/Qt/install-x64/include/QtCore/qcalendar.h
 mdp:C:/Qt/install-x64/include/QtCore/qcborarray.h
 mdp:C:/Qt/install-x64/include/QtCore/qcborcommon.h
 mdp:C:/Qt/install-x64/include/QtCore/qcbormap.h
 mdp:C:/Qt/install-x64/include/QtCore/qcborvalue.h
 mdp:C:/Qt/install-x64/include/QtCore/qchar.h
 mdp:C:/Qt/install-x64/include/QtCore/qcompare.h
 mdp:C:/Qt/install-x64/include/QtCore/qcompare_impl.h
 mdp:C:/Qt/install-x64/include/QtCore/qcomparehelpers.h
 mdp:C:/Qt/install-x64/include/QtCore/qcompilerdetection.h
 mdp:C:/Qt/install-x64/include/QtCore/qconfig.h
 mdp:C:/Qt/install-x64/include/QtCore/qconstructormacros.h
 mdp:C:/Qt/install-x64/include/QtCore/qcontainerfwd.h
 mdp:C:/Qt/install-x64/include/QtCore/qcontainerinfo.h
 mdp:C:/Qt/install-x64/include/QtCore/qcontainertools_impl.h
 mdp:C:/Qt/install-x64/include/QtCore/qcontiguouscache.h
 mdp:C:/Qt/install-x64/include/QtCore/qcoreapplication.h
 mdp:C:/Qt/install-x64/include/QtCore/qcoreapplication_platform.h
 mdp:C:/Qt/install-x64/include/QtCore/qcoreevent.h
 mdp:C:/Qt/install-x64/include/QtCore/qdarwinhelpers.h
 mdp:C:/Qt/install-x64/include/QtCore/qdatastream.h
 mdp:C:/Qt/install-x64/include/QtCore/qdatetime.h
 mdp:C:/Qt/install-x64/include/QtCore/qdeadlinetimer.h
 mdp:C:/Qt/install-x64/include/QtCore/qdebug.h
 mdp:C:/Qt/install-x64/include/QtCore/qdir.h
 mdp:C:/Qt/install-x64/include/QtCore/qdirlisting.h
 mdp:C:/Qt/install-x64/include/QtCore/qelapsedtimer.h
 mdp:C:/Qt/install-x64/include/QtCore/qendian.h
 mdp:C:/Qt/install-x64/include/QtCore/qeventloop.h
 mdp:C:/Qt/install-x64/include/QtCore/qexceptionhandling.h
 mdp:C:/Qt/install-x64/include/QtCore/qfile.h
 mdp:C:/Qt/install-x64/include/QtCore/qfiledevice.h
 mdp:C:/Qt/install-x64/include/QtCore/qfileinfo.h
 mdp:C:/Qt/install-x64/include/QtCore/qflags.h
 mdp:C:/Qt/install-x64/include/QtCore/qfloat16.h
 mdp:C:/Qt/install-x64/include/QtCore/qforeach.h
 mdp:C:/Qt/install-x64/include/QtCore/qfunctionaltools_impl.h
 mdp:C:/Qt/install-x64/include/QtCore/qfunctionpointer.h
 mdp:C:/Qt/install-x64/include/QtCore/qgenericatomic.h
 mdp:C:/Qt/install-x64/include/QtCore/qglobal.h
 mdp:C:/Qt/install-x64/include/QtCore/qglobalstatic.h
 mdp:C:/Qt/install-x64/include/QtCore/qhash.h
 mdp:C:/Qt/install-x64/include/QtCore/qhashfunctions.h
 mdp:C:/Qt/install-x64/include/QtCore/qiodevice.h
 mdp:C:/Qt/install-x64/include/QtCore/qiodevicebase.h
 mdp:C:/Qt/install-x64/include/QtCore/qiterable.h
 mdp:C:/Qt/install-x64/include/QtCore/qiterator.h
 mdp:C:/Qt/install-x64/include/QtCore/qlatin1stringview.h
 mdp:C:/Qt/install-x64/include/QtCore/qlist.h
 mdp:C:/Qt/install-x64/include/QtCore/qlocale.h
 mdp:C:/Qt/install-x64/include/QtCore/qlogging.h
 mdp:C:/Qt/install-x64/include/QtCore/qmalloc.h
 mdp:C:/Qt/install-x64/include/QtCore/qmap.h
 mdp:C:/Qt/install-x64/include/QtCore/qmargins.h
 mdp:C:/Qt/install-x64/include/QtCore/qmath.h
 mdp:C:/Qt/install-x64/include/QtCore/qmetacontainer.h
 mdp:C:/Qt/install-x64/include/QtCore/qmetaobject.h
 mdp:C:/Qt/install-x64/include/QtCore/qmetatype.h
 mdp:C:/Qt/install-x64/include/QtCore/qminmax.h
 mdp:C:/Qt/install-x64/include/QtCore/qmutex.h
 mdp:C:/Qt/install-x64/include/QtCore/qnamespace.h
 mdp:C:/Qt/install-x64/include/QtCore/qnativeinterface.h
 mdp:C:/Qt/install-x64/include/QtCore/qnumeric.h
 mdp:C:/Qt/install-x64/include/QtCore/qobject.h
 mdp:C:/Qt/install-x64/include/QtCore/qobject_impl.h
 mdp:C:/Qt/install-x64/include/QtCore/qobjectdefs.h
 mdp:C:/Qt/install-x64/include/QtCore/qobjectdefs_impl.h
 mdp:C:/Qt/install-x64/include/QtCore/qoverload.h
 mdp:C:/Qt/install-x64/include/QtCore/qpair.h
 mdp:C:/Qt/install-x64/include/QtCore/qpoint.h
 mdp:C:/Qt/install-x64/include/QtCore/qpointer.h
 mdp:C:/Qt/install-x64/include/QtCore/qprocessordetection.h
 mdp:C:/Qt/install-x64/include/QtCore/qrect.h
 mdp:C:/Qt/install-x64/include/QtCore/qrefcount.h
 mdp:C:/Qt/install-x64/include/QtCore/qregularexpression.h
 mdp:C:/Qt/install-x64/include/QtCore/qscopedpointer.h
 mdp:C:/Qt/install-x64/include/QtCore/qscopeguard.h
 mdp:C:/Qt/install-x64/include/QtCore/qset.h
 mdp:C:/Qt/install-x64/include/QtCore/qshareddata.h
 mdp:C:/Qt/install-x64/include/QtCore/qshareddata_impl.h
 mdp:C:/Qt/install-x64/include/QtCore/qsharedpointer.h
 mdp:C:/Qt/install-x64/include/QtCore/qsharedpointer_impl.h
 mdp:C:/Qt/install-x64/include/QtCore/qsize.h
 mdp:C:/Qt/install-x64/include/QtCore/qspan.h
 mdp:C:/Qt/install-x64/include/QtCore/qstandardpaths.h
 mdp:C:/Qt/install-x64/include/QtCore/qstdlibdetection.h
 mdp:C:/Qt/install-x64/include/QtCore/qstring.h
 mdp:C:/Qt/install-x64/include/QtCore/qstringalgorithms.h
 mdp:C:/Qt/install-x64/include/QtCore/qstringbuilder.h
 mdp:C:/Qt/install-x64/include/QtCore/qstringconverter.h
 mdp:C:/Qt/install-x64/include/QtCore/qstringconverter_base.h
 mdp:C:/Qt/install-x64/include/QtCore/qstringfwd.h
 mdp:C:/Qt/install-x64/include/QtCore/qstringlist.h
 mdp:C:/Qt/install-x64/include/QtCore/qstringliteral.h
 mdp:C:/Qt/install-x64/include/QtCore/qstringmatcher.h
 mdp:C:/Qt/install-x64/include/QtCore/qstringtokenizer.h
 mdp:C:/Qt/install-x64/include/QtCore/qstringview.h
 mdp:C:/Qt/install-x64/include/QtCore/qswap.h
 mdp:C:/Qt/install-x64/include/QtCore/qsysinfo.h
 mdp:C:/Qt/install-x64/include/QtCore/qsystemdetection.h
 mdp:C:/Qt/install-x64/include/QtCore/qtaggedpointer.h
 mdp:C:/Qt/install-x64/include/QtCore/qtclasshelpermacros.h
 mdp:C:/Qt/install-x64/include/QtCore/qtconfiginclude.h
 mdp:C:/Qt/install-x64/include/QtCore/qtconfigmacros.h
 mdp:C:/Qt/install-x64/include/QtCore/qtcore-config.h
 mdp:C:/Qt/install-x64/include/QtCore/qtcoreexports.h
 mdp:C:/Qt/install-x64/include/QtCore/qtcoreglobal.h
 mdp:C:/Qt/install-x64/include/QtCore/qtdeprecationdefinitions.h
 mdp:C:/Qt/install-x64/include/QtCore/qtdeprecationmarkers.h
 mdp:C:/Qt/install-x64/include/QtCore/qtemporarydir.h
 mdp:C:/Qt/install-x64/include/QtCore/qtenvironmentvariables.h
 mdp:C:/Qt/install-x64/include/QtCore/qtestsupport_core.h
 mdp:C:/Qt/install-x64/include/QtCore/qtextstream.h
 mdp:C:/Qt/install-x64/include/QtCore/qtformat_impl.h
 mdp:C:/Qt/install-x64/include/QtCore/qthread.h
 mdp:C:/Qt/install-x64/include/QtCore/qtimezone.h
 mdp:C:/Qt/install-x64/include/QtCore/qtmetamacros.h
 mdp:C:/Qt/install-x64/include/QtCore/qtnoop.h
 mdp:C:/Qt/install-x64/include/QtCore/qtpreprocessorsupport.h
 mdp:C:/Qt/install-x64/include/QtCore/qtresource.h
 mdp:C:/Qt/install-x64/include/QtCore/qtsan_impl.h
 mdp:C:/Qt/install-x64/include/QtCore/qttranslation.h
 mdp:C:/Qt/install-x64/include/QtCore/qttypetraits.h
 mdp:C:/Qt/install-x64/include/QtCore/qtversion.h
 mdp:C:/Qt/install-x64/include/QtCore/qtversionchecks.h
 mdp:C:/Qt/install-x64/include/QtCore/qtypeinfo.h
 mdp:C:/Qt/install-x64/include/QtCore/qtypes.h
 mdp:C:/Qt/install-x64/include/QtCore/qurl.h
 mdp:C:/Qt/install-x64/include/QtCore/qutf8stringview.h
 mdp:C:/Qt/install-x64/include/QtCore/quuid.h
 mdp:C:/Qt/install-x64/include/QtCore/qvariant.h
 mdp:C:/Qt/install-x64/include/QtCore/qvarlengtharray.h
 mdp:C:/Qt/install-x64/include/QtCore/qversiontagging.h
 mdp:C:/Qt/install-x64/include/QtCore/qxpfunctional.h
 mdp:C:/Qt/install-x64/include/QtCore/qxptype_traits.h
 mdp:C:/Qt/install-x64/include/QtCore/qyieldcpu.h
 mdp:C:/Qt/install-x64/include/QtSql/QSqlDatabase
 mdp:C:/Qt/install-x64/include/QtSql/QSqlError
 mdp:C:/Qt/install-x64/include/QtSql/QSqlQuery
 mdp:C:/Qt/install-x64/include/QtSql/qsqldatabase.h
 mdp:C:/Qt/install-x64/include/QtSql/qsqlerror.h
 mdp:C:/Qt/install-x64/include/QtSql/qsqlquery.h
 mdp:C:/Qt/install-x64/include/QtSql/qtsql-config.h
 mdp:C:/Qt/install-x64/include/QtSql/qtsqlexports.h
 mdp:C:/Qt/install-x64/include/QtSql/qtsqlglobal.h
 mdp:C:/Qt/install-x64/include/QtTest/QSignalSpy
 mdp:C:/Qt/install-x64/include/QtTest/QTest
 mdp:C:/Qt/install-x64/include/QtTest/qbenchmark.h
 mdp:C:/Qt/install-x64/include/QtTest/qbenchmarkmetric.h
 mdp:C:/Qt/install-x64/include/QtTest/qsignalspy.h
 mdp:C:/Qt/install-x64/include/QtTest/qtest.h
 mdp:C:/Qt/install-x64/include/QtTest/qtestcase.h
 mdp:C:/Qt/install-x64/include/QtTest/qtestdata.h
 mdp:C:/Qt/install-x64/include/QtTest/qtesteventloop.h
 mdp:C:/Qt/install-x64/include/QtTest/qtestsystem.h
 mdp:C:/Qt/install-x64/include/QtTest/qtesttostring.h
 mdp:C:/Qt/install-x64/include/QtTest/qttestexports.h
 mdp:C:/Qt/install-x64/include/QtTest/qttestglobal.h
 mdp:C:/Qt/install-x64/include/QtTest/qttestlib-config.h
C:/PROJECT/HighJump/src/persistence/database_manager.h
 mmc:Q_OBJECT
 mdp:C:/PROJECT/HighJump/src/persistence/database_manager.h
 mdp:C:/Qt/install-x64/include/QtCore/QMutex
 mdp:C:/Qt/install-x64/include/QtCore/QObject
 mdp:C:/Qt/install-x64/include/QtCore/QString
 mdp:C:/Qt/install-x64/include/QtCore/q17memory.h
 mdp:C:/Qt/install-x64/include/QtCore/q20functional.h
 mdp:C:/Qt/install-x64/include/QtCore/q20memory.h
 mdp:C:/Qt/install-x64/include/QtCore/q20type_traits.h
 mdp:C:/Qt/install-x64/include/QtCore/q20utility.h
 mdp:C:/Qt/install-x64/include/QtCore/q23utility.h
 mdp:C:/Qt/install-x64/include/QtCore/qalgorithms.h
 mdp:C:/Qt/install-x64/include/QtCore/qanystringview.h
 mdp:C:/Qt/install-x64/include/QtCore/qarraydata.h
 mdp:C:/Qt/install-x64/include/QtCore/qarraydataops.h
 mdp:C:/Qt/install-x64/include/QtCore/qarraydatapointer.h
 mdp:C:/Qt/install-x64/include/QtCore/qassert.h
 mdp:C:/Qt/install-x64/include/QtCore/qatomic.h
 mdp:C:/Qt/install-x64/include/QtCore/qatomic_cxx11.h
 mdp:C:/Qt/install-x64/include/QtCore/qbasicatomic.h
 mdp:C:/Qt/install-x64/include/QtCore/qbindingstorage.h
 mdp:C:/Qt/install-x64/include/QtCore/qbytearray.h
 mdp:C:/Qt/install-x64/include/QtCore/qbytearrayalgorithms.h
 mdp:C:/Qt/install-x64/include/QtCore/qbytearraylist.h
 mdp:C:/Qt/install-x64/include/QtCore/qbytearrayview.h
 mdp:C:/Qt/install-x64/include/QtCore/qchar.h
 mdp:C:/Qt/install-x64/include/QtCore/qcompare.h
 mdp:C:/Qt/install-x64/include/QtCore/qcompare_impl.h
 mdp:C:/Qt/install-x64/include/QtCore/qcomparehelpers.h
 mdp:C:/Qt/install-x64/include/QtCore/qcompilerdetection.h
 mdp:C:/Qt/install-x64/include/QtCore/qconfig.h
 mdp:C:/Qt/install-x64/include/QtCore/qconstructormacros.h
 mdp:C:/Qt/install-x64/include/QtCore/qcontainerfwd.h
 mdp:C:/Qt/install-x64/include/QtCore/qcontainerinfo.h
 mdp:C:/Qt/install-x64/include/QtCore/qcontainertools_impl.h
 mdp:C:/Qt/install-x64/include/QtCore/qcontiguouscache.h
 mdp:C:/Qt/install-x64/include/QtCore/qdarwinhelpers.h
 mdp:C:/Qt/install-x64/include/QtCore/qdatastream.h
 mdp:C:/Qt/install-x64/include/QtCore/qdeadlinetimer.h
 mdp:C:/Qt/install-x64/include/QtCore/qdebug.h
 mdp:C:/Qt/install-x64/include/QtCore/qelapsedtimer.h
 mdp:C:/Qt/install-x64/include/QtCore/qexceptionhandling.h
 mdp:C:/Qt/install-x64/include/QtCore/qflags.h
 mdp:C:/Qt/install-x64/include/QtCore/qfloat16.h
 mdp:C:/Qt/install-x64/include/QtCore/qforeach.h
 mdp:C:/Qt/install-x64/include/QtCore/qfunctionaltools_impl.h
 mdp:C:/Qt/install-x64/include/QtCore/qfunctionpointer.h
 mdp:C:/Qt/install-x64/include/QtCore/qgenericatomic.h
 mdp:C:/Qt/install-x64/include/QtCore/qglobal.h
 mdp:C:/Qt/install-x64/include/QtCore/qglobalstatic.h
 mdp:C:/Qt/install-x64/include/QtCore/qhash.h
 mdp:C:/Qt/install-x64/include/QtCore/qhashfunctions.h
 mdp:C:/Qt/install-x64/include/QtCore/qiodevicebase.h
 mdp:C:/Qt/install-x64/include/QtCore/qiterable.h
 mdp:C:/Qt/install-x64/include/QtCore/qiterator.h
 mdp:C:/Qt/install-x64/include/QtCore/qlatin1stringview.h
 mdp:C:/Qt/install-x64/include/QtCore/qlist.h
 mdp:C:/Qt/install-x64/include/QtCore/qlogging.h
 mdp:C:/Qt/install-x64/include/QtCore/qmalloc.h
 mdp:C:/Qt/install-x64/include/QtCore/qmap.h
 mdp:C:/Qt/install-x64/include/QtCore/qmath.h
 mdp:C:/Qt/install-x64/include/QtCore/qmetacontainer.h
 mdp:C:/Qt/install-x64/include/QtCore/qmetaobject.h
 mdp:C:/Qt/install-x64/include/QtCore/qmetatype.h
 mdp:C:/Qt/install-x64/include/QtCore/qminmax.h
 mdp:C:/Qt/install-x64/include/QtCore/qmutex.h
 mdp:C:/Qt/install-x64/include/QtCore/qnamespace.h
 mdp:C:/Qt/install-x64/include/QtCore/qnumeric.h
 mdp:C:/Qt/install-x64/include/QtCore/qobject.h
 mdp:C:/Qt/install-x64/include/QtCore/qobject_impl.h
 mdp:C:/Qt/install-x64/include/QtCore/qobjectdefs.h
 mdp:C:/Qt/install-x64/include/QtCore/qobjectdefs_impl.h
 mdp:C:/Qt/install-x64/include/QtCore/qoverload.h
 mdp:C:/Qt/install-x64/include/QtCore/qpair.h
 mdp:C:/Qt/install-x64/include/QtCore/qprocessordetection.h
 mdp:C:/Qt/install-x64/include/QtCore/qrefcount.h
 mdp:C:/Qt/install-x64/include/QtCore/qscopedpointer.h
 mdp:C:/Qt/install-x64/include/QtCore/qscopeguard.h
 mdp:C:/Qt/install-x64/include/QtCore/qset.h
 mdp:C:/Qt/install-x64/include/QtCore/qshareddata.h
 mdp:C:/Qt/install-x64/include/QtCore/qshareddata_impl.h
 mdp:C:/Qt/install-x64/include/QtCore/qsharedpointer.h
 mdp:C:/Qt/install-x64/include/QtCore/qsharedpointer_impl.h
 mdp:C:/Qt/install-x64/include/QtCore/qstdlibdetection.h
 mdp:C:/Qt/install-x64/include/QtCore/qstring.h
 mdp:C:/Qt/install-x64/include/QtCore/qstringalgorithms.h
 mdp:C:/Qt/install-x64/include/QtCore/qstringbuilder.h
 mdp:C:/Qt/install-x64/include/QtCore/qstringconverter.h
 mdp:C:/Qt/install-x64/include/QtCore/qstringconverter_base.h
 mdp:C:/Qt/install-x64/include/QtCore/qstringfwd.h
 mdp:C:/Qt/install-x64/include/QtCore/qstringlist.h
 mdp:C:/Qt/install-x64/include/QtCore/qstringliteral.h
 mdp:C:/Qt/install-x64/include/QtCore/qstringmatcher.h
 mdp:C:/Qt/install-x64/include/QtCore/qstringtokenizer.h
 mdp:C:/Qt/install-x64/include/QtCore/qstringview.h
 mdp:C:/Qt/install-x64/include/QtCore/qswap.h
 mdp:C:/Qt/install-x64/include/QtCore/qsysinfo.h
 mdp:C:/Qt/install-x64/include/QtCore/qsystemdetection.h
 mdp:C:/Qt/install-x64/include/QtCore/qtaggedpointer.h
 mdp:C:/Qt/install-x64/include/QtCore/qtclasshelpermacros.h
 mdp:C:/Qt/install-x64/include/QtCore/qtconfiginclude.h
 mdp:C:/Qt/install-x64/include/QtCore/qtconfigmacros.h
 mdp:C:/Qt/install-x64/include/QtCore/qtcore-config.h
 mdp:C:/Qt/install-x64/include/QtCore/qtcoreexports.h
 mdp:C:/Qt/install-x64/include/QtCore/qtcoreglobal.h
 mdp:C:/Qt/install-x64/include/QtCore/qtdeprecationdefinitions.h
 mdp:C:/Qt/install-x64/include/QtCore/qtdeprecationmarkers.h
 mdp:C:/Qt/install-x64/include/QtCore/qtenvironmentvariables.h
 mdp:C:/Qt/install-x64/include/QtCore/qtextstream.h
 mdp:C:/Qt/install-x64/include/QtCore/qtformat_impl.h
 mdp:C:/Qt/install-x64/include/QtCore/qtmetamacros.h
 mdp:C:/Qt/install-x64/include/QtCore/qtnoop.h
 mdp:C:/Qt/install-x64/include/QtCore/qtpreprocessorsupport.h
 mdp:C:/Qt/install-x64/include/QtCore/qtresource.h
 mdp:C:/Qt/install-x64/include/QtCore/qtsan_impl.h
 mdp:C:/Qt/install-x64/include/QtCore/qttranslation.h
 mdp:C:/Qt/install-x64/include/QtCore/qttypetraits.h
 mdp:C:/Qt/install-x64/include/QtCore/qtversion.h
 mdp:C:/Qt/install-x64/include/QtCore/qtversionchecks.h
 mdp:C:/Qt/install-x64/include/QtCore/qtypeinfo.h
 mdp:C:/Qt/install-x64/include/QtCore/qtypes.h
 mdp:C:/Qt/install-x64/include/QtCore/qutf8stringview.h
 mdp:C:/Qt/install-x64/include/QtCore/qvariant.h
 mdp:C:/Qt/install-x64/include/QtCore/qvarlengtharray.h
 mdp:C:/Qt/install-x64/include/QtCore/qversiontagging.h
 mdp:C:/Qt/install-x64/include/QtCore/qxptype_traits.h
 mdp:C:/Qt/install-x64/include/QtCore/qyieldcpu.h
 mdp:C:/Qt/install-x64/include/QtSql/QSqlDatabase
 mdp:C:/Qt/install-x64/include/QtSql/QSqlError
 mdp:C:/Qt/install-x64/include/QtSql/QSqlQuery
 mdp:C:/Qt/install-x64/include/QtSql/qsqldatabase.h
 mdp:C:/Qt/install-x64/include/QtSql/qsqlerror.h
 mdp:C:/Qt/install-x64/include/QtSql/qsqlquery.h
 mdp:C:/Qt/install-x64/include/QtSql/qtsql-config.h
 mdp:C:/Qt/install-x64/include/QtSql/qtsqlexports.h
 mdp:C:/Qt/install-x64/include/QtSql/qtsqlglobal.h
C:/PROJECT/HighJump/src/persistence/database_manager.cpp
