{"permissions": {"allow": ["<PERSON><PERSON>(chmod:*)", "Bash(export:*)", "Bash(\"./build/bin/Release/test_database_manager.exe\")", "Bash(cp:*)", "Bash(ldd:*)", "Bash(powershell.exe:*)", "Bash(cmake:*)", "<PERSON><PERSON>(timeout:*)", "Bash(./test_database_manager.exe --help)", "Bash(./test_database_manager.exe:*)", "Bash(echo \"Exit code: $?\")", "<PERSON><PERSON>(cat:*)", "Bash(ls:*)", "Bash(find:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(./test_config_manager.exe)", "<PERSON><PERSON>(echo:*)", "Bash(.test_config_manager.exe)", "Bash(Remove-Item -Path \"debug_config.cpp\" -Force -ErrorAction SilentlyContinue)"], "deny": []}}