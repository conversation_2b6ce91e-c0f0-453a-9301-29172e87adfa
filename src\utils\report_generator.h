#ifndef REPORT_GENERATOR_H
#define REPORT_GENERATOR_H

#include <QObject>
#include <QString>
#include <QList>
#include <QVariantMap>
#include <QVariantList>
#include <QDateTime>
#include <QTime>
#include <QScopedPointer>

// Forward declarations
class QPrinter;
class QTextDocument;
class QWidget;

/**
 * @brief Comprehensive report generation system for High Jump Competition Management
 * 
 * This class provides advanced report generation capabilities for the High Jump
 * Competition Management System, supporting multiple output formats including PDF,
 * Excel (CSV), and HTML with customizable templates and real-time preview functionality.
 * 
 * The ReportGenerator implements a sophisticated template-based system that allows
 * for flexible report customization while maintaining consistent formatting and
 * professional appearance. It provides:
 * - Multiple output formats (PDF, Excel, HTML)
 * - Template-based report generation
 * - Real-time preview and editing capabilities
 * - Customizable data formatting and styling
 * - Progress tracking and cancellation support
 * - Error handling and validation
 * 
 * The system supports various report types including competition summaries,
 * athlete details, results tables, and performance analysis reports.
 * 
 * @note This class uses Qt's printing system for PDF generation and supports
 *       both programmatic report generation and user-initiated report creation.
 */
class ReportGenerator : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief Supported report output formats
     * 
     * Defines the different output formats available for report generation.
     * Each format has specific characteristics and use cases.
     */
    enum ReportFormat {
        PDF,        ///< Portable Document Format - high quality, fixed layout
        Excel,      ///< Excel/CSV format - spreadsheet compatible
        HTML        ///< HyperText Markup Language - web browser compatible
    };

    /**
     * @brief Available report types
     * 
     * Defines the different types of reports that can be generated.
     * Each type has specific content and formatting requirements.
     */
    enum ReportType {
        CompetitionSummary,    ///< Overall competition results and statistics
        AthleteDetails,        ///< Individual athlete performance details
        ResultsTable,          ///< Tabular format of all results
        PerformanceAnalysis,   ///< Statistical analysis and trends
        CustomReport          ///< User-defined custom report format
    };

    /**
     * @brief Report template structure
     * 
     * Defines a report template with all necessary information for
     * generating consistent reports with specific formatting.
     */
    struct ReportTemplate {
        QString name;              ///< Template name
        QString description;       ///< Template description
        ReportType type;           ///< Associated report type
        QString templatePath;      ///< Path to template file
        QVariantMap defaultSettings; ///< Default template settings
    };

    /**
     * @brief Report data structure
     * 
     * Contains all data necessary for generating a complete report,
     * including competition information, athlete data, and results.
     */
    struct ReportData {
        QString title;             ///< Report title
        QString subtitle;          ///< Report subtitle
        QDateTime generatedAt;     ///< Report generation timestamp
        QVariantMap competitionData; ///< Competition information
        QVariantList athletesData; ///< Athlete information list
        QVariantList resultsData;  ///< Competition results data
        QVariantMap statistics;    ///< Statistical data
    };

    /**
     * @brief Constructs the report generator
     * @param parent Parent QObject (default: nullptr)
     * 
     * Initializes the report generator with default templates and settings.
     * Sets up the printing system and loads available templates.
     */
    explicit ReportGenerator(QObject *parent = nullptr);
    
    /**
     * @brief Destructor
     * 
     * Ensures proper cleanup of resources including printer and document objects.
     */
    ~ReportGenerator();

    // Main report generation methods
    /**
     * @brief Generates a report of specified type and format
     * @param type Type of report to generate
     * @param format Output format for the report
     * @param outputPath Path where to save the generated report
     * @param data Report data (optional, uses default if not provided)
     * @return true if report generated successfully, false otherwise
     * 
     * Generates a complete report with the specified type and format.
     * The report is saved to the specified output path. Progress is
     * reported through signals during generation.
     */
    bool generateReport(ReportType type, ReportFormat format, const QString &outputPath, 
                       const ReportData &data = ReportData());
    
    /**
     * @brief Generates a report using a specific template
     * @param templateName Name of the template to use
     * @param format Output format for the report
     * @param outputPath Path where to save the generated report
     * @param data Report data (optional, uses default if not provided)
     * @return true if report generated successfully, false otherwise
     * 
     * Generates a report using a specific template for custom formatting.
     * This allows for highly customized report layouts and styling.
     */
    bool generateReportFromTemplate(const QString &templateName, ReportFormat format, 
                                   const QString &outputPath, const ReportData &data = ReportData());

    // Template management
    /**
     * @brief Gets all available report templates
     * @return List of available templates
     * 
     * Returns a list of all templates available for report generation,
     * including both built-in and custom templates.
     */
    QList<ReportTemplate> getAvailableTemplates() const;
    
    /**
     * @brief Adds a new report template
     * @param reportTemplate Template to add
     * @return true if template added successfully, false otherwise
     * 
     * Adds a new custom template to the available templates.
     * The template will be available for future report generation.
     */
    bool addTemplate(const ReportTemplate &reportTemplate);
    
    /**
     * @brief Removes a report template
     * @param templateName Name of the template to remove
     * @return true if template removed successfully, false otherwise
     * 
     * Removes a custom template from the available templates.
     * Built-in templates cannot be removed.
     */
    bool removeTemplate(const QString &templateName);
    
    /**
     * @brief Updates an existing template
     * @param templateName Name of the template to update
     * @param reportTemplate New template data
     * @return true if template updated successfully, false otherwise
     * 
     * Updates an existing template with new data.
     * Only custom templates can be updated.
     */
    bool updateTemplate(const QString &templateName, const ReportTemplate &reportTemplate);

    // Preview functionality
    /**
     * @brief Creates a preview widget for a report
     * @param type Type of report to preview
     * @param data Report data (optional, uses default if not provided)
     * @return Preview widget, or nullptr on error
     * 
     * Creates a widget that shows a preview of the report.
     * The widget can be embedded in dialogs or other UI components.
     */
    QWidget* createPreviewWidget(ReportType type, const ReportData &data = ReportData());
    
    /**
     * @brief Shows a preview dialog for a report
     * @param type Type of report to preview
     * @param data Report data (optional, uses default if not provided)
     * @param parent Parent widget for the dialog
     * @return true if dialog shown successfully, false otherwise
     * 
     * Shows a modal dialog with a preview of the report.
     * The dialog includes options for printing and saving.
     */
    bool showPreviewDialog(ReportType type, const ReportData &data = ReportData(), QWidget *parent = nullptr);

    // Utility methods
    /**
     * @brief Gets the default output path for a report
     * @param type Type of report
     * @param format Output format
     * @return Default output path
     * 
     * Generates a default output path based on report type and format.
     * The path includes appropriate file extension and timestamp.
     */
    QString getDefaultOutputPath(ReportType type, ReportFormat format) const;
    
    /**
     * @brief Validates report data
     * @param data Report data to validate
     * @return true if data is valid, false otherwise
     * 
     * Validates that the report data contains all required information
     * for successful report generation.
     */
    bool validateReportData(const ReportData &data) const;
    
    /**
     * @brief Gets file extension for a format
     * @param format Report format
     * @return File extension (including dot)
     * 
     * Returns the appropriate file extension for the specified format.
     */
    QString getFormatExtension(ReportFormat format) const;
    
    // Report control
    /**
     * @brief Cancels ongoing report generation
     * 
     * Cancels any currently running report generation process.
     * This is useful for long-running reports that need to be stopped.
     */
    void cancelGeneration();

signals:
    /**
     * @brief Emitted when report generation starts
     * @param reportName Name of the report being generated
     * 
     * Signal emitted when report generation begins.
     * Used for UI updates and progress tracking.
     */
    void reportGenerationStarted(const QString &reportName);
    
    /**
     * @brief Emitted during report generation progress
     * @param percentage Progress percentage (0-100)
     * 
     * Signal emitted during report generation to show progress.
     * Used for progress bars and status updates.
     */
    void reportGenerationProgress(int percentage);
    
    /**
     * @brief Emitted when report generation completes
     * @param outputPath Path to the generated report
     * 
     * Signal emitted when report generation completes successfully.
     * Used for completion notifications and file opening.
     */
    void reportGenerationCompleted(const QString &outputPath);
    
    /**
     * @brief Emitted when report generation fails
     * @param error Error message describing the failure
     * 
     * Signal emitted when report generation fails.
     * Used for error handling and user notifications.
     */
    void reportGenerationFailed(const QString &error);

private slots:
    /**
     * @brief Handles printer errors
     * @param error Printer error code
     * 
     * Slot for handling printer errors during PDF generation.
     * Converts printer errors to user-friendly error messages.
     */
    void onPrinterError(int error);

private:
    // Format-specific generation methods
    /**
     * @brief Generates a PDF report
     * @param type Type of report to generate
     * @param outputPath Path where to save the PDF
     * @param data Report data
     * @return true if PDF generated successfully, false otherwise
     * 
     * Generates a PDF report using Qt's printing system.
     * Creates high-quality, print-ready PDF documents.
     */
    bool generatePDFReport(ReportType type, const QString &outputPath, const ReportData &data);
    
    /**
     * @brief Generates an Excel/CSV report
     * @param type Type of report to generate
     * @param outputPath Path where to save the Excel file
     * @param data Report data
     * @return true if Excel file generated successfully, false otherwise
     * 
     * Generates an Excel-compatible CSV file with tabular data.
     * Suitable for data analysis and spreadsheet applications.
     */
    bool generateExcelReport(ReportType type, const QString &outputPath, const ReportData &data);
    
    /**
     * @brief Generates an HTML report
     * @param type Type of report to generate
     * @param outputPath Path where to save the HTML file
     * @param data Report data
     * @return true if HTML file generated successfully, false otherwise
     * 
     * Generates an HTML report suitable for web browsers.
     * Includes CSS styling for professional appearance.
     */
    bool generateHTMLReport(ReportType type, const QString &outputPath, const ReportData &data);
    
    // Content generation methods
    /**
     * @brief Creates a text document for PDF generation
     * @param type Type of report
     * @param data Report data
     * @return QTextDocument for PDF generation
     * 
     * Creates a QTextDocument with formatted content for PDF generation.
     * Handles text formatting, tables, and layout.
     */
    QTextDocument* createTextDocument(ReportType type, const ReportData &data);
    
    /**
     * @brief Generates HTML content for reports
     * @param type Type of report
     * @param data Report data
     * @return HTML content string
     * 
     * Generates HTML content with CSS styling for web-based reports.
     * Creates professional-looking HTML documents.
     */
    QString generateHTMLContent(ReportType type, const ReportData &data);
    
    /**
     * @brief Generates CSV content for Excel reports
     * @param type Type of report
     * @param data Report data
     * @return CSV content string
     * 
     * Generates CSV content for Excel-compatible files.
     * Handles data formatting and table structure.
     */
    QString generateCSVContent(ReportType type, const ReportData &data);
    
    // Template processing methods
    /**
     * @brief Processes a template with data
     * @param templateContent Template content
     * @param data Data to insert into template
     * @return Processed template content
     * 
     * Processes a template by replacing placeholders with actual data.
     * Supports complex template logic and conditional content.
     */
    QString processTemplate(const QString &templateContent, const ReportData &data);
    
    /**
     * @brief Replaces placeholders in content
     * @param content Content with placeholders
     * @param data Data for placeholder replacement
     * @return Content with replaced placeholders
     * 
     * Replaces template placeholders with actual data values.
     * Supports various placeholder formats and data types.
     */
    QString replacePlaceholders(const QString &content, const QVariantMap &data);
    
    // Formatting utility methods
    /**
     * @brief Formats a date/time for display
     * @param dateTime Date/time to format
     * @return Formatted date/time string
     * 
     * Formats a QDateTime object for consistent display in reports.
     * Uses localized formatting appropriate for the report context.
     */
    QString formatDateTime(const QDateTime &dateTime) const;
    
    /**
     * @brief Formats a height value for display
     * @param height Height in centimeters
     * @return Formatted height string
     * 
     * Formats a height value for consistent display in reports.
     * Includes appropriate units and precision.
     */
    QString formatHeight(double height) const;
    
    /**
     * @brief Formats a time value for display
     * @param time Time to format
     * @return Formatted time string
     * 
     * Formats a QTime object for consistent display in reports.
     * Uses appropriate time format for the context.
     */
    QString formatTime(const QTime &time) const;
    
    /**
     * @brief Formats statistical data for display
     * @param stats Statistical data map
     * @return Formatted statistics string
     * 
     * Formats statistical data for display in reports.
     * Handles various statistical measures and their formatting.
     */
    QString formatStatistics(const QVariantMap &stats) const;
    
    // Template management methods
    /**
     * @brief Initializes default templates
     * 
     * Creates and registers default templates for all report types.
     * These templates provide standard formatting for common reports.
     */
    void initializeDefaultTemplates();
    
    /**
     * @brief Gets the default template for a report type
     * @param type Report type
     * @return Default template content
     * 
     * Returns the default template content for the specified report type.
     * Used when no custom template is specified.
     */
    QString getDefaultTemplate(ReportType type) const;
    
    // Content generation methods for specific report types
    QString generateCompetitionSummaryHTML(const ReportData &data);
    QString generateAthleteDetailsHTML(const ReportData &data);
    QString generateResultsTableHTML(const ReportData &data);
    QString generatePerformanceAnalysisHTML(const ReportData &data);
    QString generateCustomReportHTML(const ReportData &data);
    
    // Member variables
    QScopedPointer<QPrinter> m_printer;        ///< Printer for PDF generation
    QScopedPointer<QTextDocument> m_document;  ///< Document for content generation
    QList<ReportTemplate> m_templates;         ///< Available report templates
    bool m_generationCancelled;                ///< Cancellation flag
    QString m_currentReportName;               ///< Current report being generated
};

#endif // REPORT_GENERATOR_H 