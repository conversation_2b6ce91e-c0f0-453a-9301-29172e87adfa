# 核心业务流程

## 主要用户工作流程

### 1. 应用启动与赛事选择流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant App as 应用程序
    participant API as API客户端
    participant DB as 数据库
    participant Server as 服务器

    User->>App: 启动应用
    App->>DB: 初始化本地数据库
    DB-->>App: 数据库就绪
    
    App->>API: 检测网络连接
    alt 网络可用
        API->>Server: 获取比赛列表
        Server-->>API: 返回比赛数据
        API->>DB: 缓存比赛列表
    else 网络不可用
        App->>DB: 从本地加载比赛列表
    end
    
    App->>User: 显示赛事选择界面
    User->>App: 选择比赛
    App->>DB: 加载比赛详细数据
    DB-->>App: 返回运动员和高度数据
    App->>User: 显示主计分界面
```

### 2. 核心计分操作流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant UI as 用户界面
    participant Model as 数据模型
    participant Rules as 规则引擎
    participant DB as 数据库
    participant Sync as 同步队列

    User->>UI: 按下试跳结果键(O/X/-/R)
    UI->>Model: 记录试跳结果
    Model->>Rules: 验证操作有效性
    Rules-->>Model: 验证通过
    
    Model->>DB: 写入本地数据库
    DB-->>Model: 写入成功
    
    Model->>Sync: 添加到同步队列
    Sync-->>Model: 队列添加成功
    
    Model->>Rules: 重新计算排名
    Rules-->>Model: 返回新排名
    
    Model->>UI: 发送数据更新信号
    UI->>User: 更新界面显示
    
    alt 自动前进到下一位选手
        Model->>Rules: 计算下一个焦点
        Rules-->>Model: 返回下一位运动员
        Model->>UI: 更新焦点位置
    end
```

### 3. 离线数据同步流程

```mermaid
sequenceDiagram
    participant Timer as 定时器
    participant Sync as 同步管理器
    participant API as API客户端
    participant DB as 数据库
    participant Server as 服务器

    Timer->>Sync: 触发同步检查
    Sync->>API: 检测网络状态
    
    alt 网络可用
        API-->>Sync: 网络连接正常
        Sync->>DB: 获取待同步数据
        DB-->>Sync: 返回同步队列
        
        loop 处理每个同步项
            Sync->>API: 发送数据到服务器
            API->>Server: HTTP请求
            alt 服务器响应成功
                Server-->>API: 200 OK
                API-->>Sync: 同步成功
                Sync->>DB: 标记为已同步
            else 服务器错误
                Server-->>API: 错误响应
                API-->>Sync: 同步失败
                Sync->>DB: 增加重试计数
            end
        end
    else 网络不可用
        API-->>Sync: 网络连接失败
        Sync->>Timer: 等待下次检查
    end
```

### 4. 排名计算工作流程

```mermaid
sequenceDiagram
    participant Model as 数据模型
    participant Rules as 规则引擎
    participant DB as 数据库

    Model->>Rules: 触发排名计算
    Rules->>DB: 查询所有运动员成绩
    DB-->>Rules: 返回成绩数据
    
    Rules->>Rules: 按最佳成绩排序
    Rules->>Rules: 处理成绩相同情况
    
    loop 对于每组相同成绩
        Rules->>Rules: 比较该高度失败次数
        alt 失败次数不同
            Rules->>Rules: 失败次数少的排前
        else 失败次数相同
            Rules->>Rules: 比较总失败次数
            alt 总失败次数不同
                Rules->>Rules: 总失败次数少的排前
            else 完全平跳
                Rules->>Rules: 并列排名
            end
        end
    end
    
    Rules-->>Model: 返回排序结果
    Model->>Model: 更新内部排名数据
```

### 5. 成绩导出流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant UI as 用户界面
    participant Export as 导出工具
    participant DB as 数据库
    participant File as 文件系统

    User->>UI: 点击导出按钮
    UI->>Export: 启动导出流程
    Export->>DB: 查询最终成绩
    DB-->>Export: 返回完整数据
    
    Export->>Export: 生成PDF报告
    Export->>File: 写入PDF文件
    File-->>Export: PDF创建成功
    
    Export->>Export: 生成CSV数据
    Export->>File: 写入CSV文件
    File-->>Export: CSV创建成功
    
    Export->>UI: 导出完成通知
    UI->>User: 显示成功消息
```

## 错误处理流程

### 1. 数据库错误处理

```mermaid
sequenceDiagram
    participant Model as 数据模型
    participant DB as 数据库
    participant Error as 错误处理器
    participant UI as 用户界面

    Model->>DB: 执行数据库操作
    DB-->>Model: 返回错误
    Model->>Error: 处理数据库错误
    
    alt 连接错误
        Error->>Error: 尝试重新连接
        Error->>DB: 重新建立连接
        alt 重连成功
            DB-->>Error: 连接恢复
            Error->>Model: 重试原操作
        else 重连失败
            Error->>UI: 显示离线模式警告
        end
    else 约束错误
        Error->>UI: 显示数据验证错误
    else 其他错误
        Error->>UI: 显示通用错误消息
        Error->>Error: 记录错误日志
    end
```

### 2. 网络错误处理

```mermaid
sequenceDiagram
    participant API as API客户端
    participant Retry as 重试机制
    participant Server as 服务器
    participant UI as 用户界面

    API->>Server: 发送HTTP请求
    Server-->>API: 网络超时/错误
    
    API->>Retry: 触发重试逻辑
    Retry->>Retry: 检查是否可重试
    
    alt 可以重试
        Retry->>Retry: 等待退避时间
        Retry->>API: 重新发送请求
        API->>Server: 重试请求
        alt 重试成功
            Server-->>API: 成功响应
        else 重试失败
            Retry->>Retry: 达到最大重试次数
            Retry->>UI: 显示网络错误
        end
    else 不可重试
        Retry->>UI: 立即显示错误
    end
```

## 状态转换流程

### 运动员状态转换

```mermaid
stateDiagram-v2
    [*] --> Active: 比赛开始
    
    Active --> Active: 成功试跳
    Active --> Active: 失败试跳(未达3次)
    Active --> Eliminated: 连续3次失败
    Active --> Retired: 主动退赛
    Active --> Finished: 比赛结束
    
    Eliminated --> [*]: 比赛结束
    Retired --> [*]: 比赛结束
    Finished --> [*]: 比赛结束
```

### 比赛状态转换

```mermaid
stateDiagram-v2
    [*] --> NotStarted: 创建比赛
    
    NotStarted --> InProgress: 开始比赛
    InProgress --> Paused: 暂停比赛
    Paused --> InProgress: 恢复比赛
    InProgress --> Finished: 所有运动员完成/淘汰
    
    Finished --> [*]: 比赛归档
```

## 数据流程图

### 核心数据流

```mermaid
flowchart TD
    A[用户输入] --> B[UI层验证]
    B --> C[数据模型]
    C --> D[规则引擎验证]
    D --> E[数据库存储]
    E --> F[同步队列]
    F --> G[API上传]
    
    C --> H[排名计算]
    H --> I[UI更新]
    
    G --> J[服务器同步]
    J --> K[同步状态更新]
```

### 配置数据流

```mermaid
flowchart TD
    A[配置文件] --> B[配置管理器]
    B --> C[API客户端配置]
    B --> D[数据库配置]
    B --> E[UI主题配置]
    
    C --> F[网络请求]
    D --> G[数据库连接]
    E --> H[界面渲染]
```

## 性能关键路径

### 高频操作优化

1. **试跳记录路径**: 用户输入 → 数据验证 → 本地存储 → UI更新
   - 目标延迟: < 100ms
   - 优化策略: 内存缓存 + 批量数据库写入

2. **排名计算路径**: 数据变更 → 规则引擎 → 排序算法 → 显示更新
   - 目标延迟: < 200ms
   - 优化策略: 增量计算 + 缓存排名结果

3. **界面响应路径**: 用户交互 → 事件处理 → 模型更新 → 视图刷新
   - 目标延迟: < 50ms
   - 优化策略: 异步处理 + 局部更新

## 并发处理策略

### 多线程架构

- **主线程**: UI交互和即时响应
- **数据库线程**: 所有数据库操作
- **网络线程**: API请求和同步操作
- **计算线程**: 排名计算和数据处理

### 线程间通信

使用Qt的信号槽机制确保线程安全的数据传递，避免直接的跨线程数据访问。