<?xml version="1.0" encoding="UTF-8" ?>
<testsuite name="TestDatabaseManager" timestamp="2025&#x002D;08&#x002D;07T00:52:25" hostname="BD51" tests="10" failures="5" errors="0" skipped="0" time="0.015">
  <properties>
    <property name="QTestVersion" value="6.9.1"/>
    <property name="QtVersion" value="6.9.1"/>
    <property name="QtBuild" value="Qt 6.9.1 (x86_64&#x002D;little_endian&#x002D;llp64 shared (dynamic) release build; by MSVC 2022)"/>
  </properties>
  <testcase name="initTestCase" classname="TestDatabaseManager" time="0.008">
    <system-out>
      <![CDATA[Test database path: "C:/Users/<USER>/AppData/Local/Temp/test_database_manager-WMcfZb/test_competition_data.sqlite"]]>
    </system-out>
  </testcase>
  <testcase name="testSingletonPattern" classname="TestDatabaseManager" time="0.001">
    <system-out>
      <![CDATA[Starting testSingletonPattern...]]>
      <![CDATA[Got first instance: DatabaseManager(0x122a9c87910)]]>
      <![CDATA[Got second instance: DatabaseManager(0x122a9c87910)]]>
      <![CDATA[testSingletonPattern completed successfully]]>
    </system-out>
  </testcase>
  <testcase name="testDatabaseInitialization" classname="TestDatabaseManager" time="0.002">
    <failure type="fail" message="&apos;initResult&apos; returned FALSE. ()"/>
    <system-out>
      <![CDATA[Starting testDatabaseInitialization...]]>
      <![CDATA[Got DatabaseManager instance: DatabaseManager(0x122a9c87910)]]>
      <![CDATA[Calling initialize()...]]>
      <![CDATA[Initializing DatabaseManager...]]>
      <![CDATA[Database file path: "C:/Users/<USER>/AppData/Roaming/test_database_manager/competition_data.sqlite"]]>
      <![CDATA[Initialize result: false]]>
    </system-out>
    <system-err>
      <![CDATA[qt.sql.qsqldatabase: QSqlDatabase: can not load requested driver 'QSQLITE', available drivers: ]]>
      <![CDATA["Failed to open database: Driver not loaded Driver not loaded"]]>
      <![CDATA[Failed to connect to database]]>
    </system-err>
  </testcase>
  <testcase name="testTableCreation" classname="TestDatabaseManager" time="0.000">
    <failure type="fail" message="&apos;dbManager&#x002D;&gt;tableExists(&quot;competitions&quot;)&apos; returned FALSE. ()"/>
  </testcase>
  <testcase name="testSchemaVersionManagement" classname="TestDatabaseManager" time="0.000">
    <failure type="fail" message="&apos;currentVersion &gt; 0&apos; returned FALSE. ()"/>
    <system-err>
      <![CDATA[qt.sql.qsqlquery: QSqlQuery::exec: database not open]]>
      <![CDATA["Get current schema version failed: Driver not loaded Driver not loaded"]]>
    </system-err>
  </testcase>
  <testcase name="testTransactionManagement" classname="TestDatabaseManager" time="0.000">
    <failure type="fail" message="&apos;dbManager&#x002D;&gt;beginTransaction()&apos; returned FALSE. ()"/>
  </testcase>
  <testcase name="testErrorHandling" classname="TestDatabaseManager" time="0.000">
    <system-err>
      <![CDATA[qt.sql.qsqlquery: QSqlQuery::exec: database not open]]>
    </system-err>
  </testcase>
  <testcase name="testDatabasePath" classname="TestDatabaseManager" time="0.000">
    <system-out>
      <![CDATA[Database path: "C:/Users/<USER>/AppData/Roaming/test_database_manager/competition_data.sqlite"]]>
    </system-out>
  </testcase>
  <testcase name="testConnectionStatus" classname="TestDatabaseManager" time="0.000">
    <failure type="fail" message="&apos;dbManager&#x002D;&gt;isConnected()&apos; returned FALSE. ()"/>
  </testcase>
  <testcase name="cleanupTestCase" classname="TestDatabaseManager" time="0.000"/>
</testsuite>
