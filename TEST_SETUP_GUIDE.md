# High Jump Competition Management System - Test Setup Guide

## Overview

This guide helps resolve Qt SQLite plugin loading issues that prevent tests from running properly.

## Problem Description

The main issue is: **"QSqlDatabase: can not load requested driver 'QSQLITE'"**

This occurs because Qt cannot find the SQLite plugin (qsqlite.dll) even though it exists in the build directory.

## Root Cause

Qt applications need to find plugins in specific locations. The SQLite plugin must be:
1. In the correct directory structure (`sqldrivers/qsqlite.dll`)
2. Accessible via the `QT_PLUGIN_PATH` environment variable
3. Run from the directory containing Qt DLLs

## Solutions Provided

### 1. Updated Test Runner Script (`run_tests.ps1`)

The PowerShell script now:
- Changes to the Release directory where Qt DLLs are located
- Sets `QT_PLUGIN_PATH` environment variable
- Runs tests from the correct working directory
- Provides better error reporting

**Usage:**
```powershell
.\run_tests.ps1
```

### 2. Batch File Alternative (`run_tests.bat`)

For environments where PowerShell execution is restricted:
```batch
run_tests.bat
```

### 3. Qt Environment Test Script (`test_qt_environment.ps1`)

Diagnoses Qt setup issues:
```powershell
.\test_qt_environment.ps1
```

### 4. Updated CMakeLists.txt

Added automatic Qt deployment for Windows builds using `windeployqt.exe`.

## Manual Testing Steps

If the scripts don't work, try manual testing:

1. **Navigate to Release directory:**
   ```cmd
   cd build\bin\Release
   ```

2. **Set environment variable:**
   ```cmd
   set QT_PLUGIN_PATH=%CD%
   ```

3. **Verify files exist:**
   - `qsqlite.dll`
   - `sqldrivers\qsqlite.dll`
   - All Qt6*.dll files

4. **Run individual tests:**
   ```cmd
   test_database_manager.exe
   test_config_manager.exe
   test_api_client.exe
   test_e2e_workflow.exe
   ```

## Troubleshooting

### Common Issues

1. **Missing Qt DLLs**
   - Rebuild project in Release mode
   - Check Qt installation

2. **Plugin Path Issues**
   - Ensure `sqldrivers` folder exists
   - Verify `qsqlite.dll` is in both root and `sqldrivers` folder

3. **Permission Issues**
   - Run as administrator if needed
   - Check file permissions

### Debug Mode

Enable Qt plugin debugging:
```cmd
set QT_DEBUG_PLUGINS=1
```

This will show detailed plugin loading information.

## File Structure

Expected structure in `build/bin/Release/`:
```
build/bin/Release/
├── Qt6Core.dll
├── Qt6Sql.dll
├── Qt6Test.dll
├── Qt6Widgets.dll
├── Qt6Network.dll
├── qsqlite.dll
├── sqldrivers/
│   └── qsqlite.dll
├── test_database_manager.exe
├── test_config_manager.exe
├── test_api_client.exe
└── test_e2e_workflow.exe
```

## Next Steps

1. **Run the updated test script:**
   ```powershell
   .\run_tests.ps1
   ```

2. **If issues persist, run diagnostics:**
   ```powershell
   .\test_qt_environment.ps1
   ```

3. **For manual debugging:**
   ```cmd
   cd build\bin\Release
   set QT_PLUGIN_PATH=%CD%
   set QT_DEBUG_PLUGINS=1
   test_database_manager.exe
   ```

## Success Indicators

When working correctly, you should see:
- ✅ All Qt DLLs found
- ✅ SQLite plugin files present
- ✅ Tests run without "QSQLITE" driver errors
- ✅ Database operations complete successfully

The codebase itself is clean and well-structured. These are deployment/environment issues, not code problems.
