
# 高度计分系统 - 前端架构文档 V

### **第一章：模板与框架选择 (Template and Framework Selection)**

#### **1.1 基础选择 (Foundation Choice)**

根据产品需求文档（PRD）第四章“技术假设”中的明确规定，本项目将被作为一个全新的（Greenfield）项目进行开发。我们确认**不使用任何现有的启动模板或样板代码**。整个应用程序将基于 **Qt 6** 框架从头开始构建。

#### **1.2 变更日志 (Change Log)**

| 日期 | 版本 | 描述 | 作者 |
| :--- | :--- | :--- | :--- |
| 2025-08-06 | 1.0 | 初始架构文档创建 | <PERSON> (Architect) |
| 2025-08-06 | 1.1 | 增加离线模式与数据同步架构 | Winston (Architect) |

-----

### **第二章：前端技术栈 (Frontend Tech Stack)**

#### **2.1 技术选型表 (Technology Stack Table)**

| 类别 (Category) | 技术 (Technology) | 版本 (Version) | 目的 (Purpose) | 理由 (Rationale) |
| :--- | :--- | :--- | :--- | :--- |
| **核心框架** | Qt | 6.9.1 | 构建整个桌面应用的用户界面、逻辑和平台集成 | 用户指定版本，确保与开发环境一致。 |
| **编程语言** | C++ | C++17 | 应用程序的主要编程语言 | Qt的原生语言，提供最高性能和完整的框架API访问。 |
| **构建工具** | CMake | 3.2x (最新稳定版) | 跨平台的项目构建与依赖管理 | 现代C++和Qt 6项目的标准构建系统。 |
| **本地数据库** | **SQLite3** | (与Qt 6捆绑) | **为应用提供本地数据持久化，实现离线功能。** | **轻量、稳定、文件式、与Qt的SQL模块原生集成。** |
| **测试框架** | Qt Test | (与Qt 6.9.1捆绑) | 单元测试与集成测试 | 与Qt原生集成，简化Qt特性的测试。 |
| **网络通信** | QNetworkAccessManager | (与Qt 6.9.1捆绑) | 处理所有与服务器API的HTTP/HTTPS通信 | Qt中执行网络请求的标准、集成化方案。 |
| **文件导出** | QPdfWriter / QFile | (与Qt 6.9.1捆绑) | 实现PDF和CSV格式的成绩导出功能 | 优先使用Qt原生功能，减少外部依赖。 |

-----

### **第三章：项目结构 (Project Structure)**

```plaintext
high-jump-scorer/
├── CMakeLists.txt              # 主CMake构建配置文件
│
├── src/                        # 应用程序所有源代码
│   ├── main.cpp                # 应用程序主入口点
│   ├── api/                    # 负责与服务器API通信的模块
│   ├── core/                   # 核心业务逻辑 (比赛规则引擎)
│   ├── models/                 # 数据模型
│   ├── persistence/            # << 新增：持久化层模块 (SQLite)
│   │   ├── database_manager.h
│   │   └── database_manager.cpp
│   ├── ui/                     # 用户界面 (窗口和自定义控件)
│   └── utils/                  # 通用工具或辅助函数
│
├── resources/                  # 资源文件 (图标等)
│
└── tests/                      # 测试代码
```

-----

### **第四章：组件标准 (Component Standards)**

所有UI组件应遵循Qt的信号与槽机制进行解耦，并采用统一的命名约定（类名PascalCase，文件名snake\_case，函数名camelCase，成员变量m\_camelCase）。

-----

### **第五章：状态管理 (State Management)**

采用Qt原生的**Model/View（模型/视图）架构**。创建一个中心的`CompetitionModel`类作为“单一数据源”，该模型将与`persistence`模块交互，从本地SQLite数据库读取和写入数据。UI视图绑定到此模型，当模型数据变化时，UI将自动更新。

-----

### **第六章：API 集成 (API Integration)**

创建一个专用的 `ApiClient` 单例类，作为所有网络请求的统一出口。它负责构建请求、检测网络状态，并在网络可用时，将本地数据库中标记为“待同步”的操作队列发送到服务器。

-----

### **第七章：路由管理 (Routing)**

使用Qt原生的 **`QStackedWidget`** 控件管理主窗口内的核心视图切换（如“赛事选择视图”和“主计分视图”）。

-----

### **第八章：样式指南 (Styling Guidelines)**

采用 **Qt样式表 (QSS)** 进行界面美化，样式规则存储在外部`theme.qss`文件中，实现样式与逻辑分离。

-----

### **第九章：测试要求 (Testing Requirements)**

必须包含针对`RulesEngine`和`DatabaseManager`的高覆盖率**单元测试**，以及验证API同步逻辑的**集成测试**。

-----

### **第十章：环境配置 (Environment Configuration)**

使用与可执行文件同目录的 `config.ini` 文件来管理外部配置（如服务器API地址），通过Qt的 `QSettings` 类进行读取。

-----

### **第十一章：前端开发者标准 (Frontend Developer Standards)**

所有开发工作必须遵守“单一数据源 (Model)”、“解耦通信 (ApiClient)”、“配置外部化”和“核心逻辑必测”等关键规则。

-----

### **第十二章：离线模式与数据同步架构**

#### **12.1 核心逻辑**

应用采用“**离线优先 (Offline-First)**”工作流。用户的每一次记分操作，都**首先**写入本地的SQLite数据库并立即更新UI，此操作不依赖于网络连接。

#### **12.2 数据同步流程**

1.  **操作队列:** 任何数据变更操作，在写入本地数据库的同时，会被记录到一个“待同步”队列中。
2.  **网络状态检测:** `ApiClient` 负责检测与服务器的网络连接状态。
3.  **自动同步:** 一旦网络连接恢复，`ApiClient` 将自动处理“待同步”队列，按顺序将本地操作发送给服务器。
4.  **状态反馈:** UI界面需有图标向用户展示当前的同步状态（已同步、同步中、离线/同步失败）。

#### **12.3 冲突解决策略 (MVP)**

为简化MVP复杂度，采用“**客户端优先 (Client-Wins)**”策略。网络恢复同步时，本地的数据将被视为最权威的版本，并覆盖服务器上的相应数据。

