# Story 1.1: 项目基础、本地数据库与API连接

## Status
done

## Story
**作为一个** 开发人员,  
**我想要** 搭建好一个基础的Qt 6项目，并初始化本地SQLite数据库，同时创建一个能连接到服务器API的服务模块,  
**以便于** 我有一个稳定的**离线优先**应用基础。

## Acceptance Criteria
1. 一个空白的、可运行的Qt 6项目已创建。
2. 应用首次启动时，能自动在本地创建一个`competition_data.sqlite`数据库文件及所需的数据表。
3. 项目中已建立一个API服务模块（ApiClient）。
4. 该服务模块能成功调用服务器的一个测试接口并接收到成功的回应。
5. API地址等信息通过配置文件管理。

## Tasks / Subtasks

- [ ] **Task 1: 创建Qt 6项目结构** (AC: 1)
  - [ ] 创建CMakeLists.txt主构建文件
  - [ ] 创建src目录和main.cpp入口文件
  - [ ] 配置Qt 6.9.1依赖 (Core, Widgets, SQL, Network)
  - [ ] 创建基础目录结构 (api/, persistence/, utils/, ui/, core/, models/)
  - [ ] 验证项目能够编译和运行

- [ ] **Task 2: 实现DatabaseManager类** (AC: 2)
  - [ ] 创建src/persistence/database_manager.h和database_manager.cpp
  - [ ] 实现单例模式的DatabaseManager类
  - [ ] 实现initialize()方法进行数据库文件创建
  - [ ] 创建所有必需数据表 (competitions, athletes, height_settings, attempt_records, sync_queue, schema_version)
  - [ ] 添加数据库连接错误处理和日志记录

- [ ] **Task 3: 实现ConfigManager类** (AC: 5)
  - [ ] 创建src/utils/config_manager.h和config_manager.cpp
  - [ ] 使用QSettings管理配置文件 (config.ini)
  - [ ] 实现API基础URL、超时、重试等配置项管理
  - [ ] 提供配置项的getter/setter方法

- [ ] **Task 4: 实现ApiClient类** (AC: 3, 4)
  - [ ] 创建src/api/api_client.h和api_client.cpp
  - [ ] 实现单例模式的ApiClient类
  - [ ] 使用QNetworkAccessManager处理HTTP请求
  - [ ] 实现网络状态检测功能
  - [ ] 创建测试端点调用功能 (如/api/v1/health)
  - [ ] 添加请求构建和响应处理方法

- [ ] **Task 5: 集成测试和验证** (AC: 1-5)
  - [ ] 创建基础的单元测试文件
  - [ ] 测试DatabaseManager数据库初始化
  - [ ] 测试ConfigManager配置读写
  - [ ] 测试ApiClient网络连接
  - [ ] 验证应用启动流程完整性

## Dev Notes

### 技术栈和版本要求
[Source: architecture/tech-stack.md#技术栈选型表]
- **核心框架**: Qt 6.9.1 - 构建整个桌面应用的用户界面、逻辑和平台集成
- **编程语言**: C++17 - Qt的原生语言，提供最高性能和完整的框架API访问
- **构建工具**: CMake 3.2x (最新稳定版) - 现代C++和Qt 6项目的标准构建系统
- **本地数据库**: SQLite3 (与Qt 6捆绑) - 轻量、稳定、文件式、与Qt的SQL模块原生集成
- **网络通信**: QNetworkAccessManager (与Qt 6.9.1捆绑) - Qt中执行网络请求的标准、集成化方案

### 项目结构要求
[Source: architecture/source-tree.md#目录结构]
创建以下关键目录和文件：
```
├── CMakeLists.txt              # 主CMake构建配置文件
├── src/
│   ├── main.cpp                # 应用程序主入口点
│   ├── api/
│   │   ├── api_client.h
│   │   └── api_client.cpp
│   ├── persistence/
│   │   ├── database_manager.h
│   │   └── database_manager.cpp
│   └── utils/
│       ├── config_manager.h
│       └── config_manager.cpp
├── config.ini                  # 配置文件模板
```

### 数据库初始化要求
[Source: architecture/database-schema.md#数据库文件]
- **文件名**: `competition_data.sqlite`
- **位置**: 应用程序数据目录
- **编码**: UTF-8
- **版本管理**: 通过 `schema_version` 表管理

必须创建的数据表：
- competitions (比赛表)
- athletes (运动员表) 
- height_settings (高度设置表)
- attempt_records (试跳记录表)
- sync_queue (同步队列表)
- schema_version (架构版本表)

### API客户端实现要求
[Source: architecture/rest-api-spec.md#ApiClient核心实现]
ApiClient必须实现：
- 单例模式设计
- 网络状态检测 (isOnline())
- 基础请求构建 (buildRequest())
- 响应处理 (handleResponse())
- 信号槽通信机制

### 代码规范要求
[Source: architecture/coding-standards.md#命名约定]
- **类名**: PascalCase (如 `DatabaseManager`)
- **文件名**: snake_case (如 `database_manager.h`)
- **函数名**: camelCase (如 `initialize()`)
- **成员变量**: m_camelCase (如 `m_database`)
- **信号槽连接**: 使用新式语法 `connect(sender, &Class::signal, receiver, &Class::slot)`

### 特殊技术约束
[Source: architecture/coding-standards.md#关键开发规则]
- **SQL查询**: 必须使用参数化查询防止SQL注入
- **事务管理**: 数据库写操作必须在事务中进行
- **错误处理**: 所有数据库操作必须检查返回值和错误状态
- **配置访问**: 通过QSettings统一管理，不允许硬编码配置
- **网络错误处理**: 网络请求必须包含超时和重试机制

### 架构原则
[Source: architecture/tech-stack.md#核心设计原则]
- **离线优先**: 所有功能首先在本地工作，网络同步为辅助功能
- **模块化设计**: 各功能模块解耦，便于测试和维护
- **原生集成**: 优先使用Qt原生组件，最小化外部依赖

## Testing

### 测试框架要求
[Source: architecture/testing-strategy.md#核心测试框架]
- **单元测试**: Qt Test (与Qt 6.9.1捆绑)
- **测试文件命名**: `test_<类名>.cpp`
- **测试位置**: `tests/unit/` 目录

### 必需测试用例
- **DatabaseManager测试**:
  - 测试数据库连接和初始化
  - 测试表创建和架构版本管理
  - 测试错误处理

- **ConfigManager测试**:
  - 测试配置文件读写
  - 测试默认值处理

- **ApiClient测试**:
  - 测试网络状态检测
  - 测试基础HTTP请求功能
  - 使用Mock服务器进行集成测试

### 测试数据管理
[Source: architecture/testing-strategy.md#集成测试规范]
- 数据库操作使用内存SQLite数据库
- 网络操作使用模拟服务器或离线模式
- 测试数据清理必须在每个测试后执行

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-01-XX | 1.0 | Initial story creation | Bob (Scrum Master) |