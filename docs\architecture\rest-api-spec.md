# API集成规范

## API客户端架构

创建一个专用的 `ApiClient` 单例类，作为所有网络请求的统一出口。它负责构建请求、检测网络状态，并在网络可用时，将本地数据库中标记为"待同步"的操作队列发送到服务器。

### ApiClient 核心实现

```cpp
class ApiClient : public QObject
{
    Q_OBJECT

public:
    static ApiClient* instance();
    
    // 网络状态
    bool isOnline() const;
    void checkNetworkStatus();
    
    // 比赛API
    void fetchCompetitions();
    void fetchCompetition(int competitionId);
    void uploadAttemptRecord(const AttemptRecord &record);
    
    // 同步API
    void processSyncQueue();
    void syncPendingData();

signals:
    // 响应信号
    void competitionsReceived(const QJsonArray &competitions);
    void competitionReceived(const QJsonObject &competition);
    void attemptUploaded(int recordId, bool success);
    
    // 状态信号
    void networkStatusChanged(bool isOnline);
    void syncProgress(int completed, int total);
    void syncCompleted();

private slots:
    void onNetworkReply();
    void onNetworkError(QNetworkReply::NetworkError error);
    void onSslErrors(const QList<QSslError> &errors);

private:
    explicit ApiClient(QObject *parent = nullptr);
    
    // 请求构建
    QNetworkRequest buildRequest(const QString &endpoint);
    void setAuthHeaders(QNetworkRequest &request);
    
    // 响应处理
    void handleResponse(QNetworkReply *reply);
    void processCompetitionsResponse(const QJsonObject &response);
    void processAttemptResponse(const QJsonObject &response);
    
    QNetworkAccessManager *m_networkManager;
    QString m_baseUrl;
    QString m_authToken;
    bool m_isOnline;
    QTimer *m_networkCheckTimer;
    
    static ApiClient *s_instance;
};
```

## API端点规范

### 基础配置

```cpp
// 配置管理
class ApiConfig
{
public:
    static QString baseUrl();
    static int requestTimeout();
    static int maxRetries();
    static QString apiVersion();
    
private:
    static const QString DEFAULT_BASE_URL;
    static const int DEFAULT_TIMEOUT = 30000;  // 30秒
    static const int DEFAULT_MAX_RETRIES = 3;
};
```

### 端点定义

#### 1. 获取比赛列表

```
GET /api/v1/competitions
```

**请求头**:
```
Authorization: Bearer {token}
Content-Type: application/json
```

**响应示例**:
```json
{
    "status": "success",
    "data": [
        {
            "id": 1,
            "name": "2024年全国跳高锦标赛",
            "date": "2024-08-15T10:00:00Z",
            "venue": "国家体育场",
            "status": "not_started",
            "starting_height": 150,
            "height_increment": 3
        }
    ]
}
```

#### 2. 获取比赛详情

```
GET /api/v1/competitions/{id}
```

**响应示例**:
```json
{
    "status": "success",
    "data": {
        "id": 1,
        "name": "2024年全国跳高锦标赛",
        "date": "2024-08-15T10:00:00Z",
        "venue": "国家体育场",
        "status": "in_progress",
        "starting_height": 150,
        "height_increment": 3,
        "athletes": [
            {
                "id": 101,
                "name": "张三",
                "number": 1,
                "team": "北京队",
                "personal_best": 210,
                "season_best": 205
            }
        ],
        "height_sequence": [150, 153, 156, 159, 162, 165]
    }
}
```

#### 3. 上传试跳记录

```
POST /api/v1/attempt-records
```

**请求体**:
```json
{
    "athlete_id": 101,
    "height": 156,
    "attempt_number": 1,
    "result": "success",
    "timestamp": "2024-08-15T14:30:00Z"
}
```

**响应示例**:
```json
{
    "status": "success",
    "data": {
        "id": 1001,
        "athlete_id": 101,
        "height": 156,
        "attempt_number": 1,
        "result": "success",
        "timestamp": "2024-08-15T14:30:00Z",
        "created_at": "2024-08-15T14:30:05Z"
    }
}
```

#### 4. 批量同步数据

```
POST /api/v1/sync
```

**请求体**:
```json
{
    "operations": [
        {
            "type": "create_attempt",
            "data": {
                "athlete_id": 101,
                "height": 159,
                "attempt_number": 2,
                "result": "failure",
                "timestamp": "2024-08-15T14:35:00Z"
            }
        },
        {
            "type": "update_athlete",
            "data": {
                "id": 101,
                "status": "eliminated"
            }
        }
    ]
}
```

## 错误处理

### 错误码规范

```cpp
enum class ApiErrorCode {
    Success = 0,
    NetworkError = 1000,
    AuthenticationFailed = 1001,
    InvalidRequest = 1002,
    ServerError = 1003,
    DataConflict = 1004,
    RateLimitExceeded = 1005
};

class ApiError
{
public:
    ApiErrorCode code;
    QString message;
    QJsonObject details;
    
    static ApiError fromResponse(const QJsonObject &response);
    QString toString() const;
};
```

### 重试机制

```cpp
class RetryPolicy
{
public:
    explicit RetryPolicy(int maxRetries = 3);
    
    bool shouldRetry(const ApiError &error) const;
    int nextRetryDelay(int attempt) const;  // 指数退避
    
private:
    int m_maxRetries;
    QSet<ApiErrorCode> m_retryableCodes;
};
```

## 网络状态检测

### 连接检测器

```cpp
class NetworkDetector : public QObject
{
    Q_OBJECT

public:
    explicit NetworkDetector(QObject *parent = nullptr);
    
    bool isConnected() const;
    void startMonitoring();
    void stopMonitoring();

signals:
    void connectionChanged(bool isConnected);

private slots:
    void checkConnection();
    void onPingResponse();

private:
    void sendPingRequest();
    
    QNetworkAccessManager *m_manager;
    QTimer *m_checkTimer;
    bool m_isConnected;
    QString m_pingUrl;
};
```

## 数据同步流程

### 同步管理器

```cpp
class SyncManager : public QObject
{
    Q_OBJECT

public:
    explicit SyncManager(QObject *parent = nullptr);
    
    void startAutoSync();
    void stopAutoSync();
    void forceSyncNow();

signals:
    void syncStarted();
    void syncProgress(int completed, int total);
    void syncCompleted(bool success);
    void syncError(const QString &message);

private slots:
    void onNetworkAvailable();
    void processSyncQueue();
    void onSyncItemCompleted(int itemId, bool success);

private:
    bool processSyncItem(const SyncQueueEntry &item);
    void markSyncCompleted(int itemId);
    void markSyncFailed(int itemId, const QString &error);
    
    ApiClient *m_apiClient;
    DatabaseManager *m_dbManager;
    QTimer *m_syncTimer;
    bool m_isSyncing;
};
```

## 认证管理

### 令牌管理

```cpp
class AuthManager : public QObject
{
    Q_OBJECT

public:
    static AuthManager* instance();
    
    bool isAuthenticated() const;
    QString getAuthToken() const;
    void setAuthToken(const QString &token);
    void refreshToken();
    void logout();

signals:
    void authenticationChanged(bool isAuthenticated);
    void tokenRefreshed();
    void authenticationFailed();

private:
    QString m_authToken;
    QDateTime m_tokenExpiry;
    QTimer *m_refreshTimer;
};
```

## 配置管理

### API配置

```cpp
class ApiConfiguration
{
public:
    static void loadFromConfig();
    static void saveToConfig();
    
    // 基础设置
    static QString baseUrl();
    static void setBaseUrl(const QString &url);
    
    // 超时设置
    static int requestTimeout();
    static void setRequestTimeout(int timeout);
    
    // 重试设置
    static int maxRetries();
    static void setMaxRetries(int retries);
    
    // 同步设置
    static int syncInterval();
    static void setSyncInterval(int interval);

private:
    static QString s_baseUrl;
    static int s_requestTimeout;
    static int s_maxRetries;
    static int s_syncInterval;
};
```

## 安全考虑

### HTTPS强制
- 所有API请求必须使用HTTPS
- 验证服务器证书
- 处理SSL错误

### 数据验证
- 验证服务器响应格式
- 检查数据完整性
- 防止恶意数据注入

### 敏感信息保护
- 令牌安全存储
- 不在日志中记录敏感信息
- 及时清理内存中的敏感数据