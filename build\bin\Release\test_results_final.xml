<?xml version="1.0" encoding="UTF-8" ?>
<testsuite name="TestDatabaseManager" timestamp="2025&#x002D;08&#x002D;07T09:54:16" hostname="BD51" tests="10" failures="1" errors="0" skipped="0" time="0.066">
  <properties>
    <property name="QTestVersion" value="6.9.1"/>
    <property name="QtVersion" value="6.9.1"/>
    <property name="QtBuild" value="Qt 6.9.1 (x86_64&#x002D;little_endian&#x002D;llp64 shared (dynamic) release build; by MSVC 2022)"/>
  </properties>
  <testcase name="initTestCase" classname="TestDatabaseManager" time="0.007">
    <system-out>
      <![CDATA[Test database path: "C:/Users/<USER>/AppData/Local/Temp/test_database_manager-vBoXfC/test_competition_data.sqlite"]]>
    </system-out>
  </testcase>
  <testcase name="testSingletonPattern" classname="TestDatabaseManager" time="0.001">
    <system-out>
      <![CDATA[Starting testSingletonPattern...]]>
      <![CDATA[Got first instance: DatabaseManager(0x22181ee1b20)]]>
      <![CDATA[Got second instance: DatabaseManager(0x22181ee1b20)]]>
      <![CDATA[testSingletonPattern completed successfully]]>
    </system-out>
  </testcase>
  <testcase name="testDatabaseInitialization" classname="TestDatabaseManager" time="0.053">
    <system-out>
      <![CDATA[Starting testDatabaseInitialization...]]>
      <![CDATA[Got DatabaseManager instance: DatabaseManager(0x22181ee1b20)]]>
      <![CDATA[Calling initialize()...]]>
      <![CDATA[Initializing DatabaseManager...]]>
      <![CDATA[Database file path: "C:/Users/<USER>/AppData/Roaming/test_database_manager/competition_data.sqlite"]]>
      <![CDATA[DatabaseManager initialized successfully]]>
      <![CDATA[Database path: "C:/Users/<USER>/AppData/Roaming/test_database_manager/competition_data.sqlite"]]>
      <![CDATA[Initialize result: true]]>
      <![CDATA[Checking connection...]]>
      <![CDATA[Connection status: true]]>
      <![CDATA[Getting database path...]]>
      <![CDATA[Database path: "C:/Users/<USER>/AppData/Roaming/test_database_manager/competition_data.sqlite"]]>
      <![CDATA[File exists: true]]>
      <![CDATA[Database initialized successfully at: "C:/Users/<USER>/AppData/Roaming/test_database_manager/competition_data.sqlite"]]>
      <![CDATA[testDatabaseInitialization completed successfully]]>
    </system-out>
  </testcase>
  <testcase name="testTableCreation" classname="TestDatabaseManager" time="0.001">
    <system-out>
      <![CDATA[All required tables created successfully]]>
    </system-out>
  </testcase>
  <testcase name="testSchemaVersionManagement" classname="TestDatabaseManager" time="0.000">
    <system-out>
      <![CDATA[Current schema version: 1]]>
      <![CDATA[Database schema is up to date (version 1 )]]>
    </system-out>
  </testcase>
  <testcase name="testTransactionManagement" classname="TestDatabaseManager" time="0.000"/>
  <testcase name="testErrorHandling" classname="TestDatabaseManager" time="0.001">
    <failure type="fail" message="&apos;!lastError.isEmpty()&apos; returned FALSE. ()"/>
  </testcase>
  <testcase name="testDatabasePath" classname="TestDatabaseManager" time="0.000">
    <system-out>
      <![CDATA[Database path: "C:/Users/<USER>/AppData/Roaming/test_database_manager/competition_data.sqlite"]]>
    </system-out>
  </testcase>
  <testcase name="testConnectionStatus" classname="TestDatabaseManager" time="0.000"/>
  <testcase name="cleanupTestCase" classname="TestDatabaseManager" time="0.000"/>
</testsuite>
