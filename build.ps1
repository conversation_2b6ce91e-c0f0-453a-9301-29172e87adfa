# High Jump Competition Management System - PowerShell Build Script
# This script helps build the project on Windows using PowerShell

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "High Jump Competition Management System" -ForegroundColor Cyan
Write-Host "PowerShell Build Script" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if CMake is available
try {
    $cmakeVersion = cmake --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ CMake found:" -ForegroundColor Green
        Write-Host $cmakeVersion[0] -ForegroundColor Gray
    }
} catch {
    Write-Host "✗ ERROR: CMake not found in PATH" -ForegroundColor Red
    Write-Host "Please install CMake from https://cmake.org/download/" -ForegroundColor Yellow
    Write-Host "or add it to your system PATH" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Check Qt environment
if (-not $env:CMAKE_PREFIX_PATH) {
    Write-Host "⚠ WARNING: CMAKE_PREFIX_PATH not set" -ForegroundColor Yellow
    Write-Host "Please set it to your Qt installation directory" -ForegroundColor Yellow
    Write-Host "Example: `$env:CMAKE_PREFIX_PATH = 'C:\Qt\6.9.1\msvc2019_64'" -ForegroundColor Gray
    Write-Host ""
    Write-Host "Common Qt installation paths:" -ForegroundColor Cyan
    Write-Host "- C:\Qt\6.9.1\msvc2019_64" -ForegroundColor Gray
    Write-Host "- C:\Qt\6.9.1\msvc2022_64" -ForegroundColor Gray
    Write-Host "- C:\Qt\6.9.1\mingw_64" -ForegroundColor Gray
    Write-Host ""
    
    $setQt = Read-Host "Do you want to set CMAKE_PREFIX_PATH now? (y/n)"
    if ($setQt -eq 'y' -or $setQt -eq 'Y') {
        $qtPath = Read-Host "Enter Qt installation path"
        if (Test-Path $qtPath) {
            $env:CMAKE_PREFIX_PATH = $qtPath
            Write-Host "✓ CMAKE_PREFIX_PATH set to: $qtPath" -ForegroundColor Green
        } else {
            Write-Host "✗ Invalid path: $qtPath" -ForegroundColor Red
            Read-Host "Press Enter to exit"
            exit 1
        }
    }
}

# Create build directory
if (-not (Test-Path "build")) {
    New-Item -ItemType Directory -Path "build" | Out-Null
    Write-Host "✓ Created build directory" -ForegroundColor Green
}

Set-Location "build"

# Configure with CMake
Write-Host ""
Write-Host "Configuring project with CMake..." -ForegroundColor Cyan
cmake .. -DCMAKE_BUILD_TYPE=Release
if ($LASTEXITCODE -ne 0) {
    Write-Host "✗ ERROR: CMake configuration failed" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Build the project
Write-Host ""
Write-Host "Building project..." -ForegroundColor Cyan
cmake --build . --config Release
if ($LASTEXITCODE -ne 0) {
    Write-Host "✗ ERROR: Build failed" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Build completed successfully!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "Executable location: build\bin\Release\high-jump-scorer.exe" -ForegroundColor Cyan
Write-Host ""

# Check if executable exists
$exePath = "bin\Release\high-jump-scorer.exe"
if (Test-Path $exePath) {
    Write-Host "✓ Executable found!" -ForegroundColor Green
    Write-Host "You can now run the application." -ForegroundColor Cyan
    Write-Host ""
    
    $runApp = Read-Host "Do you want to run the application now? (y/n)"
    if ($runApp -eq 'y' -or $runApp -eq 'Y') {
        Write-Host "Starting application..." -ForegroundColor Cyan
        Start-Process $exePath
    }
} else {
    Write-Host "⚠ WARNING: Executable not found in expected location" -ForegroundColor Yellow
    Write-Host "Please check the build output for any errors" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Build script completed." -ForegroundColor Cyan
Read-Host "Press Enter to exit" 