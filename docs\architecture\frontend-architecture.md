# 前端架构设计

## 状态管理

采用Qt原生的**Model/View（模型/视图）架构**。创建一个中心的`CompetitionModel`类作为"单一数据源"，该模型将与`persistence`模块交互，从本地SQLite数据库读取和写入数据。UI视图绑定到此模型，当模型数据变化时，UI将自动更新。

### 核心模型设计

```cpp
class CompetitionModel : public QAbstractTableModel
{
    Q_OBJECT

public:
    explicit CompetitionModel(QObject *parent = nullptr);
    
    // QAbstractTableModel interface
    int rowCount(const QModelIndex &parent = QModelIndex()) const override;
    int columnCount(const QModelIndex &parent = QModelIndex()) const override;
    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole) const override;
    QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const override;
    
    // 业务方法
    void loadCompetition(int competitionId);
    void recordAttempt(int athleteId, int height, AttemptResult result);
    
signals:
    void dataUpdated();
    void rankingChanged();

private:
    DatabaseManager *m_dbManager;
    QList<Athlete> m_athletes;
    QList<int> m_heights;
};
```

## 路由管理

使用Qt原生的 **`QStackedWidget`** 控件管理主窗口内的核心视图切换（如"赛事选择视图"和"主计分视图"）。

### 视图管理器

```cpp
class ViewManager : public QObject
{
    Q_OBJECT

public:
    enum ViewType {
        CompetitionSelection,
        MainScoring,
        ResultsView
    };
    
    explicit ViewManager(QStackedWidget *container, QObject *parent = nullptr);
    void switchToView(ViewType viewType);
    
private:
    QStackedWidget *m_container;
    QHash<ViewType, QWidget*> m_views;
};
```

## UI组件架构

### 主窗口结构

```cpp
class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void onCompetitionSelected(int competitionId);
    void onAttemptRecorded();
    void onExportRequested();

private:
    void setupUI();
    void setupMenuBar();
    void setupToolBar();
    void connectSignals();
    
    // UI组件
    QStackedWidget *m_centralStack;
    CompetitionSelectionView *m_selectionView;
    ScoringView *m_scoringView;
    
    // 模型
    CompetitionModel *m_competitionModel;
    
    // 管理器
    ViewManager *m_viewManager;
    ConfigManager *m_configManager;
};
```

### 自定义控件

#### 运动员成绩表格
```cpp
class AthleteTableWidget : public QTableView
{
    Q_OBJECT

public:
    explicit AthleteTableWidget(QWidget *parent = nullptr);
    void setCompetitionModel(CompetitionModel *model);

signals:
    void attemptRequested(int athleteId, int height);
    void cellFocusChanged(int athleteId, int height);

protected:
    void keyPressEvent(QKeyEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;

private:
    CompetitionModel *m_model;
    AthleteDelegate *m_delegate;
};
```

#### 高度列标题
```cpp
class HeightHeaderWidget : public QHeaderView
{
    Q_OBJECT

public:
    explicit HeightHeaderWidget(Qt::Orientation orientation, QWidget *parent = nullptr);
    
signals:
    void heightChangeRequested(int oldHeight, int newHeight);

protected:
    void paintSection(QPainter *painter, const QRect &rect, int logicalIndex) const override;
    void mousePressEvent(QMouseEvent *event) override;
};
```

## 样式管理

采用 **Qt样式表 (QSS)** 进行界面美化，样式规则存储在外部`theme.qss`文件中，实现样式与逻辑分离。

### 样式架构

```cpp
class ThemeManager : public QObject
{
    Q_OBJECT

public:
    enum Theme {
        Light,
        Dark,
        HighContrast
    };
    
    static ThemeManager* instance();
    void setTheme(Theme theme);
    Theme currentTheme() const;
    
signals:
    void themeChanged(Theme newTheme);

private:
    ThemeManager();
    void loadStyleSheet(const QString &fileName);
    
    Theme m_currentTheme;
    static ThemeManager *s_instance;
};
```

### 样式文件结构

```
resources/styles/
├── light_theme.qss         # 浅色主题
├── dark_theme.qss          # 深色主题
├── high_contrast.qss       # 高对比度主题
└── common.qss              # 通用样式
```

## 事件处理架构

### 键盘快捷键管理

```cpp
class ShortcutManager : public QObject
{
    Q_OBJECT

public:
    explicit ShortcutManager(QWidget *parent = nullptr);
    void setupShortcuts();

signals:
    void successAttempt();      // 'O' 键
    void failedAttempt();       // 'X' 键
    void passAttempt();         // '-' 键
    void retireAttempt();       // 'R' 键

private:
    QWidget *m_parent;
    QHash<QString, QShortcut*> m_shortcuts;
};
```

### 信号槽连接架构

```cpp
// 在MainWindow中的信号槽连接
void MainWindow::connectSignals()
{
    // 模型到视图的连接
    connect(m_competitionModel, &CompetitionModel::dataUpdated,
            m_scoringView, &ScoringView::updateDisplay);
    
    // 快捷键到业务逻辑的连接
    connect(m_shortcutManager, &ShortcutManager::successAttempt,
            this, &MainWindow::recordSuccessAttempt);
    
    // API到模型的连接
    connect(m_apiClient, &ApiClient::competitionDataReceived,
            m_competitionModel, &CompetitionModel::loadFromJson);
}
```

## 响应式布局

### 自适应界面设计

```cpp
class ResponsiveLayout : public QVBoxLayout
{
    Q_OBJECT

public:
    explicit ResponsiveLayout(QWidget *parent = nullptr);

protected:
    void addItem(QLayoutItem *item) override;
    void setGeometry(const QRect &rect) override;

private:
    void adjustForScreenSize();
    QSize m_lastSize;
};
```

## 国际化支持

### 多语言架构

```cpp
class LanguageManager : public QObject
{
    Q_OBJECT

public:
    enum Language {
        Chinese,
        English,
        Japanese
    };
    
    static LanguageManager* instance();
    void setLanguage(Language lang);
    Language currentLanguage() const;

signals:
    void languageChanged(Language newLang);

private:
    QTranslator *m_translator;
    Language m_currentLang;
};
```

### 翻译文件组织

```
resources/translations/
├── highjump_zh_CN.ts       # 中文翻译
├── highjump_en_US.ts       # 英文翻译
└── highjump_ja_JP.ts       # 日文翻译
```