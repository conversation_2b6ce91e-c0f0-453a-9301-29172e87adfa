# 第二章：需求 (Requirements)

## 功能性需求 (Functional Requirements)
* **FR1: 数据同步:** 系统必须能通过HTTP API从服务器获取指定的赛事信息和运动员名单。
* **FR2: 成绩录入:** 系统必须提供一个界面，供记分员对当前运动员的单次试跳结果进行录入。
* **FR3: 结果类型:** 成绩录入操作必须支持“成功(o)”、“失败(x)”、“免跳(-)”以及“弃权/退赛(r)”四种核心结果。
* **FR4: 规则引擎-状态管理:** 系统必须能根据录入结果，并遵循官方规则，准确地跟踪和管理每位运动员的当前状态（包括：在**特定高度**的试跳次数、是否已被淘汰等）。
* **FR5: 规则引擎-实时排名:** 系统必须根据所有运动员的实时状态，自动、即时地计算并更新全场排名。
* **FR6: 成绩导出:** 系统必须支持将最终的官方成绩单导出为PDF和CSV两种文件格式。
* **FR7: 获取高度设置:** 系统必须在比赛开始时，从服务器**获取预先配置好**的起跳高度和升杆高度序列，并以此为依据自动提示每一轮的高度。
* **FR8: 赛后挑战:** 系统必须支持已获得冠军的运动员，在比赛结束后继续挑战新高度。

## 非功能性需求 (Non-Functional Requirements)
* **NFR1: 性能:** 所有核心操作（如录入成绩、更新排名）的界面响应必须是即时的（低于500毫秒），确保在高压比赛环境下的流畅体验。
* **NFR2: 准确性:** 规则引擎的所有计算（包括排名、淘汰判断等）必须与官方规则手册100%一致，不容许任何偏差。
* **NFR3: 易用性:** 界面设计必须高度直观，为接受过培训的记分员最大程度地降低操作复杂度和心智负担。
* **NFR4: 平台兼容性:** 应用程序必须能在 Windows 10 及以上版本的操作系统上稳定运行，主要支持x86架构，并尽可能兼容ARM64。
* **NFR5: 集成安全性:** 与服务器的所有数据通信都必须通过安全的HTTPS连接进行。
* **NFR6: 数据权威性:** 服务器端为所有赛事设置数据（包括高度序列）的唯一权威来源。客户端对该数据只读不改。