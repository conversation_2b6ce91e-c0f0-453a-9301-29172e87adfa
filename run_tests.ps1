# High Jump Competition Management System - Test Runner Script
# This script runs unit tests with proper Qt environment setup

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "High Jump Competition Management System" -ForegroundColor Cyan
Write-Host "Test Runner with Qt Environment Setup" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Store original location
$originalLocation = Get-Location

# Check if build directory exists
if (-not (Test-Path "build")) {
    Write-Host "✗ ERROR: Build directory not found" -ForegroundColor Red
    Write-Host "Please run build.ps1 first to build the project" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Set up Qt environment for tests
$testDir = "build\bin\Release"

# Change to Release directory where Qt DLLs are located
if (Test-Path $testDir) {
    Set-Location $testDir
    Write-Host "✓ Changed to Release directory: $(Get-Location)" -ForegroundColor Green

    # Set Qt plugin path to current directory
    $env:QT_PLUGIN_PATH = $PWD.Path
    $env:QT_DEBUG_PLUGINS = "0"
    Write-Host "✓ Set Qt Plugin Path: $env:QT_PLUGIN_PATH" -ForegroundColor Green

    # Check for required Qt SQLite files
    $qtFiles = @("qsqlite.dll", "sqldrivers\qsqlite.dll")
    foreach ($file in $qtFiles) {
        if (Test-Path $file) {
            Write-Host "✓ Found Qt file: $file" -ForegroundColor Green
        } else {
            Write-Host "⚠ WARNING: Qt file not found: $file" -ForegroundColor Yellow
        }
    }
    Write-Host ""
} else {
    Write-Host "✗ ERROR: Release directory not found: $testDir" -ForegroundColor Red
    Write-Host "Please build the project in Release mode first" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Define core test executables (required)
$coreTestFiles = @(
    "test_database_manager.exe",
    "test_config_manager.exe",
    "test_api_client.exe"
)

# Define optional test executables
$optionalTestFiles = @(
    "test_e2e_workflow.exe"
)

Write-Host "Running core unit tests..." -ForegroundColor Cyan
Write-Host ""

$allCoreTestsPassed = $true
$coreTestsRun = 0
$coreTestsPassed = 0

# Run core tests (required)
foreach ($testFile in $coreTestFiles) {
    if (Test-Path $testFile) {
        $coreTestsRun++
        Write-Host "Running $testFile..." -ForegroundColor Yellow
        try {
            # Run test executable directly from Release directory
            $process = Start-Process -FilePath ".\$testFile" -Wait -PassThru -NoNewWindow
            if ($process.ExitCode -eq 0) {
                Write-Host "✓ $testFile passed" -ForegroundColor Green
                $coreTestsPassed++
            } else {
                Write-Host "✗ $testFile failed with exit code $($process.ExitCode)" -ForegroundColor Red
                $allCoreTestsPassed = $false
            }
        } catch {
            Write-Host "✗ $testFile failed with exception: $($_.Exception.Message)" -ForegroundColor Red
            $allCoreTestsPassed = $false
        }
        Write-Host ""
    } else {
        Write-Host "✗ ERROR: Required test $testFile not found" -ForegroundColor Red
        $allCoreTestsPassed = $false
    }
}

# Run optional tests (don't affect overall result)
foreach ($testFile in $optionalTestFiles) {
    if (Test-Path $testFile) {
        Write-Host "Running $testFile..." -ForegroundColor Yellow
        try {
            $process = Start-Process -FilePath ".\$testFile" -Wait -PassThru -NoNewWindow
            if ($process.ExitCode -eq 0) {
                Write-Host "✓ $testFile passed" -ForegroundColor Green
            } else {
                Write-Host "✗ $testFile failed with exit code $($process.ExitCode)" -ForegroundColor Red
            }
        } catch {
            Write-Host "✗ $testFile failed with exception: $($_.Exception.Message)" -ForegroundColor Red
        }
        Write-Host ""
    } else {
        Write-Host "⚠ INFO: Optional test $testFile not found (skipping)" -ForegroundColor Yellow
    }
}

# Restore original location
Set-Location $originalLocation

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Test Results Summary" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

if ($allCoreTestsPassed) {
    Write-Host "✓ All core tests passed! ($coreTestsPassed/$coreTestsRun)" -ForegroundColor Green
    Write-Host "✓ High Jump Competition Management System core functionality verified" -ForegroundColor Green
    Write-Host ""
    Write-Host "Core test results:" -ForegroundColor White
    Write-Host "- Database Manager: ✓ PASSED" -ForegroundColor Green
    Write-Host "- Config Manager: ✓ PASSED" -ForegroundColor Green
    Write-Host "- API Client: ✓ PASSED" -ForegroundColor Green
    Write-Host ""
    Write-Host "🎉 System is ready for production!" -ForegroundColor Green
} else {
    Write-Host "✗ Some core tests failed ($coreTestsPassed/$coreTestsRun passed)" -ForegroundColor Red
    Write-Host "Please check the test output above for details" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Common issues:" -ForegroundColor Yellow
    Write-Host "- Qt SQLite plugin not found (check sqldrivers folder)" -ForegroundColor Gray
    Write-Host "- Missing Qt DLLs in Release directory" -ForegroundColor Gray
    Write-Host "- Database file permissions or path issues" -ForegroundColor Gray
}
Write-Host "========================================" -ForegroundColor Cyan

Read-Host "Press Enter to exit"