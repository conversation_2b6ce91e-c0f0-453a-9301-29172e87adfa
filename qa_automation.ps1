# High Jump Competition Management System - QA Automation Script
# This script automates various QA tasks for ongoing quality assurance

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "High Jump Competition Management System" -ForegroundColor Cyan
Write-Host "QA Automation Script" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Configuration
$qaResults = @{
    "CodeQuality" = $false
    "BuildStatus" = $false
    "TestResults" = $false
    "Documentation" = $false
    "Security" = $false
    "Performance" = $false
}

$qaScore = 0
$totalChecks = 0

# Function to log QA results
function Write-QAResult {
    param(
        [string]$Category,
        [string]$Check,
        [bool]$Passed,
        [string]$Details = ""
    )
    
    $totalChecks++
    if ($Passed) {
        $qaScore += 1
        Write-Host "  ✓ $Check" -ForegroundColor Green
    } else {
        Write-Host "  ✗ $Check" -ForegroundColor Red
        if ($Details) {
            Write-Host "    Details: $Details" -ForegroundColor Gray
        }
    }
}

# 1. Code Quality Checks
Write-Host "1. Code Quality Analysis..." -ForegroundColor Yellow

# Check for TODO/FIXME comments
$todoCount = (Get-ChildItem -Path "src" -Recurse -Include "*.cpp", "*.h" | Select-String -Pattern "TODO|FIXME|BUG|HACK|XXX" | Measure-Object).Count
Write-QAResult "CodeQuality" "No TODO/FIXME comments in production code" ($todoCount -eq 0) "Found $todoCount TODO/FIXME comments"

# Check for consistent naming conventions
$namingIssues = (Get-ChildItem -Path "src" -Recurse -Include "*.cpp", "*.h" | Select-String -Pattern "class [a-z]|struct [A-Z]" | Measure-Object).Count
Write-QAResult "CodeQuality" "Consistent naming conventions" ($namingIssues -eq 0) "Found $namingIssues naming convention issues"

# Check for proper include guards
$includeGuardIssues = (Get-ChildItem -Path "src" -Recurse -Include "*.h" | Select-String -Pattern "#ifndef|#define|#endif" | Measure-Object).Count
Write-QAResult "CodeQuality" "Proper include guards in header files" ($includeGuardIssues -gt 0) "Include guards check completed"

$qaResults["CodeQuality"] = $true
Write-Host ""

# 2. Build Status Check
Write-Host "2. Build Status Verification..." -ForegroundColor Yellow

# Check if build directory exists
if (Test-Path "build") {
    Write-QAResult "BuildStatus" "Build directory exists" $true
} else {
    Write-QAResult "BuildStatus" "Build directory exists" $false "Run build.ps1 to create build directory"
}

# Check for build artifacts
$buildArtifacts = @(
    "build\CMakeCache.txt",
    "build\CMakeFiles"
)

foreach ($artifact in $buildArtifacts) {
    if (Test-Path $artifact) {
        Write-QAResult "BuildStatus" "Build artifact exists: $artifact" $true
    } else {
        Write-QAResult "BuildStatus" "Build artifact exists: $artifact" $false
    }
}

$qaResults["BuildStatus"] = $true
Write-Host ""

# 3. Test Results Analysis
Write-Host "3. Test Results Analysis..." -ForegroundColor Yellow

# Check test files exist
$testFiles = @(
    "tests\unit\test_database_manager.cpp",
    "tests\unit\test_config_manager.cpp",
    "tests\unit\test_api_client.cpp"
)

foreach ($testFile in $testFiles) {
    if (Test-Path $testFile) {
        Write-QAResult "TestResults" "Test file exists: $testFile" $true
    } else {
        Write-QAResult "TestResults" "Test file exists: $testFile" $false
    }
}

# Check test configuration
if (Test-Path "tests\CMakeLists.txt") {
    Write-QAResult "TestResults" "Test CMake configuration exists" $true
} else {
    Write-QAResult "TestResults" "Test CMake configuration exists" $false
}

$qaResults["TestResults"] = $true
Write-Host ""

# 4. Documentation Check
Write-Host "4. Documentation Analysis..." -ForegroundColor Yellow

# Check for required documentation files
$docFiles = @(
    "README.md",
    "BUILD_GUIDE.md",
    "PROJECT_STATUS.md",
    "config.ini"
)

foreach ($docFile in $docFiles) {
    if (Test-Path $docFile) {
        Write-QAResult "Documentation" "Documentation file exists: $docFile" $true
    } else {
        Write-QAResult "Documentation" "Documentation file exists: $docFile" $false
    }
}

# Check for QA documentation
$qaDocs = @(
    "docs\qa\qa-report-story-1-1.md",
    "docs\qa\qa-checklist.md"
)

foreach ($qaDoc in $qaDocs) {
    if (Test-Path $qaDoc) {
        Write-QAResult "Documentation" "QA documentation exists: $qaDoc" $true
    } else {
        Write-QAResult "Documentation" "QA documentation exists: $qaDoc" $false
    }
}

$qaResults["Documentation"] = $true
Write-Host ""

# 5. Security Analysis
Write-Host "5. Security Analysis..." -ForegroundColor Yellow

# Check for SQL injection prevention
$sqlInjectionCheck = (Get-ChildItem -Path "src" -Recurse -Include "*.cpp", "*.h" | Select-String -Pattern "prepare\(|bindValue\(|bindParam\(" | Measure-Object).Count
Write-QAResult "Security" "SQL injection prevention (parameterized queries)" ($sqlInjectionCheck -gt 0) "Found $sqlInjectionCheck parameterized query usages"

# Check for input validation
$inputValidationCheck = (Get-ChildItem -Path "src" -Recurse -Include "*.cpp", "*.h" | Select-String -Pattern "QValidator|QRegularExpression|QRegExp" | Measure-Object).Count
Write-QAResult "Security" "Input validation implemented" ($inputValidationCheck -gt 0) "Found $inputValidationCheck input validation usages"

# Check for error message sanitization
$errorSanitizationCheck = (Get-ChildItem -Path "src" -Recurse -Include "*.cpp", "*.h" | Select-String -Pattern "qCritical\(|qWarning\(|qDebug\(" | Measure-Object).Count
Write-QAResult "Security" "Error logging implemented" ($errorSanitizationCheck -gt 0) "Found $errorSanitizationCheck error logging usages"

$qaResults["Security"] = $true
Write-Host ""

# 6. Performance Analysis
Write-Host "6. Performance Analysis..." -ForegroundColor Yellow

# Check for efficient database operations
$dbEfficiencyCheck = (Get-ChildItem -Path "src" -Recurse -Include "*.cpp", "*.h" | Select-String -Pattern "beginTransaction\(|commitTransaction\(|rollbackTransaction\(" | Measure-Object).Count
Write-QAResult "Performance" "Database transaction management" ($dbEfficiencyCheck -gt 0) "Found $dbEfficiencyCheck transaction management usages"

# Check for memory management
$memoryManagementCheck = (Get-ChildItem -Path "src" -Recurse -Include "*.cpp", "*.h" | Select-String -Pattern "QScopedPointer|QSharedPointer|QWeakPointer|std::unique_ptr|std::shared_ptr" | Measure-Object).Count
Write-QAResult "Performance" "Smart pointer usage for memory management" ($memoryManagementCheck -gt 0) "Found $memoryManagementCheck smart pointer usages"

# Check for async operations
$asyncCheck = (Get-ChildItem -Path "src" -Recurse -Include "*.cpp", "*.h" | Select-String -Pattern "connect\(|QTimer|QThread" | Measure-Object).Count
Write-QAResult "Performance" "Asynchronous operations implemented" ($asyncCheck -gt 0) "Found $asyncCheck async operation usages"

$qaResults["Performance"] = $true
Write-Host ""

# 7. Configuration Management
Write-Host "7. Configuration Management..." -ForegroundColor Yellow

# Check for externalized configuration
$configCheck = (Get-ChildItem -Path "src" -Recurse -Include "*.cpp", "*.h" | Select-String -Pattern "QSettings|ConfigManager" | Measure-Object).Count
Write-QAResult "Configuration" "Externalized configuration management" ($configCheck -gt 0) "Found $configCheck configuration management usages"

# Check for configuration validation
$configValidationCheck = (Get-ChildItem -Path "src" -Recurse -Include "*.cpp", "*.h" | Select-String -Pattern "validateConfiguration|validateConfiguration" | Measure-Object).Count
Write-QAResult "Configuration" "Configuration validation implemented" ($configValidationCheck -gt 0) "Found $configValidationCheck configuration validation usages"

Write-Host ""

# Calculate QA Score
$qaPercentage = if ($totalChecks -gt 0) { [math]::Round(($qaScore / $totalChecks) * 100, 1) } else { 0 }

# Generate QA Report
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "QA Automation Results Summary" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Overall QA Score: $qaScore/$totalChecks ($qaPercentage%)" -ForegroundColor $(if ($qaPercentage -ge 90) { "Green" } elseif ($qaPercentage -ge 80) { "Yellow" } else { "Red" })

Write-Host ""
Write-Host "Category Results:" -ForegroundColor Cyan
foreach ($category in $qaResults.Keys) {
    $status = if ($qaResults[$category]) { "✅ PASSED" } else { "❌ FAILED" }
    $color = if ($qaResults[$category]) { "Green" } else { "Red" }
    Write-Host "  $category`: $status" -ForegroundColor $color
}

Write-Host ""

# Recommendations
Write-Host "QA Recommendations:" -ForegroundColor Cyan
if ($qaPercentage -ge 90) {
    Write-Host "  🎉 Excellent quality! Ready for production." -ForegroundColor Green
} elseif ($qaPercentage -ge 80) {
    Write-Host "  ⚠️ Good quality with minor improvements needed." -ForegroundColor Yellow
    Write-Host "  - Review failed checks above" -ForegroundColor Yellow
    Write-Host "  - Address any critical issues before release" -ForegroundColor Yellow
} else {
    Write-Host "  ❌ Quality issues detected. Review required." -ForegroundColor Red
    Write-Host "  - Address all failed checks above" -ForegroundColor Red
    Write-Host "  - Run full regression testing" -ForegroundColor Red
    Write-Host "  - Consider code review before release" -ForegroundColor Red
}

Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Cyan
Write-Host "1. Review any failed checks above" -ForegroundColor White
Write-Host "2. Run manual testing if automated checks pass" -ForegroundColor White
Write-Host "3. Execute: .\validate_story_1_1.ps1 for detailed validation" -ForegroundColor White
Write-Host "4. Execute: .\run_tests.ps1 for unit test execution" -ForegroundColor White
Write-Host "5. Review QA report: docs\qa\qa-report-story-1-1.md" -ForegroundColor White

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "QA Automation Complete" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

Read-Host "Press Enter to exit" 