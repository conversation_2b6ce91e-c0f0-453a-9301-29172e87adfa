# 第六章：史诗 1: MVP核心计分应用 - 详情 (修订版)

## 用户故事 (User Stories):

**故事 1.1: 项目基础、本地数据库与API连接**
* **作为一个** 开发人员, **我想要** 搭建好一个基础的Qt 6项目，**并初始化本地SQLite数据库**，同时创建一个能连接到服务器API的服务模块, **以便于** 我有一个稳定的**离线优先**应用基础。
* **验收标准:**
    1.  一个空白的、可运行的Qt 6项目已创建。
    2.  应用首次启动时，能自动在本地创建一个`competition_data.sqlite`数据库文件及所需的数据表。
    3.  项目中已建立一个API服务模块（ApiClient）。
    4.  该服务模块能成功调用服务器的一个测试接口并接收到成功的回应。
    5.  API地址等信息通过配置文件管理。

**故事 1.2: 赛事选择与主界面框架**
* **作为一个** 记分员, **我想要** 在启动应用后，能从服务器获取的列表中选择本次比赛, **以便于** 我能为当前执裁的赛事加载正确的数据。
* **验收标准:**
    1.  应用启动时，通过API获取可用的赛事列表。
    2.  赛事列表清晰地展示给用户。
    3.  用户可以选择其中一场比赛。
    4.  选择后，应用加载主窗口，其中包含计分表格和排名面板的UI框架。

**故事 1.3: 填充运动员与高度数据**
* **作为一个** 记分员, **我想要** 在选择赛事后，看到完整的运动员名单和预设的升杆高度序列被填充到主计分表格中, **以便于** 我对比赛全局有一个清晰的概览。
* **验收标准:**
    1.  选择赛事后，通过API获取该赛事的运动员名单和高度序列。
    2.  主计分表格的“行”被正确填充为运动员信息。
    3.  主计分表格的“列”被正确填充为高度序列。
    4.  第一个运动员在起跳高度的单元格被自动高亮。

**故事 1.4: 实现核心计分操作 (离线优先)**
* **作为一个** 记分员, **我想要** 使用简洁的按钮或键盘快捷键（o, x, -, r）来记录当前焦点运动员的试跳结果, **以便于** 结果能**即时、安全地保存在本地电脑上**，不受网络状态影响。
* **验收标准:**
    1.  界面上提供“成功(o)”, “失败(x)”, “免跳(-)”和“弃权(r)”四个核心操作按钮。
    2.  点击按钮或使用快捷键，能将结果**立即写入本地SQLite数据库**。
    3.  该操作同时被添加到一个本地的“待同步队列”中，并标记为“待处理”状态。
    4.  规则引擎能根据**本地数据库**的数据正确处理运动员状态并更新UI。
    5.  每次录入后，操作焦点会自动按规则移至下一个逻辑状态。

**故事 1.5: 实现实时排名计算**
* **作为一个** 记分员, **我想要** 在每一次试跳结果被录入后，排名面板能自动刷新, **以便于** 我随时都能看到最准确的实时赛况。
* **验收标准:**
    1.  任何一次成绩录入后，规则引擎会立刻重新计算所有运动员的排名。
    2.  排名规则遵循官方标准。
    3.  排名面板准确无误地显示每位选手的名次、姓名和最佳成绩。

**故事 1.6 (新增): 实现离线数据同步**
* **作为一个** 记分员, **我想要** 应用能够自动检测网络连接，并在连接恢复时，将我在离线期间保存的所有数据自动同步到服务器, **以便于** 官方记录总能保持最新，而无需我进行任何手动同步操作。
* **验收标准:**
    1.  ApiClient能够检测到与服务器的网络连接状态（在线/离线）。
    2.  当网络恢复在线时，ApiClient会自动处理本地数据库中的“待同步队列”。
    3.  队列中的每一项“待处理”操作都会被发送到对应的服务器API接口。
    4.  服务器成功返回后，该操作在本地队列中的状态被更新为“已同步”。
    5.  如果API调用失败，该操作保留在队列中，以便后续重试。
    6.  UI界面上有一个不打扰用户的状态图标，用于显示当前的同步状态。

**故事 1.7 (原1.6): 最终成绩导出**
* **作为一个** 记分员, **我想要** 在比赛完全结束后，能将最终的官方成绩单导出为PDF和CSV文件, **以便于** 我能进行成绩归档和分发。
* **验收标准:**
    1.  界面上提供一个“导出成绩”的按钮。
    2.  导出的数据**必须**来源于**本地SQLite数据库**。
    3.  导出的PDF格式清晰、专业。
    4.  导出的CSV文件包含所有结构化的原始数据。
    5.  导出的两种文件中的数据都与计分表格的最终状态完全一致。