
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Windows - 10.0.26100 - ARM64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.18+a338add32
      鐢熸垚鍚姩鏃堕棿涓?2025/8/6 18:16:47銆?
      
      鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\PROJECT\\HighJump\\build\\CMakeFiles\\3.31.6-msvc6\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)銆?
      PrepareForBuild:
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\鈥濄€?
        宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\CompilerIdCXX.tlog\\鈥濄€?
      InitializeBuildStatus:
        姝ｅ湪鍒涘缓鈥淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
      VcpkgTripletSelection:
        Using triplet "x64-windows" from "C:\\PROJECT\\HighJumpTerminal\\vcpkg\\installed\\x64-windows\\"
        Using normalized configuration "Release"
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /I"C:\\PROJECT\\HighJumpTerminal\\vcpkg\\installed\\x64-windows\\include" /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO /LIBPATH:"C:\\PROJECT\\HighJumpTerminal\\vcpkg\\installed\\x64-windows\\lib" /LIBPATH:"C:\\PROJECT\\HighJumpTerminal\\vcpkg\\installed\\x64-windows\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib "C:\\PROJECT\\HighJumpTerminal\\vcpkg\\installed\\x64-windows\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> C:\\PROJECT\\HighJump\\build\\CMakeFiles\\3.31.6-msvc6\\CompilerIdCXX\\CompilerIdCXX.exe
      AppLocalFromInstalled:
        pwsh.exe -ExecutionPolicy Bypass -noprofile -File "C:\\PROJECT\\HighJumpTerminal\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "C:\\PROJECT\\HighJump\\build\\CMakeFiles\\3.31.6-msvc6\\CompilerIdCXX\\CompilerIdCXX.exe" "C:\\PROJECT\\HighJumpTerminal\\vcpkg\\installed\\x64-windows\\bin" "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.write.1u.tlog" "Debug\\vcpkg.applocal.log"
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostarm64\\x64\\cl.exe
      FinalizeBuildStatus:
        姝ｅ湪鍒犻櫎鏂囦欢鈥淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
      宸插畬鎴愮敓鎴愰」鐩€淐:\\PROJECT\\HighJump\\build\\CMakeFiles\\3.31.6-msvc6\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
      
      宸叉垚鍔熺敓鎴愩€?
          0 涓鍛?
          0 涓敊璇?
      
      宸茬敤鏃堕棿 00:00:02.27
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        C:/PROJECT/HighJump/build/CMakeFiles/3.31.6-msvc6/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/PROJECT/HighJump/build/CMakeFiles/CMakeScratch/TryCompile-gvbqau"
      binary: "C:/PROJECT/HighJump/build/CMakeFiles/CMakeScratch/TryCompile-gvbqau"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/PROJECT/HighJump/build/CMakeFiles/CMakeScratch/TryCompile-gvbqau'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_5e310.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.18+a338add32
        鐢熸垚鍚姩鏃堕棿涓?2025/8/6 18:16:49銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-gvbqau\\cmTC_5e310.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_5e310.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-gvbqau\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_5e310.dir\\Debug\\cmTC_5e310.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_5e310.dir\\Debug\\cmTC_5e310.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_5e310.dir\\Debug\\cmTC_5e310.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "C:\\PROJECT\\HighJumpTerminal\\vcpkg\\installed\\x64-windows\\"
          Using normalized configuration "Debug"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /I"C:\\PROJECT\\HighJumpTerminal\\vcpkg\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_5e310.dir\\Debug\\\\" /Fd"cmTC_5e310.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35214 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /I"C:\\PROJECT\\HighJumpTerminal\\vcpkg\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_5e310.dir\\Debug\\\\" /Fd"cmTC_5e310.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\CommonExtensions\\Microsoft\\CMake\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-gvbqau\\Debug\\cmTC_5e310.exe" /INCREMENTAL /ILK:"cmTC_5e310.dir\\Debug\\cmTC_5e310.ilk" /NOLOGO /LIBPATH:"C:\\PROJECT\\HighJumpTerminal\\vcpkg\\installed\\x64-windows\\debug\\lib" /LIBPATH:"C:\\PROJECT\\HighJumpTerminal\\vcpkg\\installed\\x64-windows\\debug\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib "C:\\PROJECT\\HighJumpTerminal\\vcpkg\\installed\\x64-windows\\debug\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/PROJECT/HighJump/build/CMakeFiles/CMakeScratch/TryCompile-gvbqau/Debug/cmTC_5e310.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/PROJECT/HighJump/build/CMakeFiles/CMakeScratch/TryCompile-gvbqau/Debug/cmTC_5e310.lib" /MACHINE:X64  /machine:x64 cmTC_5e310.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_5e310.vcxproj -> C:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-gvbqau\\Debug\\cmTC_5e310.exe
        AppLocalFromInstalled:
          pwsh.exe -ExecutionPolicy Bypass -noprofile -File "C:\\PROJECT\\HighJumpTerminal\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "C:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-gvbqau\\Debug\\cmTC_5e310.exe" "C:\\PROJECT\\HighJumpTerminal\\vcpkg\\installed\\x64-windows\\debug\\bin" "cmTC_5e310.dir\\Debug\\cmTC_5e310.tlog\\cmTC_5e310.write.1u.tlog" "cmTC_5e310.dir\\Debug\\vcpkg.applocal.log"
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_5e310.dir\\Debug\\cmTC_5e310.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_5e310.dir\\Debug\\cmTC_5e310.tlog\\cmTC_5e310.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淐:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-gvbqau\\cmTC_5e310.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:01.91
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostArm64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostArm64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35214.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCXXSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/FindThreads.cmake:99 (CHECK_CXX_SOURCE_COMPILES)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/Qt/install-x64/lib/cmake/Qt6/Qt6Dependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "C:/Qt/install-x64/lib/cmake/Qt6/Qt6Config.cmake:168 (include)"
      - "CMakeLists.txt:14 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "C:/PROJECT/HighJump/build/CMakeFiles/CMakeScratch/TryCompile-jmn0s2"
      binary: "C:/PROJECT/HighJump/build/CMakeFiles/CMakeScratch/TryCompile-jmn0s2"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Qt/install-x64/lib/cmake/Qt6;C:/Qt/install-x64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/Qt/install-x64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'C:/PROJECT/HighJump/build/CMakeFiles/CMakeScratch/TryCompile-jmn0s2'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_ddb5d.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.18+a338add32
        鐢熸垚鍚姩鏃堕棿涓?2025/8/6 18:16:52銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jmn0s2\\cmTC_ddb5d.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_ddb5d.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jmn0s2\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_ddb5d.dir\\Debug\\cmTC_ddb5d.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_ddb5d.dir\\Debug\\cmTC_ddb5d.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_ddb5d.dir\\Debug\\cmTC_ddb5d.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "C:\\PROJECT\\HighJumpTerminal\\vcpkg\\installed\\x64-windows\\"
          Using normalized configuration "Debug"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /I"C:\\PROJECT\\HighJumpTerminal\\vcpkg\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /Fo"cmTC_ddb5d.dir\\Debug\\\\" /Fd"cmTC_ddb5d.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jmn0s2\\src.cxx"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35214 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /I"C:\\PROJECT\\HighJumpTerminal\\vcpkg\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /Fo"cmTC_ddb5d.dir\\Debug\\\\" /Fd"cmTC_ddb5d.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jmn0s2\\src.cxx"
          src.cxx
        C:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jmn0s2\\src.cxx(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥減thread.h鈥? No such file or directory [C:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jmn0s2\\cmTC_ddb5d.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淐:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jmn0s2\\cmTC_ddb5d.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淐:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jmn0s2\\cmTC_ddb5d.vcxproj鈥?榛樿鐩爣) (1) ->
        (ClCompile 鐩爣) -> 
          C:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jmn0s2\\src.cxx(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥減thread.h鈥? No such file or directory [C:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jmn0s2\\cmTC_ddb5d.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.39
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckLibraryExists.cmake:78 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/FindThreads.cmake:175 (_threads_check_lib)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/Qt/install-x64/lib/cmake/Qt6/Qt6Dependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "C:/Qt/install-x64/lib/cmake/Qt6/Qt6Config.cmake:168 (include)"
      - "CMakeLists.txt:14 (find_package)"
    checks:
      - "Looking for pthread_create in pthreads"
    directories:
      source: "C:/PROJECT/HighJump/build/CMakeFiles/CMakeScratch/TryCompile-kg92xj"
      binary: "C:/PROJECT/HighJump/build/CMakeFiles/CMakeScratch/TryCompile-kg92xj"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Qt/install-x64/lib/cmake/Qt6;C:/Qt/install-x64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/Qt/install-x64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "CMAKE_HAVE_PTHREADS_CREATE"
      cached: true
      stdout: |
        Change Dir: 'C:/PROJECT/HighJump/build/CMakeFiles/CMakeScratch/TryCompile-kg92xj'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_d898e.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.18+a338add32
        鐢熸垚鍚姩鏃堕棿涓?2025/8/6 18:16:52銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kg92xj\\cmTC_d898e.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_d898e.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kg92xj\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_d898e.dir\\Debug\\cmTC_d898e.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_d898e.dir\\Debug\\cmTC_d898e.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_d898e.dir\\Debug\\cmTC_d898e.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "C:\\PROJECT\\HighJumpTerminal\\vcpkg\\installed\\x64-windows\\"
          Using normalized configuration "Debug"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /I"C:\\PROJECT\\HighJumpTerminal\\vcpkg\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /Fo"cmTC_d898e.dir\\Debug\\\\" /Fd"cmTC_d898e.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kg92xj\\CheckFunctionExists.cxx"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35214 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /I"C:\\PROJECT\\HighJumpTerminal\\vcpkg\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /Fo"cmTC_d898e.dir\\Debug\\\\" /Fd"cmTC_d898e.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kg92xj\\CheckFunctionExists.cxx"
          CheckFunctionExists.cxx
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kg92xj\\Debug\\cmTC_d898e.exe" /INCREMENTAL /ILK:"cmTC_d898e.dir\\Debug\\cmTC_d898e.ilk" /NOLOGO /LIBPATH:"C:\\PROJECT\\HighJumpTerminal\\vcpkg\\installed\\x64-windows\\debug\\lib" /LIBPATH:"C:\\PROJECT\\HighJumpTerminal\\vcpkg\\installed\\x64-windows\\debug\\lib\\manual-link" pthreads.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib "C:\\PROJECT\\HighJumpTerminal\\vcpkg\\installed\\x64-windows\\debug\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/PROJECT/HighJump/build/CMakeFiles/CMakeScratch/TryCompile-kg92xj/Debug/cmTC_d898e.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/PROJECT/HighJump/build/CMakeFiles/CMakeScratch/TryCompile-kg92xj/Debug/cmTC_d898e.lib" /MACHINE:X64  /machine:x64 cmTC_d898e.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥減threads.lib鈥?[C:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kg92xj\\cmTC_d898e.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淐:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kg92xj\\cmTC_d898e.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淐:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kg92xj\\cmTC_d898e.vcxproj鈥?榛樿鐩爣) (1) ->
        (Link 鐩爣) -> 
          LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥減threads.lib鈥?[C:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kg92xj\\cmTC_d898e.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.49
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckLibraryExists.cmake:78 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/FindThreads.cmake:176 (_threads_check_lib)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/Qt/install-x64/lib/cmake/Qt6/Qt6Dependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "C:/Qt/install-x64/lib/cmake/Qt6/Qt6Config.cmake:168 (include)"
      - "CMakeLists.txt:14 (find_package)"
    checks:
      - "Looking for pthread_create in pthread"
    directories:
      source: "C:/PROJECT/HighJump/build/CMakeFiles/CMakeScratch/TryCompile-85f7q0"
      binary: "C:/PROJECT/HighJump/build/CMakeFiles/CMakeScratch/TryCompile-85f7q0"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Qt/install-x64/lib/cmake/Qt6;C:/Qt/install-x64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/Qt/install-x64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "CMAKE_HAVE_PTHREAD_CREATE"
      cached: true
      stdout: |
        Change Dir: 'C:/PROJECT/HighJump/build/CMakeFiles/CMakeScratch/TryCompile-85f7q0'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_df8bc.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.18+a338add32
        鐢熸垚鍚姩鏃堕棿涓?2025/8/6 18:16:53銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-85f7q0\\cmTC_df8bc.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_df8bc.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-85f7q0\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_df8bc.dir\\Debug\\cmTC_df8bc.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_df8bc.dir\\Debug\\cmTC_df8bc.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_df8bc.dir\\Debug\\cmTC_df8bc.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "C:\\PROJECT\\HighJumpTerminal\\vcpkg\\installed\\x64-windows\\"
          Using normalized configuration "Debug"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /I"C:\\PROJECT\\HighJumpTerminal\\vcpkg\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /Fo"cmTC_df8bc.dir\\Debug\\\\" /Fd"cmTC_df8bc.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-85f7q0\\CheckFunctionExists.cxx"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35214 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /I"C:\\PROJECT\\HighJumpTerminal\\vcpkg\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /Fo"cmTC_df8bc.dir\\Debug\\\\" /Fd"cmTC_df8bc.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-85f7q0\\CheckFunctionExists.cxx"
          CheckFunctionExists.cxx
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-85f7q0\\Debug\\cmTC_df8bc.exe" /INCREMENTAL /ILK:"cmTC_df8bc.dir\\Debug\\cmTC_df8bc.ilk" /NOLOGO /LIBPATH:"C:\\PROJECT\\HighJumpTerminal\\vcpkg\\installed\\x64-windows\\debug\\lib" /LIBPATH:"C:\\PROJECT\\HighJumpTerminal\\vcpkg\\installed\\x64-windows\\debug\\lib\\manual-link" pthread.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib "C:\\PROJECT\\HighJumpTerminal\\vcpkg\\installed\\x64-windows\\debug\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/PROJECT/HighJump/build/CMakeFiles/CMakeScratch/TryCompile-85f7q0/Debug/cmTC_df8bc.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/PROJECT/HighJump/build/CMakeFiles/CMakeScratch/TryCompile-85f7q0/Debug/cmTC_df8bc.lib" /MACHINE:X64  /machine:x64 cmTC_df8bc.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥減thread.lib鈥?[C:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-85f7q0\\cmTC_df8bc.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淐:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-85f7q0\\cmTC_df8bc.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淐:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-85f7q0\\cmTC_df8bc.vcxproj鈥?榛樿鐩爣) (1) ->
        (Link 鐩爣) -> 
          LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥減thread.lib鈥?[C:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-85f7q0\\cmTC_df8bc.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.49
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCXXSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "C:/Qt/install-x64/lib/cmake/Qt6/FindWrapAtomic.cmake:36 (check_cxx_source_compiles)"
      - "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/Qt/install-x64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "C:/Qt/install-x64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake:45 (include)"
      - "C:/Qt/install-x64/lib/cmake/Qt6/Qt6Config.cmake:218 (find_package)"
      - "CMakeLists.txt:14 (find_package)"
    checks:
      - "Performing Test HAVE_STDATOMIC"
    directories:
      source: "C:/PROJECT/HighJump/build/CMakeFiles/CMakeScratch/TryCompile-g843hn"
      binary: "C:/PROJECT/HighJump/build/CMakeFiles/CMakeScratch/TryCompile-g843hn"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Qt/install-x64/lib/cmake/Qt6;C:/Qt/install-x64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/Qt/install-x64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "HAVE_STDATOMIC"
      cached: true
      stdout: |
        Change Dir: 'C:/PROJECT/HighJump/build/CMakeFiles/CMakeScratch/TryCompile-g843hn'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/arm64/MSBuild.exe" cmTC_e29f3.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.18+a338add32
        鐢熸垚鍚姩鏃堕棿涓?2025/8/6 18:16:54銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-g843hn\\cmTC_e29f3.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_e29f3.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-g843hn\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_e29f3.dir\\Debug\\cmTC_e29f3.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_e29f3.dir\\Debug\\cmTC_e29f3.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_e29f3.dir\\Debug\\cmTC_e29f3.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        VcpkgTripletSelection:
          Using triplet "x64-windows" from "C:\\PROJECT\\HighJumpTerminal\\vcpkg\\installed\\x64-windows\\"
          Using normalized configuration "Debug"
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\CL.exe /c /I"C:\\PROJECT\\HighJumpTerminal\\vcpkg\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAVE_STDATOMIC /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /Fo"cmTC_e29f3.dir\\Debug\\\\" /Fd"cmTC_e29f3.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-g843hn\\src.cxx"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35214 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /I"C:\\PROJECT\\HighJumpTerminal\\vcpkg\\installed\\x64-windows\\include" /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAVE_STDATOMIC /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /Fo"cmTC_e29f3.dir\\Debug\\\\" /Fd"cmTC_e29f3.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-g843hn\\src.cxx"
          src.cxx
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostArm64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-g843hn\\Debug\\cmTC_e29f3.exe" /INCREMENTAL /ILK:"cmTC_e29f3.dir\\Debug\\cmTC_e29f3.ilk" /NOLOGO /LIBPATH:"C:\\PROJECT\\HighJumpTerminal\\vcpkg\\installed\\x64-windows\\debug\\lib" /LIBPATH:"C:\\PROJECT\\HighJumpTerminal\\vcpkg\\installed\\x64-windows\\debug\\lib\\manual-link" kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib "C:\\PROJECT\\HighJumpTerminal\\vcpkg\\installed\\x64-windows\\debug\\lib\\*.lib" /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/PROJECT/HighJump/build/CMakeFiles/CMakeScratch/TryCompile-g843hn/Debug/cmTC_e29f3.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/PROJECT/HighJump/build/CMakeFiles/CMakeScratch/TryCompile-g843hn/Debug/cmTC_e29f3.lib" /MACHINE:X64  /machine:x64 cmTC_e29f3.dir\\Debug\\src.obj
          cmTC_e29f3.vcxproj -> C:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-g843hn\\Debug\\cmTC_e29f3.exe
        AppLocalFromInstalled:
          pwsh.exe -ExecutionPolicy Bypass -noprofile -File "C:\\PROJECT\\HighJumpTerminal\\vcpkg\\scripts\\buildsystems\\msbuild\\applocal.ps1" "C:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-g843hn\\Debug\\cmTC_e29f3.exe" "C:\\PROJECT\\HighJumpTerminal\\vcpkg\\installed\\x64-windows\\debug\\bin" "cmTC_e29f3.dir\\Debug\\cmTC_e29f3.tlog\\cmTC_e29f3.write.1u.tlog" "cmTC_e29f3.dir\\Debug\\vcpkg.applocal.log"
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_e29f3.dir\\Debug\\cmTC_e29f3.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_e29f3.dir\\Debug\\cmTC_e29f3.tlog\\cmTC_e29f3.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淐:\\PROJECT\\HighJump\\build\\CMakeFiles\\CMakeScratch\\TryCompile-g843hn\\cmTC_e29f3.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:01.98
        
      exitCode: 0
...
