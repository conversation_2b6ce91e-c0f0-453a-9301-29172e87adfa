# Tests CMakeLists.txt
# Unit tests for High Jump Competition Management System

# Find Qt Test module
find_package(Qt6 REQUIRED COMPONENTS Test)

# Enable Qt Test
qt_add_executable(test_database_manager
    unit/test_database_manager.cpp
    "${CMAKE_SOURCE_DIR}/src/persistence/database_manager.cpp"
)

qt_add_executable(test_config_manager
    unit/test_config_manager.cpp
    "${CMAKE_SOURCE_DIR}/src/utils/config_manager.cpp"
)

qt_add_executable(test_api_client
    unit/test_api_client.cpp
    "${CMAKE_SOURCE_DIR}/src/api/api_client.cpp"
    "${CMAKE_SOURCE_DIR}/src/utils/config_manager.cpp"
)

# Enable automatic MOC processing for all test executables
set_target_properties(test_database_manager test_config_manager test_api_client PROPERTIES
    AUTOMOC ON
)

# Link Qt libraries
target_link_libraries(test_database_manager PRIVATE
    Qt6::Core
    Qt6::Sql
    Qt6::Test
)

target_link_libraries(test_config_manager PRIVATE
    Qt6::Core
    Qt6::Widgets
    Qt6::Test
)

target_link_libraries(test_api_client PRIVATE
    Qt6::Core
    Qt6::Network
    Qt6::Widgets
    Qt6::Test
)

# Include directories
target_include_directories(test_database_manager PRIVATE
    ${CMAKE_SOURCE_DIR}/src
)

target_include_directories(test_config_manager PRIVATE
    ${CMAKE_SOURCE_DIR}/src
)

target_include_directories(test_api_client PRIVATE
    ${CMAKE_SOURCE_DIR}/src
)

# Set output directory for tests
set_target_properties(test_database_manager test_config_manager test_api_client PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Deploy Qt plugins for tests (Windows)
if(WIN32)
    # Find Qt installation path
    get_target_property(QT_QMAKE_EXECUTABLE Qt6::qmake IMPORTED_LOCATION)
    get_filename_component(QT_WINDEPLOYQT_EXECUTABLE ${QT_QMAKE_EXECUTABLE} PATH)
    set(QT_WINDEPLOYQT_EXECUTABLE "${QT_WINDEPLOYQT_EXECUTABLE}/windeployqt.exe")

    if(EXISTS ${QT_WINDEPLOYQT_EXECUTABLE})
        # Deploy Qt for each test executable
        foreach(test_target test_database_manager test_config_manager test_api_client)
            add_custom_command(TARGET ${test_target} POST_BUILD
                COMMAND ${QT_WINDEPLOYQT_EXECUTABLE} --sql $<TARGET_FILE:${test_target}>
                COMMENT "Deploying Qt libraries and plugins for ${test_target}")
        endforeach()
    endif()
endif()

# Add tests to CTest
add_test(NAME DatabaseManagerTest COMMAND test_database_manager)
add_test(NAME ConfigManagerTest COMMAND test_config_manager)
add_test(NAME ApiClientTest COMMAND test_api_client)

# Sprint 2 E2E Tests - Simplified Version
qt_add_executable(test_e2e_workflow
    integration/test_e2e_simple.cpp
    "${CMAKE_SOURCE_DIR}/src/utils/config_manager.cpp"
    "${CMAKE_SOURCE_DIR}/src/persistence/database_manager.cpp"
    "${CMAKE_SOURCE_DIR}/src/api/api_client.cpp"
)

# Enable automatic MOC processing for the E2E test
set_target_properties(test_e2e_workflow PROPERTIES
    AUTOMOC ON
)

target_link_libraries(test_e2e_workflow PRIVATE
    Qt6::Core
    Qt6::Widgets
    Qt6::Sql
    Qt6::Network
    Qt6::Test
)

target_include_directories(test_e2e_workflow PRIVATE
    ${CMAKE_SOURCE_DIR}/src
)

# Set output directory for E2E test
set_target_properties(test_e2e_workflow PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Deploy Qt for E2E test (Windows)
if(WIN32 AND EXISTS ${QT_WINDEPLOYQT_EXECUTABLE})
    add_custom_command(TARGET test_e2e_workflow POST_BUILD
        COMMAND ${QT_WINDEPLOYQT_EXECUTABLE} --sql $<TARGET_FILE:test_e2e_workflow>
        COMMENT "Deploying Qt libraries and plugins for test_e2e_workflow")
endif()

add_test(NAME E2EWorkflowTest COMMAND test_e2e_workflow)