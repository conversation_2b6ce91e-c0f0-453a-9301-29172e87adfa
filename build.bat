@echo off
REM High Jump Competition Management System - Build Script
REM This script helps build the project on Windows

echo ========================================
echo High Jump Competition Management System
echo Build Script for Windows
echo ========================================

REM Check if CMake is available
where cmake >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: CMake not found in PATH
    echo Please install CMake from https://cmake.org/download/
    echo or add it to your system PATH
    pause
    exit /b 1
)

REM Check if Qt environment is set
if "%CMAKE_PREFIX_PATH%"=="" (
    echo WARNING: CMAKE_PREFIX_PATH not set
    echo Please set it to your Qt installation directory
    echo Example: set CMAKE_PREFIX_PATH=C:\Qt\6.9.1\msvc2019_64
    echo.
    echo Common Qt installation paths:
    echo - C:\Qt\6.9.1\msvc2019_64
    echo - C:\Qt\6.9.1\msvc2022_64
    echo - C:\Qt\6.9.1\mingw_64
    echo.
    pause
)

REM Create build directory
if not exist build mkdir build
cd build

REM Configure with CMake
echo.
echo Configuring project with CMake...
cmake .. -DCMAKE_BUILD_TYPE=Release
if %errorlevel% neq 0 (
    echo ERROR: CMake configuration failed
    pause
    exit /b 1
)

REM Build the project
echo.
echo Building project...
cmake --build . --config Release
if %errorlevel% neq 0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build completed successfully!
echo ========================================
echo.
echo Executable location: build\bin\Release\high-jump-scorer.exe
echo.

REM Check if executable exists
if exist "bin\Release\high-jump-scorer.exe" (
    echo Executable found! You can now run the application.
    echo.
    set /p choice="Do you want to run the application now? (y/n): "
    if /i "%choice%"=="y" (
        echo Starting application...
        start "" "bin\Release\high-jump-scorer.exe"
    )
) else (
    echo WARNING: Executable not found in expected location
    echo Please check the build output for any errors
)

echo.
echo Build script completed.
pause 