# 技术栈规范

## 基础架构选择

根据产品需求文档（PRD）第四章"技术假设"中的明确规定，本项目将被作为一个全新的（Greenfield）项目进行开发。我们确认**不使用任何现有的启动模板或样板代码**。整个应用程序将基于 **Qt 6** 框架从头开始构建。

## 技术栈选型表

| 类别 (Category) | 技术 (Technology) | 版本 (Version) | 目的 (Purpose) | 理由 (Rationale) |
|:---|:---|:---|:---|:---|
| **核心框架** | Qt | 6.9.1 | 构建整个桌面应用的用户界面、逻辑和平台集成 | 用户指定版本，确保与开发环境一致。 |
| **编程语言** | C++ | C++17 | 应用程序的主要编程语言 | Qt的原生语言，提供最高性能和完整的框架API访问。 |
| **构建工具** | CMake | 3.2x (最新稳定版) | 跨平台的项目构建与依赖管理 | 现代C++和Qt 6项目的标准构建系统。 |
| **本地数据库** | **SQLite3** | (与Qt 6捆绑) | **为应用提供本地数据持久化，实现离线功能。** | **轻量、稳定、文件式、与Qt的SQL模块原生集成。** |
| **测试框架** | Qt Test | (与Qt 6.9.1捆绑) | 单元测试与集成测试 | 与Qt原生集成，简化Qt特性的测试。 |
| **网络通信** | QNetworkAccessManager | (与Qt 6.9.1捆绑) | 处理所有与服务器API的HTTP/HTTPS通信 | Qt中执行网络请求的标准、集成化方案。 |
| **文件导出** | QPdfWriter / QFile | (与Qt 6.9.1捆绑) | 实现PDF和CSV格式的成绩导出功能 | 优先使用Qt原生功能，减少外部依赖。 |

## 架构原则

### 核心设计原则
- **离线优先**: 所有功能首先在本地工作，网络同步为辅助功能
- **单一数据源**: 使用中心化的数据模型管理应用状态
- **模块化设计**: 各功能模块解耦，便于测试和维护
- **原生集成**: 优先使用Qt原生组件，最小化外部依赖

### 技术约束
- 必须兼容Qt 6.9.1版本
- 使用C++17标准特性
- 支持跨平台部署（Windows, macOS, Linux）
- 遵循Qt的信号槽机制进行组件通信

## 依赖管理

### 核心依赖
- Qt 6.9.1 (Core, Widgets, SQL, Network, PrintSupport)
- CMake 3.20+ 
- C++17兼容编译器

### 可选依赖
- Qt Test (开发和测试环境)
- QSS样式表支持
- 平台特定的系统集成库

## 版本兼容性

### 支持的平台
- Windows 10/11 (x64)
- macOS 12+ (x64/ARM64)
- Linux (主流发行版)

### 编译器要求
- MSVC 2019+ (Windows)
- Clang 12+ (macOS)  
- GCC 9+ (Linux)