********* Start testing of TestApiClient *********
Config: Using QtTest library 6.9.1, Qt 6.9.1 (x86_64-little_endian-llp64 shared (dynamic) release build; by MSVC 2022), windows 11
QDEBUG : TestApiClient::initTestCase() Initializing ConfigManager...
QDEBUG : TestApiClient::initTestCase() ConfigManager initialized successfully
QDEBUG : TestApiClient::initTestCase() Configuration file: "C:/Users/<USER>/AppData/Local/test_api_client/config.ini"
QDEBUG : TestApiClient::initTestCase() Test API client initialized
PASS   : TestApiClient::initTestCase()
PASS   : TestApiClient::testSingletonPattern()
QDEBUG : TestApiClient::testInitialization() Initializing ApiClient...
QDEBUG : TestApiClient::testInitialization() Loaded API configuration:
QDEBUG : TestApiClient::testInitialization()   Base URL: "http://localhost:8080"
QDEBUG : TestApiClient::testInitialization()   Timeout: 30000 ms
QDEBUG : TestApiClient::testInitialization()   Max Retries: 3
QDEBUG : TestApiClient::testInitialization()   API Version: "v1"
QDEBUG : TestApiClient::testInitialization() ApiClient initialized successfully
QDEBUG : TestApiClient::testInitialization() API Base URL: "http://localhost:8080"
QDEBUG : TestApiClient::testInitialization() API Timeout: 30000 ms
QDEBUG : TestApiClient::testInitialization() API client initialized successfully
PASS   : TestApiClient::testInitialization()
QDEBUG : TestApiClient::testNetworkStatusDetection() Network status: Offline
PASS   : TestApiClient::testNetworkStatusDetection()
QDEBUG : TestApiClient::testConnectionTest() Connection test skipped - ApiClient initialization depends on ConfigManager
PASS   : TestApiClient::testConnectionTest()
QDEBUG : TestApiClient::testRequestBuilding() Request building test skipped - ApiClient initialization depends on ConfigManager
PASS   : TestApiClient::testRequestBuilding()
QDEBUG : TestApiClient::testResponseHandling() Response handling test skipped - ApiClient initialization depends on ConfigManager
PASS   : TestApiClient::testResponseHandling()
QDEBUG : TestApiClient::testAuthentication() Auth token set
QDEBUG : TestApiClient::testAuthentication() API key set
QDEBUG : TestApiClient::testAuthentication() Authentication cleared
QDEBUG : TestApiClient::testAuthentication() Authentication test completed
PASS   : TestApiClient::testAuthentication()
QDEBUG : TestApiClient::testErrorHandling() Error handling test skipped - ApiClient initialization depends on ConfigManager
PASS   : TestApiClient::testErrorHandling()
QDEBUG : TestApiClient::testConfigurationIntegration() Configuration integration test completed
QDEBUG : TestApiClient::testConfigurationIntegration() API URL: "http://localhost:8080"
QDEBUG : TestApiClient::testConfigurationIntegration() Timeout: 30000 ms
QDEBUG : TestApiClient::testConfigurationIntegration() Max retries: 3
PASS   : TestApiClient::testConfigurationIntegration()
PASS   : TestApiClient::cleanupTestCase()
Totals: 11 passed, 0 failed, 0 skipped, 0 blacklisted, 4132ms
********* Finished testing of TestApiClient *********
