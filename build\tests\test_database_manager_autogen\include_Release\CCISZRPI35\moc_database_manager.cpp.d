C:/PROJECT/HighJump/build/tests/test_database_manager_autogen/include_Release/CCISZRPI35/moc_database_manager.cpp: C:/PROJECT/HighJump/src/persistence/database_manager.h \
  C:/Qt/install-x64/include/QtCore/QMutex \
  C:/Qt/install-x64/include/QtCore/QObject \
  C:/Qt/install-x64/include/QtCore/QString \
  C:/Qt/install-x64/include/QtCore/q17memory.h \
  C:/Qt/install-x64/include/QtCore/q20functional.h \
  C:/Qt/install-x64/include/QtCore/q20memory.h \
  C:/Qt/install-x64/include/QtCore/q20type_traits.h \
  C:/Qt/install-x64/include/QtCore/q20utility.h \
  C:/Qt/install-x64/include/QtCore/q23utility.h \
  C:/Qt/install-x64/include/QtCore/qalgorithms.h \
  C:/Qt/install-x64/include/QtCore/qanystringview.h \
  C:/Qt/install-x64/include/QtCore/qarraydata.h \
  C:/Qt/install-x64/include/QtCore/qarraydataops.h \
  C:/Qt/install-x64/include/QtCore/qarraydatapointer.h \
  C:/Qt/install-x64/include/QtCore/qassert.h \
  C:/Qt/install-x64/include/QtCore/qatomic.h \
  C:/Qt/install-x64/include/QtCore/qatomic_cxx11.h \
  C:/Qt/install-x64/include/QtCore/qbasicatomic.h \
  C:/Qt/install-x64/include/QtCore/qbindingstorage.h \
  C:/Qt/install-x64/include/QtCore/qbytearray.h \
  C:/Qt/install-x64/include/QtCore/qbytearrayalgorithms.h \
  C:/Qt/install-x64/include/QtCore/qbytearraylist.h \
  C:/Qt/install-x64/include/QtCore/qbytearrayview.h \
  C:/Qt/install-x64/include/QtCore/qchar.h \
  C:/Qt/install-x64/include/QtCore/qcompare.h \
  C:/Qt/install-x64/include/QtCore/qcompare_impl.h \
  C:/Qt/install-x64/include/QtCore/qcomparehelpers.h \
  C:/Qt/install-x64/include/QtCore/qcompilerdetection.h \
  C:/Qt/install-x64/include/QtCore/qconfig.h \
  C:/Qt/install-x64/include/QtCore/qconstructormacros.h \
  C:/Qt/install-x64/include/QtCore/qcontainerfwd.h \
  C:/Qt/install-x64/include/QtCore/qcontainerinfo.h \
  C:/Qt/install-x64/include/QtCore/qcontainertools_impl.h \
  C:/Qt/install-x64/include/QtCore/qcontiguouscache.h \
  C:/Qt/install-x64/include/QtCore/qdarwinhelpers.h \
  C:/Qt/install-x64/include/QtCore/qdatastream.h \
  C:/Qt/install-x64/include/QtCore/qdeadlinetimer.h \
  C:/Qt/install-x64/include/QtCore/qdebug.h \
  C:/Qt/install-x64/include/QtCore/qelapsedtimer.h \
  C:/Qt/install-x64/include/QtCore/qexceptionhandling.h \
  C:/Qt/install-x64/include/QtCore/qflags.h \
  C:/Qt/install-x64/include/QtCore/qfloat16.h \
  C:/Qt/install-x64/include/QtCore/qforeach.h \
  C:/Qt/install-x64/include/QtCore/qfunctionaltools_impl.h \
  C:/Qt/install-x64/include/QtCore/qfunctionpointer.h \
  C:/Qt/install-x64/include/QtCore/qgenericatomic.h \
  C:/Qt/install-x64/include/QtCore/qglobal.h \
  C:/Qt/install-x64/include/QtCore/qglobalstatic.h \
  C:/Qt/install-x64/include/QtCore/qhash.h \
  C:/Qt/install-x64/include/QtCore/qhashfunctions.h \
  C:/Qt/install-x64/include/QtCore/qiodevicebase.h \
  C:/Qt/install-x64/include/QtCore/qiterable.h \
  C:/Qt/install-x64/include/QtCore/qiterator.h \
  C:/Qt/install-x64/include/QtCore/qlatin1stringview.h \
  C:/Qt/install-x64/include/QtCore/qlist.h \
  C:/Qt/install-x64/include/QtCore/qlogging.h \
  C:/Qt/install-x64/include/QtCore/qmalloc.h \
  C:/Qt/install-x64/include/QtCore/qmap.h \
  C:/Qt/install-x64/include/QtCore/qmath.h \
  C:/Qt/install-x64/include/QtCore/qmetacontainer.h \
  C:/Qt/install-x64/include/QtCore/qmetaobject.h \
  C:/Qt/install-x64/include/QtCore/qmetatype.h \
  C:/Qt/install-x64/include/QtCore/qminmax.h \
  C:/Qt/install-x64/include/QtCore/qmutex.h \
  C:/Qt/install-x64/include/QtCore/qnamespace.h \
  C:/Qt/install-x64/include/QtCore/qnumeric.h \
  C:/Qt/install-x64/include/QtCore/qobject.h \
  C:/Qt/install-x64/include/QtCore/qobject_impl.h \
  C:/Qt/install-x64/include/QtCore/qobjectdefs.h \
  C:/Qt/install-x64/include/QtCore/qobjectdefs_impl.h \
  C:/Qt/install-x64/include/QtCore/qoverload.h \
  C:/Qt/install-x64/include/QtCore/qpair.h \
  C:/Qt/install-x64/include/QtCore/qprocessordetection.h \
  C:/Qt/install-x64/include/QtCore/qrefcount.h \
  C:/Qt/install-x64/include/QtCore/qscopedpointer.h \
  C:/Qt/install-x64/include/QtCore/qscopeguard.h \
  C:/Qt/install-x64/include/QtCore/qset.h \
  C:/Qt/install-x64/include/QtCore/qshareddata.h \
  C:/Qt/install-x64/include/QtCore/qshareddata_impl.h \
  C:/Qt/install-x64/include/QtCore/qsharedpointer.h \
  C:/Qt/install-x64/include/QtCore/qsharedpointer_impl.h \
  C:/Qt/install-x64/include/QtCore/qstdlibdetection.h \
  C:/Qt/install-x64/include/QtCore/qstring.h \
  C:/Qt/install-x64/include/QtCore/qstringalgorithms.h \
  C:/Qt/install-x64/include/QtCore/qstringbuilder.h \
  C:/Qt/install-x64/include/QtCore/qstringconverter.h \
  C:/Qt/install-x64/include/QtCore/qstringconverter_base.h \
  C:/Qt/install-x64/include/QtCore/qstringfwd.h \
  C:/Qt/install-x64/include/QtCore/qstringlist.h \
  C:/Qt/install-x64/include/QtCore/qstringliteral.h \
  C:/Qt/install-x64/include/QtCore/qstringmatcher.h \
  C:/Qt/install-x64/include/QtCore/qstringtokenizer.h \
  C:/Qt/install-x64/include/QtCore/qstringview.h \
  C:/Qt/install-x64/include/QtCore/qswap.h \
  C:/Qt/install-x64/include/QtCore/qsysinfo.h \
  C:/Qt/install-x64/include/QtCore/qsystemdetection.h \
  C:/Qt/install-x64/include/QtCore/qtaggedpointer.h \
  C:/Qt/install-x64/include/QtCore/qtclasshelpermacros.h \
  C:/Qt/install-x64/include/QtCore/qtconfiginclude.h \
  C:/Qt/install-x64/include/QtCore/qtconfigmacros.h \
  C:/Qt/install-x64/include/QtCore/qtcore-config.h \
  C:/Qt/install-x64/include/QtCore/qtcoreexports.h \
  C:/Qt/install-x64/include/QtCore/qtcoreglobal.h \
  C:/Qt/install-x64/include/QtCore/qtdeprecationdefinitions.h \
  C:/Qt/install-x64/include/QtCore/qtdeprecationmarkers.h \
  C:/Qt/install-x64/include/QtCore/qtenvironmentvariables.h \
  C:/Qt/install-x64/include/QtCore/qtextstream.h \
  C:/Qt/install-x64/include/QtCore/qtformat_impl.h \
  C:/Qt/install-x64/include/QtCore/qtmetamacros.h \
  C:/Qt/install-x64/include/QtCore/qtnoop.h \
  C:/Qt/install-x64/include/QtCore/qtpreprocessorsupport.h \
  C:/Qt/install-x64/include/QtCore/qtresource.h \
  C:/Qt/install-x64/include/QtCore/qtsan_impl.h \
  C:/Qt/install-x64/include/QtCore/qttranslation.h \
  C:/Qt/install-x64/include/QtCore/qttypetraits.h \
  C:/Qt/install-x64/include/QtCore/qtversion.h \
  C:/Qt/install-x64/include/QtCore/qtversionchecks.h \
  C:/Qt/install-x64/include/QtCore/qtypeinfo.h \
  C:/Qt/install-x64/include/QtCore/qtypes.h \
  C:/Qt/install-x64/include/QtCore/qutf8stringview.h \
  C:/Qt/install-x64/include/QtCore/qvariant.h \
  C:/Qt/install-x64/include/QtCore/qvarlengtharray.h \
  C:/Qt/install-x64/include/QtCore/qversiontagging.h \
  C:/Qt/install-x64/include/QtCore/qxptype_traits.h \
  C:/Qt/install-x64/include/QtCore/qyieldcpu.h \
  C:/Qt/install-x64/include/QtSql/QSqlDatabase \
  C:/Qt/install-x64/include/QtSql/QSqlError \
  C:/Qt/install-x64/include/QtSql/QSqlQuery \
  C:/Qt/install-x64/include/QtSql/qsqldatabase.h \
  C:/Qt/install-x64/include/QtSql/qsqlerror.h \
  C:/Qt/install-x64/include/QtSql/qsqlquery.h \
  C:/Qt/install-x64/include/QtSql/qtsql-config.h \
  C:/Qt/install-x64/include/QtSql/qtsqlexports.h \
  C:/Qt/install-x64/include/QtSql/qtsqlglobal.h
