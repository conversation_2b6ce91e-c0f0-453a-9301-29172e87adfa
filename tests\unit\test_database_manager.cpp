#include <QTest>
#include <QSignalSpy>
#include <QDir>
#include <QStandardPaths>
#include <QTemporaryDir>

#include "persistence/database_manager.h"

/**
 * @brief Unit tests for DatabaseManager class
 * 
 * This test class provides comprehensive unit testing for the DatabaseManager
 * class, covering all aspects of database management functionality including
 * initialization, table creation, transaction management, and error handling.
 * 
 * The test suite includes:
 * - Singleton pattern verification
 * - Database initialization and connection testing
 * - Table creation and schema management
 * - Transaction handling and rollback testing
 * - Error handling and edge cases
 * - Database path management
 * - Connection status monitoring
 * 
 * Each test method focuses on a specific aspect of the DatabaseManager
 * functionality and provides detailed verification of expected behavior.
 * 
 * @note This test class uses QTemporaryDir for isolated test database files
 *       to ensure tests don't interfere with production data.
 */
class TestDatabaseManager : public QObject
{
    Q_OBJECT

private slots:
    /**
     * @brief Initializes the test case
     * 
     * Sets up the test environment by creating a temporary directory
     * for the test database and storing the original database path.
     * This method is called once before all test methods.
     */
    void initTestCase();
    
    /**
     * @brief Cleans up the test case
     * 
     * Performs cleanup operations after all tests are complete.
     * The temporary directory is automatically cleaned up by QTemporaryDir.
     * This method is called once after all test methods.
     */
    void cleanupTestCase();
    
    /**
     * @brief Tests the singleton pattern implementation
     * 
     * Verifies that DatabaseManager correctly implements the singleton pattern
     * by ensuring that multiple calls to instance() return the same object.
     */
    void testSingletonPattern();
    
    /**
     * @brief Tests database initialization
     * 
     * Verifies that the database can be properly initialized and connected.
     * Tests that the database file is created and the connection is established.
     */
    void testDatabaseInitialization();
    
    /**
     * @brief Tests table creation
     * 
     * Verifies that all required database tables are created successfully
     * during initialization, including competitions, athletes, height_settings,
     * attempt_records, sync_queue, and schema_version tables.
     */
    void testTableCreation();
    
    /**
     * @brief Tests schema version management
     * 
     * Verifies that the database schema version is properly managed,
     * including current version retrieval and schema migration handling.
     */
    void testSchemaVersionManagement();
    
    /**
     * @brief Tests transaction management
     * 
     * Verifies that database transactions work correctly, including
     * transaction start, commit, and rollback functionality.
     */
    void testTransactionManagement();
    
    /**
     * @brief Tests error handling
     * 
     * Verifies that the DatabaseManager properly handles various error
     * conditions and provides appropriate error reporting.
     */
    void testErrorHandling();
    
    /**
     * @brief Tests database path management
     * 
     * Verifies that the database path can be retrieved and is valid.
     * Tests that the path points to an actual file location.
     */
    void testDatabasePath();
    
    /**
     * @brief Tests connection status
     * 
     * Verifies that the database connection status is properly reported
     * and updated when the connection state changes.
     */
    void testConnectionStatus();

private:
    QTemporaryDir m_tempDir;        ///< Temporary directory for test database files
    QString m_originalDatabasePath; ///< Original database path for restoration
};

/**
 * @brief Initializes the test case environment
 * 
 * Creates a temporary directory for test database files and stores
 * the original database path for potential restoration. This ensures
 * that tests run in isolation without affecting production data.
 */
void TestDatabaseManager::initTestCase()
{
    // Create temporary directory for test database
    QVERIFY(m_tempDir.isValid());
    
    // Store original database path
    DatabaseManager* dbManager = DatabaseManager::instance();
    m_originalDatabasePath = dbManager->getDatabasePath();
    
    // Set test database path
    QString testDbPath = m_tempDir.filePath("test_competition_data.sqlite");
    qDebug() << "Test database path:" << testDbPath;
}

/**
 * @brief Cleans up the test case environment
 * 
 * Performs any necessary cleanup operations. The QTemporaryDir
 * automatically removes the temporary directory and all its contents.
 */
void TestDatabaseManager::cleanupTestCase()
{
    // Cleanup will be handled by QTemporaryDir
}

/**
 * @brief Tests the singleton pattern implementation
 * 
 * Verifies that DatabaseManager correctly implements the singleton pattern
 * by ensuring that multiple calls to instance() return the same object pointer.
 * This is a fundamental test for the singleton design pattern.
 */
void TestDatabaseManager::testSingletonPattern()
{
    qDebug() << "Starting testSingletonPattern...";
    
    DatabaseManager* instance1 = DatabaseManager::instance();
    qDebug() << "Got first instance:" << instance1;
    
    DatabaseManager* instance2 = DatabaseManager::instance();
    qDebug() << "Got second instance:" << instance2;
    
    QVERIFY(instance1 != nullptr);
    QVERIFY(instance2 != nullptr);
    QCOMPARE(instance1, instance2);
    
    qDebug() << "testSingletonPattern completed successfully";
}

/**
 * @brief Tests database initialization functionality
 * 
 * Verifies that the database can be properly initialized and that
 * a connection is established. Tests that the database file is created
 * at the expected location and that the connection status is reported correctly.
 */
void TestDatabaseManager::testDatabaseInitialization()
{
    qDebug() << "Starting testDatabaseInitialization...";
    
    DatabaseManager* dbManager = DatabaseManager::instance();
    qDebug() << "Got DatabaseManager instance:" << dbManager;
    
    // Test initialization
    qDebug() << "Calling initialize()...";
    bool initResult = dbManager->initialize();
    qDebug() << "Initialize result:" << initResult;
    QVERIFY(initResult);
    
    qDebug() << "Checking connection...";
    bool connected = dbManager->isConnected();
    qDebug() << "Connection status:" << connected;
    QVERIFY(connected);
    
    // Test database file exists
    qDebug() << "Getting database path...";
    QString dbPath = dbManager->getDatabasePath();
    qDebug() << "Database path:" << dbPath;
    
    bool fileExists = QFile::exists(dbPath);
    qDebug() << "File exists:" << fileExists;
    QVERIFY(fileExists);
    
    qDebug() << "Database initialized successfully at:" << dbPath;
    qDebug() << "testDatabaseInitialization completed successfully";
}

/**
 * @brief Tests table creation during initialization
 * 
 * Verifies that all required database tables are created successfully
 * during the initialization process. This includes the core tables
 * needed for the application to function properly.
 */
void TestDatabaseManager::testTableCreation()
{
    DatabaseManager* dbManager = DatabaseManager::instance();
    
    // Verify all required tables exist
    QVERIFY(dbManager->tableExists("competitions"));
    QVERIFY(dbManager->tableExists("athletes"));
    QVERIFY(dbManager->tableExists("height_settings"));
    QVERIFY(dbManager->tableExists("attempt_records"));
    QVERIFY(dbManager->tableExists("sync_queue"));
    QVERIFY(dbManager->tableExists("schema_version"));
    
    qDebug() << "All required tables created successfully";
}

/**
 * @brief Tests schema version management functionality
 * 
 * Verifies that the database schema version is properly managed,
 * including retrieval of the current version and handling of schema
 * migrations. This ensures database schema consistency.
 */
void TestDatabaseManager::testSchemaVersionManagement()
{
    DatabaseManager* dbManager = DatabaseManager::instance();
    
    // Test current schema version
    int currentVersion = dbManager->getCurrentSchemaVersion();
    QVERIFY(currentVersion > 0);
    
    qDebug() << "Current schema version:" << currentVersion;
    
    // Test schema migration (should not change version if already current)
    QVERIFY(dbManager->migrateSchema());
    int newVersion = dbManager->getCurrentSchemaVersion();
    QCOMPARE(currentVersion, newVersion);
}

void TestDatabaseManager::testTransactionManagement()
{
    DatabaseManager* dbManager = DatabaseManager::instance();
    
    // Test transaction operations
    QVERIFY(dbManager->beginTransaction());
    QVERIFY(dbManager->commitTransaction());
    
    // Test rollback
    QVERIFY(dbManager->beginTransaction());
    QVERIFY(dbManager->rollbackTransaction());
}

void TestDatabaseManager::testErrorHandling()
{
    DatabaseManager* dbManager = DatabaseManager::instance();

    // Test error handling with invalid operations
    QSignalSpy errorSpy(dbManager, &DatabaseManager::databaseError);

    // Try to execute invalid SQL
    QSqlQuery query(dbManager->m_database);
    bool result = query.exec("SELECT * FROM non_existent_table");
    QVERIFY(!result);

    // Check if there's a query error or database error
    QString queryError = query.lastError().text();
    QString dbError = dbManager->m_database.lastError().text();

    // At least one error should be present
    bool hasError = !queryError.isEmpty() || !dbError.isEmpty() || query.lastError().isValid();
    QVERIFY(hasError);

    qDebug() << "Query error (expected):" << queryError;
    qDebug() << "Database error:" << dbError;
}

/**
 * @brief Tests database path management
 * 
 * Verifies that the database path can be retrieved and is valid.
 * Tests that the path points to an actual file location.
 */
void TestDatabaseManager::testDatabasePath()
{
    DatabaseManager* dbManager = DatabaseManager::instance();
    
    QString dbPath = dbManager->getDatabasePath();
    QVERIFY(!dbPath.isEmpty());
    QVERIFY(QFile::exists(dbPath));
    
    qDebug() << "Database path:" << dbPath;
}

/**
 * @brief Tests connection status
 * 
 * Verifies that the database connection status is properly reported
 * and updated when the connection state changes.
 */
void TestDatabaseManager::testConnectionStatus()
{
    DatabaseManager* dbManager = DatabaseManager::instance();
    
    // Test connection status
    QVERIFY(dbManager->isConnected());
    
    // Test database operations
    QSqlQuery query(dbManager->m_database);
    QVERIFY(query.exec("SELECT 1"));
    QVERIFY(query.next());
    QCOMPARE(query.value(0).toInt(), 1);
}

QTEST_MAIN(TestDatabaseManager)
#include "test_database_manager.moc" 