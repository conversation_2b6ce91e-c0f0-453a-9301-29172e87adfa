# High Jump Competition Management System - Build Guide

## 🏗️ Build Requirements

### Prerequisites
- **Qt 6.9.1 or later** (Core, Widgets, Sql, Network, PrintSupport modules)
- **CMake 3.20 or later**
- **C++17 compatible compiler** (MSVC, GCC, Clang)
- **Visual Studio 2019/2022** (Windows) or **Xcode** (macOS) or **GCC** (Linux)

## 🚀 Quick Build Instructions

### Windows (Visual Studio)
```powershell
# 1. Install Qt 6.9.1+ from https://www.qt.io/download
# 2. Install Visual Studio 2019/2022 with C++ workload
# 3. Install CMake from https://cmake.org/download/

# Set Qt environment (adjust path as needed)
$env:CMAKE_PREFIX_PATH = "C:\Qt\6.9.1\msvc2019_64"

# Configure and build
cmake -B build -S .
cmake --build build --config Release

# Run the application
.\build\bin\Release\high-jump-scorer.exe
```

### macOS (Xcode)
```bash
# 1. Install Qt 6.9.1+ via Homebrew or Qt installer
# 2. Install Xcode from App Store
# 3. Install CMake: brew install cmake

# Set Qt environment
export CMAKE_PREFIX_PATH="/opt/homebrew/opt/qt@6"

# Configure and build
cmake -B build -S .
cmake --build build --config Release

# Run the application
open build/bin/high-jump-scorer.app
```

### Linux (GCC)
```bash
# 1. Install Qt 6.9.1+ via package manager
# 2. Install build tools: sudo apt install build-essential cmake
# 3. Install Qt6 development packages

# Configure and build
cmake -B build -S .
cmake --build build --config Release

# Run the application
./build/bin/high-jump-scorer
```

## 📁 Project Structure

```
HighJump/
├── CMakeLists.txt              # Main build configuration
├── src/
│   ├── main.cpp               # Application entry point
│   ├── api/
│   │   ├── api_client.h       # API communication
│   │   └── api_client.cpp
│   ├── core/
│   │   ├── jump_manager.h     # Core business logic
│   │   └── jump_manager.cpp
│   ├── models/
│   │   ├── athlete.h          # Data models
│   │   ├── athlete.cpp
│   │   ├── competition.h
│   │   ├── competition.cpp
│   │   ├── jump_attempt.h
│   │   └── jump_attempt.cpp
│   ├── persistence/
│   │   ├── database_manager.h # Data persistence
│   │   └── database_manager.cpp
│   ├── ui/
│   │   ├── main_window.h      # Main UI
│   │   ├── main_window.cpp
│   │   ├── athlete_dialog.h   # Athlete management UI
│   │   └── athlete_dialog.cpp
│   └── utils/
│       ├── config_manager.h   # Configuration management
│       └── config_manager.cpp
└── docs/                      # Documentation
```

## 🔧 Build Configuration

### CMake Configuration Options
- `CMAKE_BUILD_TYPE`: Debug, Release, RelWithDebInfo, MinSizeRel
- `CMAKE_PREFIX_PATH`: Path to Qt installation
- `BUILD_TESTS`: Enable/disable test building (default: ON)

### Example CMake Configuration
```bash
cmake -B build -S . \
    -DCMAKE_BUILD_TYPE=Release \
    -DCMAKE_PREFIX_PATH="/path/to/qt" \
    -DBUILD_TESTS=ON
```

## 🐛 Troubleshooting

### Common Issues

#### 1. Qt Not Found
```
CMake Error: Could not find Qt6
```
**Solution**: Set `CMAKE_PREFIX_PATH` to your Qt installation directory.

#### 2. C++17 Not Supported
```
error: 'std::optional' is not a member of 'std'
```
**Solution**: Ensure your compiler supports C++17. For MSVC, use `/std:c++17` flag.

#### 3. Missing Qt Modules
```
CMake Error: Could not find Qt6::PrintSupport
```
**Solution**: Install the required Qt modules or adjust the CMakeLists.txt.

#### 4. Build Failures on Windows
```
LNK1104: cannot open file 'Qt6Core.lib'
```
**Solution**: Ensure Qt libraries are in your PATH or use Qt's qmake to set up the environment.

## 📦 Deployment

### Windows
- Copy `high-jump-scorer.exe` and Qt DLLs to target directory
- Use `windeployqt` to automatically copy Qt dependencies:
```bash
windeployqt build/bin/Release/high-jump-scorer.exe
```

### macOS
- The `.app` bundle contains all dependencies
- Use `macdeployqt` for additional Qt dependencies if needed

### Linux
- Install Qt runtime packages on target system
- Or use static linking for standalone deployment

## 🧪 Testing

### Run Tests
```bash
# Build and run tests
cmake --build build --target test

# Or run tests directly
ctest --test-dir build
```

### Manual Testing
1. Launch the application
2. Create a new competition
3. Add athletes
4. Record jump attempts
5. Export results
6. Test all menu and toolbar functions

## 📋 Build Checklist

- [ ] Qt 6.9.1+ installed with required modules
- [ ] CMake 3.20+ installed
- [ ] C++17 compiler available
- [ ] Environment variables set correctly
- [ ] Project builds without errors
- [ ] Application launches successfully
- [ ] All features work as expected
- [ ] Tests pass
- [ ] Deployment package created

## 🔄 Continuous Integration

### GitHub Actions Example
```yaml
name: Build and Test
on: [push, pull_request]
jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [windows-latest, ubuntu-latest, macos-latest]
    steps:
    - uses: actions/checkout@v3
    - name: Install Qt
      uses: jurplel/install-qt-action@v3
      with:
        version: '6.9.1'
    - name: Configure CMake
      run: cmake -B build -S .
    - name: Build
      run: cmake --build build --config Release
    - name: Test
      run: ctest --test-dir build
```

## 📞 Support

For build issues:
1. Check the troubleshooting section above
2. Verify all prerequisites are installed
3. Ensure environment variables are set correctly
4. Check Qt and CMake documentation
5. Review the project's CMakeLists.txt configuration

## 🎯 Next Steps

After successful build:
1. Test all application features
2. Create deployment packages
3. Set up continuous integration
4. Document any custom build requirements
5. Prepare for distribution 