#include "performance_monitor.h"
#include <QApplication>
#include <QDebug>
#include <QFile>
#include <QTextStream>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QStandardPaths>
#include <QDir>
#include <QProcess>
#include <QThread>
#include <QCoreApplication>
#include <QTimer>
#include <QDateTime>
#include <QRegularExpression>
#include <QRegularExpressionMatch>

#ifdef Q_OS_WINDOWS
#include <windows.h>
#include <psapi.h>
#elif defined(Q_OS_LINUX)
#include <sys/sysinfo.h>
#include <sys/resource.h>
#include <unistd.h>
#elif defined(Q_OS_MAC)
#include <mach/mach.h>
#include <mach/mach_host.h>
#endif

PerformanceMonitor::PerformanceMonitor(QObject *parent)
    : QObject(parent)
    , m_memoryTimer(new QTimer(this))
    , m_peakMemory(0)
    , m_monitoringEnabled(true)
    , m_memoryTrackingEnabled(true)
    , m_databaseTrackingEnabled(true)
    , m_autoOptimizationEnabled(false)
    , m_optimizationLevel(Basic)
    , m_memoryCheckInterval(5000)  // 5 seconds
    , m_autoOptimizationInterval(30000)  // 30 seconds
    , m_memoryWarningThreshold(80.0)  // 80%
    , m_slowQueryThreshold(1000)  // 1 second
{
    // Initialize database metrics
    m_databaseMetrics.queryCount = 0;
    m_databaseMetrics.totalQueryTime = 0;
    m_databaseMetrics.averageQueryTime = 0;
    m_databaseMetrics.slowestQueryTime = 0;
    m_databaseMetrics.slowestQuery = "";
    
    // Setup memory timer
    connect(m_memoryTimer.get(), &QTimer::timeout, this, &PerformanceMonitor::onMemoryCheck);
    
    // Setup auto-optimization timer
    if (m_autoOptimizationEnabled) {
        QTimer *autoOptTimer = new QTimer(this);
        connect(autoOptTimer, &QTimer::timeout, this, &PerformanceMonitor::onAutoOptimization);
        autoOptTimer->start(m_autoOptimizationInterval);
    }
    
    // Setup performance analysis timer
    QTimer *analysisTimer = new QTimer(this);
    connect(analysisTimer, &QTimer::timeout, this, &PerformanceMonitor::onPerformanceAnalysis);
    analysisTimer->start(60000);  // Every minute
    
    qDebug() << "PerformanceMonitor initialized";
}

PerformanceMonitor::~PerformanceMonitor()
{
    if (m_memoryTrackingEnabled) {
        stopMemoryTracking();
    }
    
    // Generate final performance report
    if (m_monitoringEnabled && !m_metrics.isEmpty()) {
        QString reportPath = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + 
                           "/high_jump_performance_report.txt";
        exportPerformanceData(reportPath);
    }
}

void PerformanceMonitor::startTimer(const QString &name, const QString &category)
{
    if (!m_monitoringEnabled) return;
    
    QMutexLocker locker(&m_mutex);
    
    QElapsedTimer timer;
    timer.start();
    m_activeTimers[name] = timer;
    m_timerCategories[name] = category;
    
    qDebug() << "Performance timer started:" << name << "in category:" << category;
}

void PerformanceMonitor::endTimer(const QString &name)
{
    if (!m_monitoringEnabled) return;
    
    QMutexLocker locker(&m_mutex);
    
    if (!m_activeTimers.contains(name)) {
        qWarning() << "Performance timer not found:" << name;
        return;
    }
    
    QElapsedTimer timer = m_activeTimers.take(name);
    QString category = m_timerCategories.take(name);
    
    qint64 duration = timer.elapsed();
    
    PerformanceMetric metric;
    metric.name = name;
    metric.category = category;
    metric.startTime = 0;  // We don't track start time in this implementation
    metric.endTime = 0;    // We don't track end time in this implementation
    metric.duration = duration;
    metric.timestamp = QDateTime::currentDateTime();
    
    m_metrics.append(metric);
    updateSummary(metric);
    
    qDebug() << "Performance timer ended:" << name << "duration:" << formatDuration(duration);
}

void PerformanceMonitor::recordMetric(const QString &name, qint64 duration, const QString &category)
{
    if (!m_monitoringEnabled) return;
    
    QMutexLocker locker(&m_mutex);
    
    PerformanceMetric metric;
    metric.name = name;
    metric.category = category;
    metric.duration = duration;
    metric.timestamp = QDateTime::currentDateTime();
    
    m_metrics.append(metric);
    updateSummary(metric);
}

PerformanceMonitor::MemoryInfo PerformanceMonitor::getCurrentMemoryInfo() const
{
    MemoryInfo info;
    info.timestamp = QDateTime::currentDateTime();
    
#ifdef Q_OS_WINDOWS
    PROCESS_MEMORY_COUNTERS_EX pmc;
    if (GetProcessMemoryInfo(GetCurrentProcess(), (PROCESS_MEMORY_COUNTERS*)&pmc, sizeof(pmc))) {
        info.totalMemory = pmc.WorkingSetSize;
        info.usedMemory = pmc.WorkingSetSize;
        info.freeMemory = 0;  // Not easily available on Windows
        info.peakMemory = pmc.PeakWorkingSetSize;
    }
#elif defined(Q_OS_LINUX)
    FILE* file = fopen("/proc/self/status", "r");
    if (file) {
        char line[128];
        while (fgets(line, 128, file) != NULL) {
            if (strncmp(line, "VmRSS:", 6) == 0) {
                sscanf(line, "VmRSS: %lld", &info.usedMemory);
                info.usedMemory *= 1024;  // Convert from KB to bytes
                break;
            }
        }
        fclose(file);
    }
    
    struct sysinfo si;
    if (sysinfo(&si) == 0) {
        info.totalMemory = si.totalram * si.mem_unit;
        info.freeMemory = si.freeram * si.mem_unit;
    }
#elif defined(Q_OS_MAC)
    struct task_basic_info t_info;
    mach_msg_type_number_t t_info_count = TASK_BASIC_INFO_COUNT;
    
    if (task_info(mach_task_self(), TASK_BASIC_INFO, (task_info_t)&t_info, &t_info_count) == KERN_SUCCESS) {
        info.usedMemory = t_info.resident_size;
    }
    
    // Get total system memory
    host_basic_info_data_t hostInfo;
    mach_msg_type_number_t infoCount = HOST_BASIC_INFO_COUNT;
    host_info(mach_host_self(), HOST_BASIC_INFO, (host_info_t)&hostInfo, &infoCount);
    info.totalMemory = hostInfo.max_mem;
#endif
    
    info.memoryUsagePercent = calculateMemoryUsage();
    
    return info;
}

void PerformanceMonitor::startMemoryTracking()
{
    if (m_memoryTrackingEnabled && !m_memoryTimer->isActive()) {
        m_memoryTimer->start(m_memoryCheckInterval);
        qDebug() << "Memory tracking started with interval:" << m_memoryCheckInterval << "ms";
    }
}

void PerformanceMonitor::stopMemoryTracking()
{
    if (m_memoryTimer->isActive()) {
        m_memoryTimer->stop();
        qDebug() << "Memory tracking stopped";
    }
}

void PerformanceMonitor::recordDatabaseQuery(const QString &query, qint64 duration)
{
    if (!m_databaseTrackingEnabled) return;
    
    QMutexLocker locker(&m_mutex);
    
    m_databaseMetrics.queryCount++;
    m_databaseMetrics.totalQueryTime += duration;
    m_databaseMetrics.averageQueryTime = m_databaseMetrics.totalQueryTime / m_databaseMetrics.queryCount;
    m_databaseMetrics.lastQuery = QDateTime::currentDateTime();
    
    if (duration > m_databaseMetrics.slowestQueryTime) {
        m_databaseMetrics.slowestQueryTime = duration;
        m_databaseMetrics.slowestQuery = query;
    }
    
    if (duration > m_slowQueryThreshold) {
        emit databaseSlowQuery(query, duration);
    }
}

PerformanceMonitor::DatabaseMetrics PerformanceMonitor::getDatabaseMetrics() const
{
    QMutexLocker locker(&m_mutex);
    return m_databaseMetrics;
}

QList<PerformanceMonitor::PerformanceSummary> PerformanceMonitor::getPerformanceSummary() const
{
    QMutexLocker locker(&m_mutex);
    return m_summaries.values();
}

QList<PerformanceMonitor::PerformanceMetric> PerformanceMonitor::getMetrics(const QString &category) const
{
    QMutexLocker locker(&m_mutex);
    
    if (category.isEmpty()) {
        return m_metrics;
    }
    
    QList<PerformanceMetric> filtered;
    for (const auto &metric : m_metrics) {
        if (metric.category == category) {
            filtered.append(metric);
        }
    }
    
    return filtered;
}

PerformanceMonitor::PerformanceSummary PerformanceMonitor::getCategorySummary(const QString &category) const
{
    QMutexLocker locker(&m_mutex);
    return m_summaries.value(category);
}

void PerformanceMonitor::optimizeDatabase()
{
    qDebug() << "Starting database optimization...";
    
    // In a real implementation, this would:
    // 1. Analyze query patterns
    // 2. Create or update indexes
    // 3. Optimize table structures
    // 4. Update query plans
    
    emit optimizationCompleted("Database optimization completed");
}

void PerformanceMonitor::optimizeMemory()
{
    qDebug() << "Starting memory optimization...";
    
    // Force garbage collection if available
    QCoreApplication::processEvents();
    
    // Clear any cached data that's not immediately needed
    // This would be implemented based on the specific caching strategy
    
    emit optimizationCompleted("Memory optimization completed");
}

void PerformanceMonitor::setOptimizationLevel(OptimizationLevel level)
{
    m_optimizationLevel = level;
    
    switch (level) {
        case None:
            m_autoOptimizationEnabled = false;
            break;
        case Basic:
            m_autoOptimizationEnabled = true;
            m_memoryCheckInterval = 10000;  // 10 seconds
            m_autoOptimizationInterval = 60000;  // 1 minute
            break;
        case Aggressive:
            m_autoOptimizationEnabled = true;
            m_memoryCheckInterval = 5000;   // 5 seconds
            m_autoOptimizationInterval = 30000;  // 30 seconds
            break;
        case Custom:
            // Keep current settings
            break;
    }
}

void PerformanceMonitor::applyOptimizations()
{
    qDebug() << "Applying optimizations with level:" << m_optimizationLevel;
    
    switch (m_optimizationLevel) {
        case None:
            break;
        case Basic:
            performGeneralOptimization();
            break;
        case Aggressive:
            performMemoryOptimization();
            performDatabaseOptimization();
            performGeneralOptimization();
            break;
        case Custom:
            // Apply custom optimizations
            break;
    }
}

QString PerformanceMonitor::generatePerformanceReport() const
{
    QMutexLocker locker(&m_mutex);
    
    QString report;
    QTextStream stream(&report);
    
    stream << "=== High Jump Competition Management System - Performance Report ===\n\n";
    stream << "Generated: " << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss") << "\n\n";
    
    // Overall statistics
    stream << "=== Overall Statistics ===\n";
    stream << "Total metrics recorded: " << m_metrics.size() << "\n";
    stream << "Categories monitored: " << m_summaries.size() << "\n";
    stream << "Database queries: " << m_databaseMetrics.queryCount << "\n";
    stream << "Peak memory usage: " << formatMemorySize(m_peakMemory) << "\n\n";
    
    // Category summaries
    stream << "=== Performance by Category ===\n";
    for (const auto &summary : m_summaries) {
        stream << "Category: " << summary.category << "\n";
        stream << "  Total time: " << formatDuration(summary.totalTime) << "\n";
        stream << "  Average time: " << formatDuration(summary.averageTime) << "\n";
        stream << "  Min time: " << formatDuration(summary.minTime) << "\n";
        stream << "  Max time: " << formatDuration(summary.maxTime) << "\n";
        stream << "  Call count: " << summary.callCount << "\n";
        stream << "  First call: " << summary.firstCall.toString("yyyy-MM-dd hh:mm:ss") << "\n";
        stream << "  Last call: " << summary.lastCall.toString("yyyy-MM-dd hh:mm:ss") << "\n\n";
    }
    
    // Database performance
    if (m_databaseMetrics.queryCount > 0) {
        stream << "=== Database Performance ===\n";
        stream << "Total queries: " << m_databaseMetrics.queryCount << "\n";
        stream << "Total query time: " << formatDuration(m_databaseMetrics.totalQueryTime) << "\n";
        stream << "Average query time: " << formatDuration(m_databaseMetrics.averageQueryTime) << "\n";
        stream << "Slowest query time: " << formatDuration(m_databaseMetrics.slowestQueryTime) << "\n";
        if (!m_databaseMetrics.slowestQuery.isEmpty()) {
            stream << "Slowest query: " << m_databaseMetrics.slowestQuery << "\n";
        }
        stream << "Last query: " << m_databaseMetrics.lastQuery.toString("yyyy-MM-dd hh:mm:ss") << "\n\n";
    }
    
    // Memory information
    MemoryInfo current = getCurrentMemoryInfo();
    if (!m_memoryHistory.isEmpty()) {
        stream << "=== Memory Usage ===\n";
        stream << "Current memory usage: " << formatMemorySize(current.usedMemory) << "\n";
        stream << "Memory usage percent: " << QString::number(current.memoryUsagePercent, 'f', 2) << "%\n";
        stream << "Peak memory usage: " << formatMemorySize(m_peakMemory) << "\n\n";
    }
    
    // Recommendations
    stream << "=== Performance Recommendations ===\n";
    
    // Check for slow operations
    for (const auto &summary : m_summaries) {
        if (summary.averageTime > 1000) {  // More than 1 second
            stream << "- Consider optimizing operations in category '" << summary.category << "'\n";
        }
    }
    
    // Check database performance
    if (m_databaseMetrics.averageQueryTime > 500) {  // More than 500ms
        stream << "- Consider optimizing database queries or adding indexes\n";
    }
    
    // Check memory usage
    if (current.memoryUsagePercent > 80) {
        stream << "- High memory usage detected. Consider implementing memory optimization\n";
    }
    
    return report;
}

void PerformanceMonitor::exportPerformanceData(const QString &filePath) const
{
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qWarning() << "Failed to open file for performance data export:" << filePath;
        return;
    }
    
    QTextStream out(&file);
    out << generatePerformanceReport();
    file.close();
    
    qDebug() << "Performance data exported to:" << filePath;
}

void PerformanceMonitor::clearMetrics()
{
    QMutexLocker locker(&m_mutex);
    m_metrics.clear();
    m_summaries.clear();
    m_memoryHistory.clear();
    
    // Reset database metrics
    m_databaseMetrics.queryCount = 0;
    m_databaseMetrics.totalQueryTime = 0;
    m_databaseMetrics.averageQueryTime = 0;
    m_databaseMetrics.slowestQueryTime = 0;
    m_databaseMetrics.slowestQuery = "";
    
    qDebug() << "Performance metrics cleared";
}

void PerformanceMonitor::setMonitoringEnabled(bool enabled)
{
    m_monitoringEnabled = enabled;
    qDebug() << "Performance monitoring" << (enabled ? "enabled" : "disabled");
}

void PerformanceMonitor::setMemoryTrackingEnabled(bool enabled)
{
    m_memoryTrackingEnabled = enabled;
    if (enabled) {
        startMemoryTracking();
    } else {
        stopMemoryTracking();
    }
}

void PerformanceMonitor::setDatabaseTrackingEnabled(bool enabled)
{
    m_databaseTrackingEnabled = enabled;
    qDebug() << "Database tracking" << (enabled ? "enabled" : "disabled");
}

void PerformanceMonitor::setAutoOptimizationEnabled(bool enabled)
{
    m_autoOptimizationEnabled = enabled;
    qDebug() << "Auto optimization" << (enabled ? "enabled" : "disabled");
}

void PerformanceMonitor::onMemoryCheck()
{
    if (!m_memoryTrackingEnabled) return;
    
    MemoryInfo info = getCurrentMemoryInfo();
    m_memoryHistory.append(info);
    
    // Keep only last 100 memory records
    if (m_memoryHistory.size() > 100) {
        m_memoryHistory.removeFirst();
    }
    
    // Update peak memory
    if (info.usedMemory > m_peakMemory) {
        m_peakMemory = info.usedMemory;
    }
    
    // Check for memory warnings
    if (info.memoryUsagePercent > m_memoryWarningThreshold) {
        emit memoryWarning(info.memoryUsagePercent);
    }
}

void PerformanceMonitor::onAutoOptimization()
{
    if (!m_autoOptimizationEnabled) return;
    
    qDebug() << "Running auto-optimization...";
    applyOptimizations();
}

void PerformanceMonitor::onPerformanceAnalysis()
{
    if (!m_monitoringEnabled) return;
    
    // Analyze performance and emit warnings if needed
    for (const auto &summary : m_summaries) {
        if (summary.averageTime > 2000) {  // More than 2 seconds
            emit performanceWarning(QString("Slow performance detected in category '%1': average time %2")
                                  .arg(summary.category, formatDuration(summary.averageTime)));
        }
    }
}

void PerformanceMonitor::updateSummary(const PerformanceMetric &metric)
{
    QString category = metric.category;
    
    if (!m_summaries.contains(category)) {
        PerformanceSummary summary;
        summary.category = category;
        summary.totalTime = 0;
        summary.averageTime = 0;
        summary.minTime = LLONG_MAX;
        summary.maxTime = 0;
        summary.callCount = 0;
        summary.firstCall = metric.timestamp;
        m_summaries[category] = summary;
    }
    
    PerformanceSummary &summary = m_summaries[category];
    summary.totalTime += metric.duration;
    summary.callCount++;
    summary.averageTime = summary.totalTime / summary.callCount;
    summary.lastCall = metric.timestamp;
    
    if (metric.duration < summary.minTime) {
        summary.minTime = metric.duration;
    }
    
    if (metric.duration > summary.maxTime) {
        summary.maxTime = metric.duration;
    }
}

void PerformanceMonitor::checkMemoryUsage()
{
    MemoryInfo info = getCurrentMemoryInfo();
    if (info.memoryUsagePercent > m_memoryWarningThreshold) {
        emit memoryWarning(info.memoryUsagePercent);
    }
}

void PerformanceMonitor::checkDatabasePerformance()
{
    if (m_databaseMetrics.averageQueryTime > m_slowQueryThreshold) {
        emit performanceWarning(QString("Slow database queries detected: average time %1")
                              .arg(formatDuration(m_databaseMetrics.averageQueryTime)));
    }
}

void PerformanceMonitor::performMemoryOptimization()
{
    qDebug() << "Performing memory optimization...";
    
    // Clear any cached data
    QCoreApplication::processEvents();
    
    // Force garbage collection if available
    // This is platform-specific and would need to be implemented accordingly
    
    // Clear internal caches
    m_metrics.clear();
    m_summaries.clear();
    
    // Force memory cleanup
    QCoreApplication::processEvents();
    
    qDebug() << "Memory optimization completed";
}

void PerformanceMonitor::performDatabaseOptimization()
{
    qDebug() << "Performing database optimization...";
    
    // This would involve:
    // 1. Analyzing query patterns
    // 2. Creating indexes
    // 3. Optimizing table structures
    // Implementation depends on the specific database being used
    
    // For now, we'll implement basic optimization strategies
    // In a real implementation, this would connect to the database and perform actual optimizations
    
    // Clear database metrics to reset performance tracking
    m_databaseMetrics = DatabaseMetrics();
    
    // Emit optimization signal
    emit optimizationCompleted("Database optimization completed");
    
    qDebug() << "Database optimization completed";
}

void PerformanceMonitor::performGeneralOptimization()
{
    qDebug() << "Performing general optimization...";
    
    // General optimizations like:
    // 1. Clearing unnecessary caches
    // 2. Compacting data structures
    // 3. Optimizing algorithms
    
    // Clear unnecessary caches
    m_metrics.clear();
    m_summaries.clear();
    
    // Reset performance counters
    m_peakMemory = 0;
    m_databaseMetrics = DatabaseMetrics();
    
    // Force garbage collection
    QCoreApplication::processEvents();
    
    qDebug() << "General optimization completed";
}

QString PerformanceMonitor::formatDuration(qint64 duration) const
{
    if (duration < 1000) {
        return QString("%1 μs").arg(duration);
    } else if (duration < 1000000) {
        return QString("%1 ms").arg(duration / 1000.0, 0, 'f', 2);
    } else {
        return QString("%1 s").arg(duration / 1000000.0, 0, 'f', 2);
    }
}

QString PerformanceMonitor::formatMemorySize(qint64 bytes) const
{
    const qint64 KB = 1024;
    const qint64 MB = KB * 1024;
    const qint64 GB = MB * 1024;
    
    if (bytes < KB) {
        return QString("%1 B").arg(bytes);
    } else if (bytes < MB) {
        return QString("%1 KB").arg(double(bytes) / KB, 0, 'f', 2);
    } else if (bytes < GB) {
        return QString("%1 MB").arg(double(bytes) / MB, 0, 'f', 2);
    } else {
        return QString("%1 GB").arg(double(bytes) / GB, 0, 'f', 2);
    }
}

double PerformanceMonitor::calculateMemoryUsage() const
{
    MemoryInfo info = getCurrentMemoryInfo();
    if (info.totalMemory > 0) {
        return (double)info.usedMemory / info.totalMemory * 100.0;
    }
    return 0.0;
}

qint64 PerformanceMonitor::getCurrentMemoryUsage() const
{
    MemoryInfo info = getCurrentMemoryInfo();
    return info.usedMemory;
}

// PerformanceTimer implementation
PerformanceTimer::PerformanceTimer(const QString &name, const QString &category, PerformanceMonitor *monitor)
    : m_name(name)
    , m_category(category)
    , m_monitor(monitor)
{
    if (m_monitor) {
        m_monitor->startTimer(m_name, m_category);
    }
    m_timer.start();
}

PerformanceTimer::~PerformanceTimer()
{
    if (m_monitor) {
        m_monitor->endTimer(m_name);
    }
} 