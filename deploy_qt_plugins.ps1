# Qt Plugin Deployment Script
# This script manually deploys Qt plugins needed for tests

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Qt Plugin Deployment" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

$releaseDir = "build\bin\Release"

if (-not (Test-Path $releaseDir)) {
    Write-Host "✗ ERROR: Release directory not found: $releaseDir" -ForegroundColor Red
    exit 1
}

# Common Qt installation paths
$qtPaths = @(
    "C:\Qt\6.9.1\msvc2022_64\plugins",
    "C:\Qt\6.9.0\msvc2022_64\plugins", 
    "C:\Qt\6.8.0\msvc2022_64\plugins",
    "C:\Qt\6.7.0\msvc2022_64\plugins",
    "C:\Qt\Tools\QtCreator\bin\plugins"
)

$qtPluginPath = $null
foreach ($path in $qtPaths) {
    if (Test-Path $path) {
        $qtPluginPath = $path
        Write-Host "✓ Found Qt plugins at: $path" -ForegroundColor Green
        break
    }
}

if (-not $qtPluginPath) {
    Write-Host "⚠ WARNING: Could not find Qt plugins directory" -ForegroundColor Yellow
    Write-Host "Searching for Qt installation..." -ForegroundColor Yellow
    
    # Try to find Qt installation
    $qtInstalls = Get-ChildItem -Path "C:\" -Directory -Name "Qt*" -ErrorAction SilentlyContinue
    foreach ($qtDir in $qtInstalls) {
        $pluginPath = "C:\$qtDir\*\msvc*\plugins"
        $found = Get-ChildItem -Path $pluginPath -ErrorAction SilentlyContinue | Select-Object -First 1
        if ($found) {
            $qtPluginPath = $found.FullName
            Write-Host "✓ Found Qt plugins at: $qtPluginPath" -ForegroundColor Green
            break
        }
    }
}

if (-not $qtPluginPath) {
    Write-Host "✗ ERROR: Could not locate Qt plugins directory" -ForegroundColor Red
    Write-Host "Please ensure Qt is properly installed" -ForegroundColor Yellow
    exit 1
}

# Create plugin directories in Release folder
$platformsDir = Join-Path $releaseDir "platforms"
$sqldriversDir = Join-Path $releaseDir "sqldrivers"

if (-not (Test-Path $platformsDir)) {
    New-Item -ItemType Directory -Path $platformsDir -Force | Out-Null
    Write-Host "✓ Created platforms directory" -ForegroundColor Green
}

if (-not (Test-Path $sqldriversDir)) {
    New-Item -ItemType Directory -Path $sqldriversDir -Force | Out-Null
    Write-Host "✓ Created sqldrivers directory" -ForegroundColor Green
}

# Copy platform plugins
$platformSource = Join-Path $qtPluginPath "platforms"
if (Test-Path $platformSource) {
    $windowsPlugin = Join-Path $platformSource "qwindows.dll"
    if (Test-Path $windowsPlugin) {
        Copy-Item $windowsPlugin $platformsDir -Force
        Write-Host "✓ Copied qwindows.dll to platforms directory" -ForegroundColor Green
    } else {
        Write-Host "✗ qwindows.dll not found in Qt installation" -ForegroundColor Red
    }
} else {
    Write-Host "✗ Platforms directory not found in Qt installation" -ForegroundColor Red
}

# Copy SQL drivers
$sqlSource = Join-Path $qtPluginPath "sqldrivers"
if (Test-Path $sqlSource) {
    $sqlitePlugin = Join-Path $sqlSource "qsqlite.dll"
    if (Test-Path $sqlitePlugin) {
        Copy-Item $sqlitePlugin $sqldriversDir -Force
        Write-Host "✓ Copied qsqlite.dll to sqldrivers directory" -ForegroundColor Green
    } else {
        Write-Host "⚠ qsqlite.dll not found in Qt sqldrivers" -ForegroundColor Yellow
    }
} else {
    Write-Host "⚠ SQL drivers directory not found in Qt installation" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Plugin Deployment Complete" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# Verify deployment
Write-Host ""
Write-Host "Verifying deployment:" -ForegroundColor Yellow
$requiredFiles = @(
    "platforms\qwindows.dll",
    "sqldrivers\qsqlite.dll"
)

$allFilesPresent = $true
foreach ($file in $requiredFiles) {
    $fullPath = Join-Path $releaseDir $file
    if (Test-Path $fullPath) {
        Write-Host "  ✓ $file" -ForegroundColor Green
    } else {
        Write-Host "  ✗ $file (missing)" -ForegroundColor Red
        $allFilesPresent = $false
    }
}

if ($allFilesPresent) {
    Write-Host ""
    Write-Host "✓ All required plugins deployed successfully!" -ForegroundColor Green
    Write-Host "You can now run the tests using run_tests.ps1" -ForegroundColor Cyan
} else {
    Write-Host ""
    Write-Host "⚠ Some plugins are missing. Tests may still fail." -ForegroundColor Yellow
}

Read-Host "Press Enter to exit"
