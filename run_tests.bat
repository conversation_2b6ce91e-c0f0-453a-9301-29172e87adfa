@echo off
REM High Jump Competition Management System - Test Runner
REM This script sets up the Qt environment and runs all tests

echo ========================================
echo High Jump Competition Test Runner
echo ========================================

REM Change to the Release directory where all Qt DLLs are present
cd /d "%~dp0build\bin\Release"

REM Set Qt plugin path to current directory
set QT_PLUGIN_PATH=%CD%

REM Set Qt debug plugins (optional, for debugging)
set QT_DEBUG_PLUGINS=0

REM Display environment info
echo Current directory: %CD%
echo Qt Plugin Path: %QT_PLUGIN_PATH%
echo.

REM Check if required files exist
if not exist "qsqlite.dll" (
    echo ERROR: qsqlite.dll not found in current directory
    goto :error
)

if not exist "sqldrivers\qsqlite.dll" (
    echo ERROR: sqldrivers\qsqlite.dll not found
    goto :error
)

echo Found Qt SQLite plugin files:
echo - qsqlite.dll
echo - sqldrivers\qsqlite.dll
echo.

REM Run Database Manager Test
echo ========================================
echo Running Database Manager Test
echo ========================================
if exist "test_database_manager.exe" (
    test_database_manager.exe
    if %ERRORLEVEL% neq 0 (
        echo Database Manager Test FAILED with exit code %ERRORLEVEL%
    ) else (
        echo Database Manager Test PASSED
    )
) else (
    echo ERROR: test_database_manager.exe not found
)
echo.

REM Run Config Manager Test
echo ========================================
echo Running Config Manager Test
echo ========================================
if exist "test_config_manager.exe" (
    test_config_manager.exe
    if %ERRORLEVEL% neq 0 (
        echo Config Manager Test FAILED with exit code %ERRORLEVEL%
    ) else (
        echo Config Manager Test PASSED
    )
) else (
    echo ERROR: test_config_manager.exe not found
)
echo.

REM Run API Client Test
echo ========================================
echo Running API Client Test
echo ========================================
if exist "test_api_client.exe" (
    test_api_client.exe
    if %ERRORLEVEL% neq 0 (
        echo API Client Test FAILED with exit code %ERRORLEVEL%
    ) else (
        echo API Client Test PASSED
    )
) else (
    echo ERROR: test_api_client.exe not found
)
echo.

REM Run E2E Workflow Test
echo ========================================
echo Running E2E Workflow Test
echo ========================================
if exist "test_e2e_workflow.exe" (
    test_e2e_workflow.exe
    if %ERRORLEVEL% neq 0 (
        echo E2E Workflow Test FAILED with exit code %ERRORLEVEL%
    ) else (
        echo E2E Workflow Test PASSED
    )
) else (
    echo ERROR: test_e2e_workflow.exe not found
)
echo.

echo ========================================
echo Test execution completed
echo ========================================
goto :end

:error
echo.
echo Test execution aborted due to missing files
echo Please ensure the project is built in Release mode
echo.
pause
exit /b 1

:end
pause
