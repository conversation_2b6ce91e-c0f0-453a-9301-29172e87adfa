#include <QApplication>
#include <QDebug>
#include <QDir>
#include <QStandardPaths>

// Include our core managers
#include "persistence/database_manager.h"
#include "utils/config_manager.h"
#include "api/api_client.h"
#include "ui/main_window.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // Set application properties
    QApplication::setApplicationName("High Jump Scorer");
    QApplication::setApplicationVersion("1.0.0");
    QApplication::setOrganizationName("High Jump Competition System");
    
    qDebug() << "Starting High Jump Competition Management System...";
    
    // Initialize configuration manager
    ConfigManager* configManager = ConfigManager::instance();
    if (!configManager->initialize()) {
        qCritical() << "Failed to initialize configuration manager";
        return -1;
    }
    
    // Initialize database manager
    DatabaseManager* dbManager = DatabaseManager::instance();
    if (!dbManager->initialize()) {
        qCritical() << "Failed to initialize database";
        return -1;
    }
    
    // Initialize API client
    ApiClient* apiClient = ApiClient::instance();
    if (!apiClient->initialize()) {
        qCritical() << "Failed to initialize API client";
        return -1;
    }
    
    qDebug() << "Application initialized successfully";
    qDebug() << "Database file:" << dbManager->getDatabasePath();
    qDebug() << "Configuration loaded from:" << configManager->getConfigPath();
    
    // Create and show main window
    MainWindow window;
    window.show();
    
    return app.exec();
}