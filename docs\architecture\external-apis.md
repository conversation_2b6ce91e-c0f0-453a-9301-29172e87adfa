# 外部API集成

## 外部服务集成概述

根据当前PRD和架构设计，本系统主要依赖内部API进行数据同步，不依赖第三方外部API服务。所有外部通信都通过自有服务器API进行。

## 服务器API集成

### 主要集成点

#### 1. 比赛管理API
- **目的**: 获取比赛信息和配置
- **基础URL**: 由配置文件管理
- **认证方式**: Bearer Token
- **限制**: 根据服务器配置

**关键端点**:
- `GET /api/v1/competitions` - 获取比赛列表
- `GET /api/v1/competitions/{id}` - 获取比赛详情
- `POST /api/v1/competitions/{id}/start` - 开始比赛

#### 2. 数据同步API
- **目的**: 离线数据与服务器同步
- **基础URL**: 与比赛管理API相同
- **认证方式**: Bearer Token
- **限制**: 批量同步有大小限制

**关键端点**:
- `POST /api/v1/sync` - 批量数据同步
- `POST /api/v1/attempt-records` - 单个试跳记录上传
- `PUT /api/v1/athletes/{id}` - 更新运动员状态

## 潜在第三方API考虑

### 未来可能集成的外部服务

#### 1. 身份认证服务 (预留)
- **服务**: OAuth 2.0 / OpenID Connect
- **目的**: 统一身份认证和授权管理
- **集成考虑**: 
  - 支持多种认证提供商
  - 离线模式下的本地认证备份
  - 令牌自动刷新机制

```cpp
// 预留的OAuth集成接口
class OAuthProvider : public QObject
{
    Q_OBJECT

public:
    virtual void authenticate() = 0;
    virtual QString getAccessToken() const = 0;
    virtual bool isTokenValid() const = 0;
    virtual void refreshToken() = 0;

signals:
    void authenticationSucceeded();
    void authenticationFailed(const QString &error);
    void tokenRefreshed();
};
```

#### 2. 云存储服务 (预留)
- **服务**: AWS S3 / Azure Blob / Google Cloud Storage
- **目的**: 比赛数据备份和归档
- **集成考虑**:
  - 自动数据备份
  - 跨设备数据同步
  - 大文件存储(视频、照片)

```cpp
// 预留的云存储接口
class CloudStorageProvider : public QObject
{
    Q_OBJECT

public:
    virtual void uploadFile(const QString &localPath, const QString &remotePath) = 0;
    virtual void downloadFile(const QString &remotePath, const QString &localPath) = 0;
    virtual QStringList listFiles(const QString &prefix) const = 0;

signals:
    void uploadCompleted(const QString &remotePath);
    void uploadFailed(const QString &error);
    void downloadCompleted(const QString &localPath);
    void downloadFailed(const QString &error);
};
```

#### 3. 通知服务 (预留)
- **服务**: Push notification services
- **目的**: 实时通知和消息推送
- **集成考虑**:
  - 比赛结果通知
  - 系统状态提醒
  - 多平台支持

## API抽象层设计

### 统一API接口

```cpp
class ExternalApiManager : public QObject
{
    Q_OBJECT

public:
    static ExternalApiManager* instance();
    
    // 注册外部API提供商
    void registerProvider(const QString &name, ExternalApiProvider *provider);
    void unregisterProvider(const QString &name);
    
    // 通用API调用
    void makeRequest(const QString &provider, const ApiRequest &request);
    
signals:
    void requestCompleted(const QString &provider, const ApiResponse &response);
    void requestFailed(const QString &provider, const QString &error);

private:
    QHash<QString, ExternalApiProvider*> m_providers;
};
```

### API提供商基类

```cpp
class ExternalApiProvider : public QObject
{
    Q_OBJECT

public:
    explicit ExternalApiProvider(QObject *parent = nullptr);
    virtual ~ExternalApiProvider() = default;
    
    // 基础信息
    virtual QString providerName() const = 0;
    virtual QString apiVersion() const = 0;
    virtual QUrl baseUrl() const = 0;
    
    // 认证管理
    virtual void authenticate() = 0;
    virtual bool isAuthenticated() const = 0;
    virtual void setCredentials(const QVariantMap &credentials) = 0;
    
    // 请求处理
    virtual void sendRequest(const ApiRequest &request) = 0;
    virtual void cancelRequest(const QString &requestId) = 0;
    
    // 配置管理
    virtual void setConfiguration(const QVariantMap &config) = 0;
    virtual QVariantMap configuration() const = 0;

signals:
    void authenticated();
    void authenticationFailed(const QString &error);
    void requestCompleted(const ApiResponse &response);
    void requestFailed(const QString &requestId, const QString &error);

protected:
    QNetworkAccessManager *m_networkManager;
    QVariantMap m_configuration;
    bool m_isAuthenticated;
};
```

## 错误处理和重试策略

### API错误分类

```cpp
enum class ExternalApiError {
    NetworkError,           // 网络连接问题
    AuthenticationError,    // 认证失败
    AuthorizationError,     // 权限不足
    RateLimitError,        // 请求频率限制
    ServerError,           // 服务器内部错误
    ServiceUnavailable,    // 服务不可用
    DataFormatError,       // 数据格式错误
    TimeoutError           // 请求超时
};
```

### 重试策略配置

```cpp
class RetryConfiguration
{
public:
    int maxRetries = 3;
    int baseDelayMs = 1000;
    double backoffMultiplier = 2.0;
    int maxDelayMs = 30000;
    
    QSet<ExternalApiError> retryableErrors = {
        ExternalApiError::NetworkError,
        ExternalApiError::ServerError,
        ExternalApiError::ServiceUnavailable,
        ExternalApiError::TimeoutError
    };
    
    bool shouldRetry(ExternalApiError error, int attemptCount) const;
    int calculateDelay(int attemptCount) const;
};
```

## 安全和隐私考虑

### API密钥管理

```cpp
class SecureCredentialManager
{
public:
    static SecureCredentialManager* instance();
    
    // 安全存储
    void storeCredential(const QString &service, const QString &key, const QString &value);
    QString retrieveCredential(const QString &service, const QString &key);
    void removeCredential(const QString &service, const QString &key);
    
    // 加密管理
    void setEncryptionKey(const QByteArray &key);
    bool isEncrypted() const;

private:
    QByteArray encryptData(const QByteArray &data);
    QByteArray decryptData(const QByteArray &encryptedData);
    
    QByteArray m_encryptionKey;
};
```

### 数据隐私保护

- 敏感数据传输加密
- 最小化数据收集原则
- 用户同意机制
- 数据保留期限管理

## 监控和日志

### API调用监控

```cpp
class ApiMonitor : public QObject
{
    Q_OBJECT

public:
    static ApiMonitor* instance();
    
    // 统计信息
    struct ApiStats {
        int totalRequests;
        int successfulRequests;
        int failedRequests;
        double averageResponseTime;
        QDateTime lastRequestTime;
    };
    
    void recordRequest(const QString &provider, const ApiRequest &request);
    void recordResponse(const QString &provider, const ApiResponse &response);
    void recordError(const QString &provider, const QString &error);
    
    ApiStats getStats(const QString &provider) const;
    QStringList getAvailableProviders() const;

signals:
    void requestStarted(const QString &provider, const QString &requestId);
    void requestCompleted(const QString &provider, const QString &requestId);
    void requestFailed(const QString &provider, const QString &requestId, const QString &error);

private:
    QHash<QString, ApiStats> m_stats;
    QHash<QString, QDateTime> m_requestTimes;
};
```

### 调试和故障排除

```cpp
class ApiDebugLogger
{
public:
    enum LogLevel {
        Debug,
        Info,
        Warning,
        Error
    };
    
    static void setLogLevel(LogLevel level);
    static void logRequest(const QString &provider, const ApiRequest &request);
    static void logResponse(const QString &provider, const ApiResponse &response);
    static void logError(const QString &provider, const QString &error);
    
    static void enableRequestLogging(bool enabled);
    static void enableResponseLogging(bool enabled);
    static void setLogFile(const QString &filePath);

private:
    static LogLevel s_logLevel;
    static bool s_requestLogging;
    static bool s_responseLogging;
    static QString s_logFilePath;
};
```

## 配置管理

### 外部API配置

```cpp
class ExternalApiConfig
{
public:
    struct ProviderConfig {
        QString name;
        QUrl baseUrl;
        QVariantMap credentials;
        QVariantMap settings;
        bool enabled;
    };
    
    static void loadConfiguration(const QString &configPath);
    static void saveConfiguration(const QString &configPath);
    
    static QList<ProviderConfig> getProviders();
    static ProviderConfig getProvider(const QString &name);
    static void setProvider(const ProviderConfig &config);
    static void removeProvider(const QString &name);

private:
    static QHash<QString, ProviderConfig> s_providers;
};
```

## 总结

虽然当前系统不依赖外部第三方API，但架构设计为未来集成外部服务提供了良好的扩展性。所有外部API交互都通过统一的抽象层管理，确保系统的模块化和可维护性。