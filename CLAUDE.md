# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## High Jump Competition Management System

This is a professional desktop application for managing high jump competitions, built with Qt6 and C++17. The system provides comprehensive tools for tracking athletes, recording jump attempts, and generating competition reports following World Athletics rules.

## Development Commands

### Build System (CMake + Qt6)
- **Build the project**: `.\build.ps1` (Windows PowerShell) or `.\build.bat` (Windows Command Prompt)
- **Configure build**: `cmake -B build -S .` (requires CMAKE_PREFIX_PATH set to Qt installation)
- **Compile**: `cmake --build build --config Release`
- **Set Qt environment**: `$env:CMAKE_PREFIX_PATH = "C:\Qt\6.9.1\msvc2019_64"`

### Testing
- **Run unit tests**: `.\run_tests.ps1` 
- **Run specific test**: `.\build\bin\Release\test_database_manager.exe`
- **Run all tests with CTest**: `ctest --test-dir build`
- **Available tests**: test_database_manager.exe, test_config_manager.exe, test_api_client.exe, test_e2e_workflow.exe

### Project Validation
- **Validate implementation**: `.\validate_story_1_1.ps1`
- **QA automation**: `.\qa_automation.ps1`
- **Debug test runner**: `.\test_runner_debug.ps1`

## Architecture Overview

### Core Components
- **Models** (`src/models/`): Data structures - `Athlete`, `Competition`, `JumpAttempt`
- **Core Logic** (`src/core/`): `JumpManager` - Business logic and World Athletics rules implementation
- **UI Layer** (`src/ui/`): `MainWindow`, `AthleteDialog`, `ReportDialog`, `ThemeManager` - Complete Qt6 interface
- **Persistence** (`src/persistence/`): `DatabaseManager` - SQLite database operations  
- **Utilities** (`src/utils/`): `ConfigManager`, `ReportGenerator`, `PerformanceMonitor`
- **API** (`src/api/`): `ApiClient` - External service integration

### Design Patterns
- **Singleton Pattern**: Core managers (JumpManager, DatabaseManager, ConfigManager, ApiClient)
- **Model-View-Controller**: UI separation with Qt's signal-slot system
- **Smart Pointers**: QScopedPointer/QSharedPointer for memory management

### Key Technologies
- **Qt 6.9.1**: Core, Widgets, Sql, Network, PrintSupport modules
- **C++17 Standard**: Modern C++ features throughout
- **SQLite**: Embedded database for offline-first functionality
- **CMake 3.20+**: Cross-platform build system

## Project Structure

```
src/
├── main.cpp               # Application entry with manager initialization
├── api/                   # External service communication
├── core/                  # Business logic (JumpManager - World Athletics rules)
├── models/               # Data models (Athlete, Competition, JumpAttempt) 
├── persistence/          # Database layer (DatabaseManager - SQLite)
├── ui/                   # Qt6 user interface components
└── utils/                # Utilities (Config, Reports, Performance, Theme)
```

### Database Schema
- SQLite database with tables for athletes, competitions, jump_attempts
- Offline-first design with optional API synchronization
- Automatic database initialization on startup

### Competition Rules Implementation
- World Athletics high jump rules compliance
- 3-attempt maximum per height, 3cm minimum increments
- Automatic elimination after 3 consecutive failures
- Comprehensive tie-breaking logic based on failed attempts

## Development Guidelines

### Code Style
- Follow Qt coding conventions and C++17 best practices
- Use Qt's signal-slot mechanism for component communication
- Smart pointer usage: QScopedPointer for UI components, QSharedPointer for shared data
- Comprehensive inline documentation with Doxygen-style comments

### Testing Strategy
- Unit tests for core components using Qt Test framework
- Integration tests for workflow validation (E2E testing)
- Manual testing checklists available in validation scripts

### Threading Model
- Main thread: UI interactions and immediate responses
- Database thread: All database operations  
- Network thread: API requests and synchronization
- Computation thread: Ranking calculations and data processing

## Build Requirements

### Prerequisites
- Qt 6.9.1+ with Core, Widgets, Sql, Network, PrintSupport modules
- CMake 3.20+
- C++17 compatible compiler (MSVC 2019+, GCC 9+, Clang 12+)

### Platform Support
- Windows 10/11 (primary development platform)
- macOS 12+ (cross-platform support)
- Linux (major distributions)

## Common Workflows

### Adding New Features
1. Implement data model changes in `src/models/` if needed
2. Update business logic in `src/core/JumpManager` 
3. Modify UI components in `src/ui/`
4. Update database schema in `DatabaseManager` if required
5. Add unit tests in `tests/unit/`
6. Run full test suite with `.\run_tests.ps1`

### Competition Management Workflow
1. Application startup initializes all singleton managers
2. User creates/loads competition through MainWindow
3. Athletes added via AthleteDialog
4. Jump attempts recorded through JumpManager with automatic rule validation
5. Results calculated in real-time with World Athletics compliance
6. Export/printing via ReportGenerator with multiple format support

### Database Operations
- All database operations go through DatabaseManager singleton
- Automatic connection management and error recovery
- Thread-safe operations with proper Qt SQL handling
- Offline-first design pattern with optional cloud sync

## Current Implementation Status

The project is **IMPLEMENTATION COMPLETE** with all core features and Sprint 2 enhancements:
- ✅ Core competition management and jump recording
- ✅ Advanced reporting system (PDF, Excel, HTML templates)
- ✅ Performance monitoring and optimization
- ✅ Modern theme management system
- ✅ Comprehensive testing framework (unit + integration)
- ✅ Full World Athletics rules compliance
- ✅ Cross-platform build system ready for deployment