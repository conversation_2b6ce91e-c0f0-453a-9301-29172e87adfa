#ifndef ATHLETE_H
#define ATHLETE_H

#include <QObject>
#include <QString>
#include <QDateTime>
#include <QMetaType>

/**
 * @brief Athlete data model for High Jump Competition Management
 * 
 * This class represents an athlete participating in high jump competitions.
 * It provides a comprehensive data model for storing and managing athlete
 * information including personal details, performance records, and competition
 * data.
 * 
 * The Athlete class implements Qt's property system for easy data binding
 * and signal/slot communication. It provides:
 * - Personal information management (name, country, club)
 * - Performance tracking (personal best, season best)
 * - Competition data (start number, age calculation)
 * - Data validation and utility methods
 * - Qt property system integration for UI binding
 * 
 * The class supports both programmatic data access and Qt's property
 * system for seamless integration with Qt's Model/View architecture.
 */
class Athlete : public QObject
{
    Q_OBJECT
    Q_PROPERTY(int id READ id WRITE setId NOTIFY idChanged)
    Q_PROPERTY(QString firstName READ firstName WRITE setFirstName NOTIFY firstNameChanged)
    Q_PROPERTY(QString lastName READ lastName WRITE setLastName NOTIFY lastNameChanged)
    Q_PROPERTY(QString country READ country WRITE setCountry NOTIFY countryChanged)
    Q_PROPERTY(QString club READ club WRITE setClub NOTIFY clubChanged)
    Q_PROPERTY(int startNumber READ startNumber WRITE setStartNumber NOTIFY startNumberChanged)
    Q_PROPERTY(int personalBest READ personalBest WRITE setPersonalBest NOTIFY personalBestChanged)
    Q_PROPERTY(int seasonBest READ seasonBest WRITE setSeasonBest NOTIFY seasonBestChanged)
    Q_PROPERTY(QDateTime dateOfBirth READ dateOfBirth WRITE setDateOfBirth NOTIFY dateOfBirthChanged)

public:
    /**
     * @brief Constructs an athlete object
     * @param parent Parent QObject (default: nullptr)
     * 
     * Creates a new athlete object with default values.
     * All properties are initialized to empty/default values.
     */
    explicit Athlete(QObject *parent = nullptr);
    
    // Getters
    /**
     * @brief Gets the athlete's unique identifier
     * @return Athlete ID
     * 
     * Returns the unique identifier assigned to this athlete.
     * Used for database storage and competition management.
     */
    int id() const { return m_id; }
    
    /**
     * @brief Gets the athlete's first name
     * @return First name
     * 
     * Returns the athlete's first/given name.
     */
    QString firstName() const { return m_firstName; }
    
    /**
     * @brief Gets the athlete's last name
     * @return Last name
     * 
     * Returns the athlete's last/family name.
     */
    QString lastName() const { return m_lastName; }
    
    /**
     * @brief Gets the athlete's full name
     * @return Full name (first + last)
     * 
     * Returns the athlete's complete name as "FirstName LastName".
     * This is a computed property combining firstName and lastName.
     */
    QString fullName() const { return m_firstName + " " + m_lastName; }
    
    /**
     * @brief Gets the athlete's country
     * @return Country name
     * 
     * Returns the athlete's country of origin or representation.
     */
    QString country() const { return m_country; }
    
    /**
     * @brief Gets the athlete's club
     * @return Club name
     * 
     * Returns the athlete's club or team affiliation.
     */
    QString club() const { return m_club; }
    
    /**
     * @brief Gets the athlete's start number
     * @return Start number
     * 
     * Returns the athlete's assigned start number for the competition.
     * Start numbers are used for competition order and identification.
     */
    int startNumber() const { return m_startNumber; }
    
    /**
     * @brief Gets the athlete's personal best height
     * @return Personal best height in centimeters
     * 
     * Returns the athlete's all-time personal best jump height.
     * This represents their highest successful jump in any competition.
     */
    int personalBest() const { return m_personalBest; }
    
    /**
     * @brief Gets the athlete's season best height
     * @return Season best height in centimeters
     * 
     * Returns the athlete's best jump height in the current season.
     * This represents their highest successful jump this season.
     */
    int seasonBest() const { return m_seasonBest; }
    
    /**
     * @brief Gets the athlete's date of birth
     * @return Date of birth
     * 
     * Returns the athlete's date of birth for age calculation
     * and eligibility verification.
     */
    QDateTime dateOfBirth() const { return m_dateOfBirth; }
    
    // Setters
    /**
     * @brief Sets the athlete's unique identifier
     * @param id New athlete ID
     * 
     * Sets the unique identifier for this athlete.
     * Emits idChanged() signal when the value changes.
     */
    void setId(int id);
    
    /**
     * @brief Sets the athlete's first name
     * @param firstName New first name
     * 
     * Sets the athlete's first/given name.
     * Emits firstNameChanged() signal when the value changes.
     */
    void setFirstName(const QString &firstName);
    
    /**
     * @brief Sets the athlete's last name
     * @param lastName New last name
     * 
     * Sets the athlete's last/family name.
     * Emits lastNameChanged() signal when the value changes.
     */
    void setLastName(const QString &lastName);
    
    /**
     * @brief Sets the athlete's country
     * @param country New country name
     * 
     * Sets the athlete's country of origin or representation.
     * Emits countryChanged() signal when the value changes.
     */
    void setCountry(const QString &country);
    
    /**
     * @brief Sets the athlete's club
     * @param club New club name
     * 
     * Sets the athlete's club or team affiliation.
     * Emits clubChanged() signal when the value changes.
     */
    void setClub(const QString &club);
    
    /**
     * @brief Sets the athlete's start number
     * @param startNumber New start number
     * 
     * Sets the athlete's assigned start number for the competition.
     * Emits startNumberChanged() signal when the value changes.
     */
    void setStartNumber(int startNumber);
    
    /**
     * @brief Sets the athlete's personal best height
     * @param personalBest New personal best height in centimeters
     * 
     * Sets the athlete's all-time personal best jump height.
     * Emits personalBestChanged() signal when the value changes.
     */
    void setPersonalBest(int personalBest);
    
    /**
     * @brief Sets the athlete's season best height
     * @param seasonBest New season best height in centimeters
     * 
     * Sets the athlete's best jump height in the current season.
     * Emits seasonBestChanged() signal when the value changes.
     */
    void setSeasonBest(int seasonBest);
    
    /**
     * @brief Sets the athlete's date of birth
     * @param dateOfBirth New date of birth
     * 
     * Sets the athlete's date of birth for age calculation.
     * Emits dateOfBirthChanged() signal when the value changes.
     */
    void setDateOfBirth(const QDateTime &dateOfBirth);
    
    // Utility methods
    /**
     * @brief Validates athlete data
     * @return true if athlete data is valid, false otherwise
     * 
     * Validates that the athlete has all required information.
     * Checks for non-empty names, valid start number, and reasonable heights.
     */
    bool isValid() const;
    
    /**
     * @brief Calculates athlete's age
     * @return Age in years
     * 
     * Calculates the athlete's current age based on their date of birth.
     * Returns 0 if date of birth is not set.
     */
    int age() const;
    
    /**
     * @brief Gets display name for UI
     * @return Formatted display name
     * 
     * Returns a formatted display name suitable for UI presentation.
     * Includes start number and full name for easy identification.
     */
    QString displayName() const;
    
signals:
    /**
     * @brief Emitted when athlete ID changes
     * 
     * Signal emitted when the athlete's unique identifier is modified.
     * Used for UI updates and data binding.
     */
    void idChanged();
    
    /**
     * @brief Emitted when first name changes
     * 
     * Signal emitted when the athlete's first name is modified.
     * Used for UI updates and data binding.
     */
    void firstNameChanged();
    
    /**
     * @brief Emitted when last name changes
     * 
     * Signal emitted when the athlete's last name is modified.
     * Used for UI updates and data binding.
     */
    void lastNameChanged();
    
    /**
     * @brief Emitted when country changes
     * 
     * Signal emitted when the athlete's country is modified.
     * Used for UI updates and data binding.
     */
    void countryChanged();
    
    /**
     * @brief Emitted when club changes
     * 
     * Signal emitted when the athlete's club is modified.
     * Used for UI updates and data binding.
     */
    void clubChanged();
    
    /**
     * @brief Emitted when start number changes
     * 
     * Signal emitted when the athlete's start number is modified.
     * Used for UI updates and data binding.
     */
    void startNumberChanged();
    
    /**
     * @brief Emitted when personal best changes
     * 
     * Signal emitted when the athlete's personal best height is modified.
     * Used for UI updates and data binding.
     */
    void personalBestChanged();
    
    /**
     * @brief Emitted when season best changes
     * 
     * Signal emitted when the athlete's season best height is modified.
     * Used for UI updates and data binding.
     */
    void seasonBestChanged();
    
    /**
     * @brief Emitted when date of birth changes
     * 
     * Signal emitted when the athlete's date of birth is modified.
     * Used for UI updates and data binding.
     */
    void dateOfBirthChanged();

private:
    int m_id;                    ///< Unique athlete identifier
    QString m_firstName;         ///< Athlete's first name
    QString m_lastName;          ///< Athlete's last name
    QString m_country;           ///< Athlete's country
    QString m_club;              ///< Athlete's club/team
    int m_startNumber;           ///< Competition start number
    int m_personalBest;          ///< Personal best height (cm)
    int m_seasonBest;            ///< Season best height (cm)
    QDateTime m_dateOfBirth;     ///< Date of birth
};

Q_DECLARE_METATYPE(Athlete*)

#endif // ATHLETE_H