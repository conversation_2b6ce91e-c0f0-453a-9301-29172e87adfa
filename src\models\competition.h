#ifndef COMPETITION_H
#define COMPETITION_H

#include <QObject>
#include <QString>
#include <QDateTime>
#include <QList>
#include <QMetaType>

class Athlete;

/**
 * @brief Competition data model for High Jump Competition Management
 * 
 * This class represents a high jump competition with comprehensive management
 * capabilities for competition lifecycle, athlete management, and height progression.
 * It implements Qt's property system for easy data binding and signal/slot communication.
 * 
 * The Competition class provides:
 * - Competition information management (name, venue, date, description)
 * - Competition status tracking (NotStarted, InProgress, Completed, Cancelled)
 * - Athlete management and participant tracking
 * - Height progression management and validation
 * - Competition flow control (start, pause, resume, end)
 * - Real-time competition state monitoring
 * - Qt property system integration for UI binding
 * 
 * The class supports both programmatic competition management and user-initiated
 * competition control through the application's competition interface.
 * 
 * @note This class maintains relationships with Athlete objects and manages
 *       the complete competition lifecycle from setup to completion.
 */
class Competition : public QObject
{
    Q_OBJECT
    Q_PROPERTY(int id READ id WRITE setId NOTIFY idChanged)
    Q_PROPERTY(QString name READ name WRITE setName NOTIFY nameChanged)
    Q_PROPERTY(QString venue READ venue WRITE setVenue NOTIFY venueChanged)
    Q_PROPERTY(QDateTime date READ date WRITE setDate NOTIFY dateChanged)
    Q_PROPERTY(CompetitionStatus status READ status WRITE setStatus NOTIFY statusChanged)
    Q_PROPERTY(int currentHeight READ currentHeight WRITE setCurrentHeight NOTIFY currentHeightChanged)
    Q_PROPERTY(QString description READ description WRITE setDescription NOTIFY descriptionChanged)

public:
    /**
     * @brief Competition status enumeration
     * 
     * Defines the different states a competition can be in during its lifecycle.
     * Each status represents a specific phase of the competition.
     */
    enum CompetitionStatus {
        NotStarted,    ///< Competition is set up but not yet started
        InProgress,    ///< Competition is currently active and running
        Completed,     ///< Competition has finished successfully
        Cancelled      ///< Competition was cancelled and will not proceed
    };
    Q_ENUM(CompetitionStatus)

    /**
     * @brief Constructs a competition object
     * @param parent Parent QObject (default: nullptr)
     * 
     * Creates a new competition object with default values.
     * All properties are initialized to empty/default values.
     */
    explicit Competition(QObject *parent = nullptr);
    
    // Getters
    /**
     * @brief Gets the competition's unique identifier
     * @return Competition ID
     * 
     * Returns the unique identifier assigned to this competition.
     * Used for database storage and competition management.
     */
    int id() const { return m_id; }
    
    /**
     * @brief Gets the competition name
     * @return Competition name
     * 
     * Returns the name/title of the competition.
     */
    QString name() const { return m_name; }
    
    /**
     * @brief Gets the competition venue
     * @return Competition venue
     * 
     * Returns the venue/location where the competition takes place.
     */
    QString venue() const { return m_venue; }
    
    /**
     * @brief Gets the competition date
     * @return Competition date and time
     * 
     * Returns the scheduled date and time for the competition.
     */
    QDateTime date() const { return m_date; }
    
    /**
     * @brief Gets the competition status
     * @return Current competition status
     * 
     * Returns the current status of the competition.
     */
    CompetitionStatus status() const { return m_status; }
    
    /**
     * @brief Gets the current height
     * @return Current height in centimeters
     * 
     * Returns the current height being attempted in the competition.
     */
    int currentHeight() const { return m_currentHeight; }
    
    /**
     * @brief Gets the competition description
     * @return Competition description
     * 
     * Returns the detailed description of the competition.
     */
    QString description() const { return m_description; }
    
    // Setters
    /**
     * @brief Sets the competition's unique identifier
     * @param id New competition ID
     * 
     * Sets the unique identifier for this competition.
     * Emits idChanged() signal when the value changes.
     */
    void setId(int id);
    
    /**
     * @brief Sets the competition name
     * @param name New competition name
     * 
     * Sets the name/title of the competition.
     * Emits nameChanged() signal when the value changes.
     */
    void setName(const QString &name);
    
    /**
     * @brief Sets the competition venue
     * @param venue New competition venue
     * 
     * Sets the venue/location where the competition takes place.
     * Emits venueChanged() signal when the value changes.
     */
    void setVenue(const QString &venue);
    
    /**
     * @brief Sets the competition date
     * @param date New competition date and time
     * 
     * Sets the scheduled date and time for the competition.
     * Emits dateChanged() signal when the value changes.
     */
    void setDate(const QDateTime &date);
    
    /**
     * @brief Sets the competition status
     * @param status New competition status
     * 
     * Sets the current status of the competition.
     * Emits statusChanged() signal when the value changes.
     */
    void setStatus(CompetitionStatus status);
    
    /**
     * @brief Sets the current height
     * @param height New current height in centimeters
     * 
     * Sets the current height being attempted in the competition.
     * Emits currentHeightChanged() signal when the value changes.
     */
    void setCurrentHeight(int height);
    
    /**
     * @brief Sets the competition description
     * @param description New competition description
     * 
     * Sets the detailed description of the competition.
     * Emits descriptionChanged() signal when the value changes.
     */
    void setDescription(const QString &description);
    
    // Athlete management
    /**
     * @brief Adds an athlete to the competition
     * @param athlete Athlete to add
     * 
     * Adds an athlete to the competition's participant list.
     * Emits athleteAdded() signal when an athlete is added.
     */
    void addAthlete(Athlete *athlete);
    
    /**
     * @brief Removes an athlete from the competition
     * @param athlete Athlete to remove
     * 
     * Removes an athlete from the competition's participant list.
     * Emits athleteRemoved() signal when an athlete is removed.
     */
    void removeAthlete(Athlete *athlete);
    
    /**
     * @brief Gets all athletes in the competition
     * @return List of athletes
     * 
     * Returns a list of all athletes participating in the competition.
     */
    QList<Athlete*> athletes() const { return m_athletes; }
    
    /**
     * @brief Gets the number of athletes in the competition
     * @return Number of athletes
     * 
     * Returns the total number of athletes participating in the competition.
     */
    int athleteCount() const { return m_athletes.size(); }
    
    /**
     * @brief Finds an athlete by start number
     * @param startNumber Athlete's start number
     * @return Athlete with the specified start number, or nullptr if not found
     * 
     * Searches for an athlete in the competition by their start number.
     * Returns the athlete if found, otherwise returns nullptr.
     */
    Athlete* findAthleteByStartNumber(int startNumber) const;
    
    // Competition flow
    /**
     * @brief Checks if the competition can be started
     * @return true if competition can be started, false otherwise
     * 
     * Validates that the competition meets all requirements to be started.
     * Checks for valid date, venue, and at least one athlete.
     */
    bool canStart() const;
    
    /**
     * @brief Starts the competition
     * 
     * Changes the competition status to InProgress and begins the competition.
     * Emits competitionStarted() signal when the competition starts.
     */
    void startCompetition();
    
    /**
     * @brief Ends the competition
     * 
     * Changes the competition status to Completed and finalizes the competition.
     * Emits competitionEnded() signal when the competition ends.
     */
    void endCompetition();
    
    /**
     * @brief Pauses the competition
     * 
     * Temporarily pauses the competition while maintaining current state.
     * The competition can be resumed later.
     */
    void pauseCompetition();
    
    /**
     * @brief Resumes the competition
     * 
     * Resumes a paused competition and continues from where it left off.
     */
    void resumeCompetition();
    
    // Height management
    /**
     * @brief Gets the height progression list
     * @return List of heights in centimeters
     * 
     * Returns the planned progression of heights for the competition.
     * Heights are listed in ascending order.
     */
    QList<int> getHeightProgression() const;
    
    /**
     * @brief Sets the height progression list
     * @param heights List of heights in centimeters
     * 
     * Sets the planned progression of heights for the competition.
     * Heights should be provided in ascending order.
     */
    void setHeightProgression(const QList<int> &heights);
    
    /**
     * @brief Checks if the competition can advance to the next height
     * @return true if can advance, false otherwise
     * 
     * Validates that the competition can proceed to the next height.
     * Checks if there are remaining heights and all athletes have attempted current height.
     */
    bool canAdvanceToNextHeight() const;
    
    /**
     * @brief Advances to the next height
     * 
     * Moves the competition to the next height in the progression.
     * Emits heightChanged() signal when the height changes.
     */
    void advanceToNextHeight();
    
    /**
     * @brief Gets the next height in progression
     * @return Next height in centimeters, or -1 if no more heights
     * 
     * Returns the next height that will be attempted in the competition.
     * Returns -1 if there are no more heights in the progression.
     */
    int getNextHeight() const;
    
    // Utility methods
    /**
     * @brief Validates competition data
     * @return true if competition data is valid, false otherwise
     * 
     * Validates that the competition has all required information.
     * Checks for non-empty name, valid date, and reasonable height values.
     */
    bool isValid() const;
    
    /**
     * @brief Gets the status as a string
     * @return Human-readable status string
     * 
     * Returns a human-readable string representation of the competition status.
     */
    QString statusString() const;
    
    /**
     * @brief Gets the number of remaining athletes
     * @return Number of athletes still in competition
     * 
     * Returns the number of athletes who have not been eliminated from the competition.
     */
    int getRemainingAthletes() const;
    
signals:
    /**
     * @brief Emitted when competition ID changes
     * 
     * Signal emitted when the competition's unique identifier is modified.
     * Used for UI updates and data binding.
     */
    void idChanged();
    
    /**
     * @brief Emitted when competition name changes
     * 
     * Signal emitted when the competition name is modified.
     * Used for UI updates and data binding.
     */
    void nameChanged();
    
    /**
     * @brief Emitted when competition venue changes
     * 
     * Signal emitted when the competition venue is modified.
     * Used for UI updates and data binding.
     */
    void venueChanged();
    
    /**
     * @brief Emitted when competition date changes
     * 
     * Signal emitted when the competition date is modified.
     * Used for UI updates and data binding.
     */
    void dateChanged();
    
    /**
     * @brief Emitted when competition status changes
     * 
     * Signal emitted when the competition status is modified.
     * Used for UI updates and data binding.
     */
    void statusChanged();
    
    /**
     * @brief Emitted when current height changes
     * 
     * Signal emitted when the current height is modified.
     * Used for UI updates and data binding.
     */
    void currentHeightChanged();
    
    /**
     * @brief Emitted when competition description changes
     * 
     * Signal emitted when the competition description is modified.
     * Used for UI updates and data binding.
     */
    void descriptionChanged();
    
    /**
     * @brief Emitted when an athlete is added
     * @param athlete Athlete that was added
     * 
     * Signal emitted when an athlete is added to the competition.
     * Used for UI updates and participant tracking.
     */
    void athleteAdded(Athlete *athlete);
    
    /**
     * @brief Emitted when an athlete is removed
     * @param athlete Athlete that was removed
     * 
     * Signal emitted when an athlete is removed from the competition.
     * Used for UI updates and participant tracking.
     */
    void athleteRemoved(Athlete *athlete);
    
    /**
     * @brief Emitted when competition starts
     * 
     * Signal emitted when the competition begins.
     * Used for competition state notifications and UI updates.
     */
    void competitionStarted();
    
    /**
     * @brief Emitted when competition ends
     * 
     * Signal emitted when the competition finishes.
     * Used for competition state notifications and UI updates.
     */
    void competitionEnded();
    
    /**
     * @brief Emitted when height changes
     * @param newHeight New height in centimeters
     * 
     * Signal emitted when the competition advances to a new height.
     * Used for height progression notifications and UI updates.
     */
    void heightChanged(int newHeight);

private:
    int m_id;                    ///< Unique competition identifier
    QString m_name;              ///< Competition name/title
    QString m_venue;             ///< Competition venue/location
    QDateTime m_date;            ///< Competition date and time
    CompetitionStatus m_status;  ///< Current competition status
    int m_currentHeight;         ///< Current height being attempted (cm)
    QString m_description;       ///< Competition description
    QList<Athlete*> m_athletes;  ///< List of participating athletes
    QList<int> m_heightProgression; ///< Planned height progression (cm)
    int m_currentHeightIndex;    ///< Current index in height progression
};

Q_DECLARE_METATYPE(Competition::CompetitionStatus)

#endif // COMPETITION_H