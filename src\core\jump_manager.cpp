#include "jump_manager.h"
#include "../models/athlete.h"
#include "../models/competition.h"
#include "../models/jump_attempt.h"
#include <QDebug>

JumpManager* JumpManager::s_instance = nullptr;

JumpManager::JumpManager(QObject *parent)
    : QObject(parent)
{
}

JumpManager::~JumpManager()
{
    // Clean up all jump attempts
    for (auto &jumpList : m_jumps) {
        qDeleteAll(jumpList);
    }
    m_jumps.clear();
}

JumpManager* JumpManager::instance()
{
    if (!s_instance) {
        s_instance = new JumpManager();
    }
    return s_instance;
}

bool JumpManager::recordJump(Athlete *athlete, Competition *competition, int height, JumpAttempt::AttemptResult result, const QString &notes)
{
    if (!athlete || !competition) {
        return false;
    }
    
    int attemptNumber = this->getNextAttemptNumber(athlete, competition, height);
    return recordJump(athlete, competition, height, attemptNumber, result, notes);
}

bool JumpManager::recordJump(Athlete *athlete, Competition *competition, int height, int attemptNumber, JumpAttempt::AttemptResult result, const QString &notes)
{
    if (!athlete || !competition) {
        return false;
    }
    
    QMutexLocker locker(&m_mutex);
    
    // Validate the jump
    if (!validateJump(athlete, competition, height, attemptNumber)) {
        return false;
    }
    
    // Create the jump attempt
    JumpAttempt *jump = new JumpAttempt();
    jump->setAthleteId(athlete->id());
    jump->setCompetitionId(competition->id());
    jump->setHeight(height);
    jump->setAttemptNumber(attemptNumber);
    jump->setResult(result);
    jump->setNotes(notes);
    
    // Store the jump
    QPair<int, int> key(competition->id(), athlete->id());
    if (!m_jumps.contains(key)) {
        m_jumps[key] = QList<JumpAttempt*>();
    }
    m_jumps[key].append(jump);
    
    // Check for athlete elimination
    checkAthleteElimination(athlete, competition);
    
    // Check for height completion
    checkHeightCompletion(height, competition);
    
    emit jumpRecorded(jump);
    
    qDebug() << "Recorded jump:" << athlete->displayName() << "at" << height << "cm, attempt" << attemptNumber << "result:" << jump->resultString();
    
    return true;
}

QList<JumpAttempt*> JumpManager::getAthleteJumps(Athlete *athlete, Competition *competition) const
{
    if (!athlete || !competition) {
        return QList<JumpAttempt*>();
    }
    
    QMutexLocker locker(&m_mutex);
    QPair<int, int> key(competition->id(), athlete->id());
    return m_jumps.value(key, QList<JumpAttempt*>());
}

QList<JumpAttempt*> JumpManager::getAthleteJumpsAtHeight(Athlete *athlete, Competition *competition, int height) const
{
    QList<JumpAttempt*> allJumps = getAthleteJumps(athlete, competition);
    QList<JumpAttempt*> heightJumps;
    
    for (JumpAttempt *jump : allJumps) {
        if (jump->height() == height) {
            heightJumps.append(jump);
        }
    }
    
    return heightJumps;
}

JumpAttempt* JumpManager::getAthleteJumpAtHeight(Athlete *athlete, Competition *competition, int height, int attemptNumber) const
{
    QList<JumpAttempt*> heightJumps = getAthleteJumpsAtHeight(athlete, competition, height);
    
    for (JumpAttempt *jump : heightJumps) {
        if (jump->attemptNumber() == attemptNumber) {
            return jump;
        }
    }
    
    return nullptr;
}

QList<Athlete*> JumpManager::getAthletesAtHeight(Competition *competition, int height) const
{
    if (!competition) {
        return QList<Athlete*>();
    }
    
    QList<Athlete*> athletes;
    const auto allAthletes = competition->athletes();
    
    for (Athlete *athlete : allAthletes) {
        if (athlete && !isAthleteEliminated(athlete, competition)) {
            QList<JumpAttempt*> heightJumps = getAthleteJumpsAtHeight(athlete, competition, height);
            if (!heightJumps.isEmpty()) {
                athletes.append(athlete);
            }
        }
    }
    
    return athletes;
}

QList<Athlete*> JumpManager::getEliminatedAthletes(Competition *competition) const
{
    if (!competition) {
        return QList<Athlete*>();
    }
    
    QList<Athlete*> eliminated;
    const auto allAthletes = competition->athletes();
    
    for (Athlete *athlete : allAthletes) {
        if (athlete && isAthleteEliminated(athlete, competition)) {
            eliminated.append(athlete);
        }
    }
    
    return eliminated;
}

QList<Athlete*> JumpManager::getActiveAthletes(Competition *competition) const
{
    if (!competition) {
        return QList<Athlete*>();
    }
    
    QList<Athlete*> active;
    const auto allAthletes = competition->athletes();
    
    for (Athlete *athlete : allAthletes) {
        if (athlete && !isAthleteEliminated(athlete, competition)) {
            active.append(athlete);
        }
    }
    
    return active;
}

int JumpManager::getAthleteAttemptsAtHeight(Athlete *athlete, Competition *competition, int height) const
{
    return getAthleteJumpsAtHeight(athlete, competition, height).size();
}

int JumpManager::getAthleteTotalFailures(Athlete *athlete, Competition *competition) const
{
    QList<JumpAttempt*> allJumps = getAthleteJumps(athlete, competition);
    int failures = 0;
    
    for (JumpAttempt *jump : allJumps) {
        if (jump->isFailed()) {
            failures++;
        }
    }
    
    return failures;
}

int JumpManager::getAthleteHighestClearedHeight(Athlete *athlete, Competition *competition) const
{
    QList<JumpAttempt*> allJumps = getAthleteJumps(athlete, competition);
    int highestCleared = 0;
    
    for (JumpAttempt *jump : allJumps) {
        if (jump->isSuccessful() && jump->height() > highestCleared) {
            highestCleared = jump->height();
        }
    }
    
    return highestCleared;
}

bool JumpManager::isAthleteEliminated(Athlete *athlete, Competition *competition) const
{
    if (!athlete || !competition) {
        return false;
    }
    
    // Check for 3 consecutive failures
    QList<JumpAttempt*> allJumps = getAthleteJumps(athlete, competition);
    int consecutiveFailures = 0;
    
    // Sort jumps by height and attempt number
    std::sort(allJumps.begin(), allJumps.end(), [](JumpAttempt *a, JumpAttempt *b) {
        if (a->height() != b->height()) {
            return a->height() < b->height();
        }
        return a->attemptNumber() < b->attemptNumber();
    });
    
    for (JumpAttempt *jump : allJumps) {
        if (jump->isFailed()) {
            consecutiveFailures++;
            if (consecutiveFailures >= 3) {
                return true;
            }
        } else if (jump->isSuccessful()) {
            consecutiveFailures = 0;
        }
    }
    
    return false;
}

bool JumpManager::canAthleteJumpAtHeight(Athlete *athlete, Competition *competition, int height) const
{
    if (!athlete || !competition || isAthleteEliminated(athlete, competition)) {
        return false;
    }
    
    int attempts = getAthleteAttemptsAtHeight(athlete, competition, height);
    return attempts < 3;
}

int JumpManager::getNextAttemptNumber(Athlete *athlete, Competition *competition, int height) const
{
    return getAthleteAttemptsAtHeight(athlete, competition, height) + 1;
}

void JumpManager::clearCompetitionData(Competition *competition)
{
    if (!competition) {
        return;
    }
    
    QMutexLocker locker(&m_mutex);
    
    // Remove all jumps for this competition
    auto it = m_jumps.begin();
    while (it != m_jumps.end()) {
        if (it.key().first == competition->id()) {
            qDeleteAll(it.value());
            it = m_jumps.erase(it);
        } else {
            ++it;
        }
    }
}

void JumpManager::removeAthleteData(Athlete *athlete, Competition *competition)
{
    if (!athlete || !competition) {
        return;
    }
    
    QMutexLocker locker(&m_mutex);
    
    QPair<int, int> key(competition->id(), athlete->id());
    if (m_jumps.contains(key)) {
        qDeleteAll(m_jumps[key]);
        m_jumps.remove(key);
    }
}

bool JumpManager::validateJump(Athlete *athlete, Competition *competition, int height, int attemptNumber) const
{
    if (!athlete || !competition) {
        return false;
    }
    
    // Check if athlete is eliminated
    if (isAthleteEliminated(athlete, competition)) {
        return false;
    }
    
    // Check attempt number
    if (attemptNumber < 1 || attemptNumber > 3) {
        return false;
    }
    
    // Check if this attempt already exists
    JumpAttempt *existingJump = getAthleteJumpAtHeight(athlete, competition, height, attemptNumber);
    if (existingJump) {
        return false;
    }
    
    // Check if previous attempts exist
    for (int i = 1; i < attemptNumber; ++i) {
        JumpAttempt *prevJump = getAthleteJumpAtHeight(athlete, competition, height, i);
        if (!prevJump) {
            return false; // Missing previous attempt
        }
    }
    
    return true;
}

void JumpManager::checkAthleteElimination(Athlete *athlete, Competition *competition)
{
    if (isAthleteEliminated(athlete, competition)) {
        emit athleteEliminated(athlete, competition);
        qDebug() << "Athlete eliminated:" << athlete->displayName();
    }
}

void JumpManager::checkHeightCompletion(int height, Competition *competition)
{
    if (!competition) {
        return;
    }
    
    // Check if all active athletes have completed this height
    QList<Athlete*> activeAthletes = getActiveAthletes(competition);
    bool allCompleted = true;
    
    for (Athlete *athlete : activeAthletes) {
        QList<JumpAttempt*> heightJumps = getAthleteJumpsAtHeight(athlete, competition, height);
        bool hasSuccessfulJump = false;
        
        for (JumpAttempt *jump : heightJumps) {
            if (jump->isSuccessful()) {
                hasSuccessfulJump = true;
                break;
            }
        }
        
        if (!hasSuccessfulJump && heightJumps.size() < 3) {
            allCompleted = false;
            break;
        }
    }
    
    if (allCompleted) {
        emit heightCompleted(height, competition);
        qDebug() << "Height completed:" << height << "cm";
    }
} 