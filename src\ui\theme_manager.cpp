#include "theme_manager.h"
#include <QApplication>
#include <QDebug>
#include <QStandardPaths>
#include <QDir>
#include <QFile>
#include <QTextStream>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QFontDatabase>
#include <QStyleFactory>
#include <QPalette>
#include <QWidget>
#include <QMainWindow>
#include <QDialog>
#include <QPushButton>
#include <QTableWidget>
#include <QMenuBar>
#include <QToolBar>
#include <QStatusBar>
#include <QProgressBar>
#include <QSlider>
#include <QSpinBox>
#include <QComboBox>
#include <QLineEdit>
#include <QTextEdit>
#include <QTabWidget>
#include <QGroupBox>
#include <QLabel>

ThemeManager::ThemeManager(QObject *parent)
    : QObject(parent)
    , m_settings(new QSettings("HighJump", "ThemeManager", this))
    , m_currentThemeName("Modern")
{
    initializeDefaultThemes();
    loadSettings();
    
    // Apply default theme if none is loaded
    if (m_currentThemeName.isEmpty() || !m_themes.contains(m_currentThemeName)) {
        loadTheme(Modern);
    }
}

ThemeManager::~ThemeManager()
{
    saveSettings();
}

bool ThemeManager::loadTheme(const QString &themeName)
{
    if (!m_themes.contains(themeName)) {
        qWarning() << "Theme not found:" << themeName;
        return false;
    }
    
    m_currentTheme = m_themes[themeName];
    m_currentThemeName = themeName;
    
    // Apply theme to application
    if (QApplication *app = qobject_cast<QApplication*>(QApplication::instance())) {
        applyThemeToApplication(app);
    }
    
    emit themeChanged(themeName);
    return true;
}

bool ThemeManager::loadTheme(ThemeType type)
{
    QString themeName;
    switch (type) {
        case Light: themeName = "Light"; break;
        case Dark: themeName = "Dark"; break;
        case HighContrast: themeName = "High Contrast"; break;
        case Professional: themeName = "Professional"; break;
        case Modern: themeName = "Modern"; break;
        case Classic: themeName = "Classic"; break;
        default: themeName = "Modern"; break;
    }
    
    return loadTheme(themeName);
}

bool ThemeManager::saveTheme(const Theme &theme)
{
    m_themes[theme.name] = theme;
    
    // Save to file if it's a custom theme
    if (theme.isCustom) {
        QString filePath = getThemeFilePath(theme.name);
        QFile file(filePath);
        if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
            QJsonObject themeObj;
            themeObj["name"] = theme.name;
            themeObj["description"] = theme.description;
            themeObj["type"] = static_cast<int>(theme.type);
            themeObj["fontFamily"] = theme.fontFamily;
            themeObj["fontSize"] = theme.fontSize;
            themeObj["styleSheet"] = theme.styleSheet;
            themeObj["isCustom"] = theme.isCustom;
            
            // Save color scheme
            QJsonObject colorObj;
            colorObj["name"] = theme.colors.name;
            colorObj["description"] = theme.colors.description;
            colorObj["primary"] = theme.colors.primary.name();
            colorObj["secondary"] = theme.colors.secondary.name();
            colorObj["accent"] = theme.colors.accent.name();
            colorObj["background"] = theme.colors.background.name();
            colorObj["surface"] = theme.colors.surface.name();
            colorObj["text"] = theme.colors.text.name();
            colorObj["textSecondary"] = theme.colors.textSecondary.name();
            colorObj["border"] = theme.colors.border.name();
            colorObj["shadow"] = theme.colors.shadow.name();
            colorObj["success"] = theme.colors.success.name();
            colorObj["warning"] = theme.colors.warning.name();
            colorObj["error"] = theme.colors.error.name();
            colorObj["info"] = theme.colors.info.name();
            
            themeObj["colors"] = colorObj;
            
            QJsonDocument doc(themeObj);
            QTextStream out(&file);
            out << doc.toJson();
            file.close();
        }
    }
    
    return true;
}

bool ThemeManager::deleteTheme(const QString &themeName)
{
    if (!m_themes.contains(themeName)) {
        return false;
    }
    
    Theme theme = m_themes[themeName];
    if (theme.isCustom) {
        QString filePath = getThemeFilePath(themeName);
        QFile::remove(filePath);
    }
    
    m_themes.remove(themeName);
    
    // If we're deleting the current theme, switch to default
    if (m_currentThemeName == themeName) {
        loadTheme(Modern);
    }
    
    return true;
}

QList<ThemeManager::Theme> ThemeManager::getAvailableThemes() const
{
    return m_themes.values();
}

ThemeManager::Theme ThemeManager::getCurrentTheme() const
{
    return m_currentTheme;
}

QString ThemeManager::getCurrentThemeName() const
{
    return m_currentThemeName;
}

ThemeManager::ThemeType ThemeManager::getCurrentThemeType() const
{
    return m_currentTheme.type;
}

ThemeManager::ColorScheme ThemeManager::getCurrentColorScheme() const
{
    return m_currentTheme.colors;
}

bool ThemeManager::setColorScheme(const ColorScheme &scheme)
{
    if (!isValidColorScheme(scheme)) {
        return false;
    }
    
    m_currentTheme.colors = scheme;
    m_currentTheme.styleSheet = generateStyleSheet(scheme, m_currentTheme.fontFamily, m_currentTheme.fontSize);
    
    // Apply to application
    if (QApplication *app = qobject_cast<QApplication*>(QApplication::instance())) {
        applyThemeToApplication(app);
    }
    
    emit colorSchemeChanged(scheme);
    return true;
}

QList<ThemeManager::ColorScheme> ThemeManager::getAvailableColorSchemes() const
{
    QList<ColorScheme> schemes;
    for (const auto &theme : m_themes) {
        schemes.append(theme.colors);
    }
    return schemes;
}

QString ThemeManager::getCurrentFontFamily() const
{
    return m_currentTheme.fontFamily;
}

int ThemeManager::getCurrentFontSize() const
{
    return m_currentTheme.fontSize;
}

bool ThemeManager::setFont(const QString &family, int size)
{
    if (family.isEmpty() || size <= 0) {
        return false;
    }
    
    m_currentTheme.fontFamily = family;
    m_currentTheme.fontSize = size;
    m_currentTheme.styleSheet = generateStyleSheet(m_currentTheme.colors, family, size);
    
    // Apply to application
    if (QApplication *app = qobject_cast<QApplication*>(QApplication::instance())) {
        app->setFont(QFont(family, size));
    }
    
    emit fontChanged(family, size);
    return true;
}

QList<QString> ThemeManager::getAvailableFonts() const
{
    QFontDatabase database;
    return database.families();
}

QString ThemeManager::getCurrentStyleSheet() const
{
    return m_currentTheme.styleSheet;
}

bool ThemeManager::setStyleSheet(const QString &styleSheet)
{
    if (styleSheet.isEmpty()) {
        return false;
    }
    
    m_currentTheme.styleSheet = styleSheet;
    
    // Apply to application
    if (QApplication *app = qobject_cast<QApplication*>(QApplication::instance())) {
        app->setStyleSheet(styleSheet);
    }
    
    emit styleSheetChanged(styleSheet);
    return true;
}

bool ThemeManager::loadStyleSheetFromFile(const QString &filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        return false;
    }
    
    QTextStream in(&file);
    QString styleSheet = in.readAll();
    file.close();
    
    return setStyleSheet(styleSheet);
}

bool ThemeManager::saveStyleSheetToFile(const QString &filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        return false;
    }
    
    QTextStream out(&file);
    out << m_currentTheme.styleSheet;
    file.close();
    
    return true;
}

void ThemeManager::applyThemeToApplication(QApplication *app)
{
    if (!app) return;
    
    // Apply palette
    QPalette palette = generatePalette(m_currentTheme.colors);
    app->setPalette(palette);
    
    // Apply font
    if (!m_currentTheme.fontFamily.isEmpty() && m_currentTheme.fontSize > 0) {
        app->setFont(QFont(m_currentTheme.fontFamily, m_currentTheme.fontSize));
    }
    
    // Apply style sheet
    if (!m_currentTheme.styleSheet.isEmpty()) {
        app->setStyleSheet(m_currentTheme.styleSheet);
    }
    
    // Apply style
    if (isDarkTheme(m_currentTheme.colors)) {
        app->setStyle(QStyleFactory::create("Fusion"));
    }
}

void ThemeManager::applyThemeToWidget(QWidget *widget)
{
    if (!widget) return;
    
    // Apply palette
    QPalette palette = generatePalette(m_currentTheme.colors);
    widget->setPalette(palette);
    
    // Apply font
    if (!m_currentTheme.fontFamily.isEmpty() && m_currentTheme.fontSize > 0) {
        widget->setFont(QFont(m_currentTheme.fontFamily, m_currentTheme.fontSize));
    }
    
    // Apply style sheet
    if (!m_currentTheme.styleSheet.isEmpty()) {
        widget->setStyleSheet(m_currentTheme.styleSheet);
    }
}

void ThemeManager::applyThemeToAllWidgets()
{
    QWidgetList widgets = QApplication::allWidgets();
    for (QWidget *widget : widgets) {
        applyThemeToWidget(widget);
    }
}

bool ThemeManager::createCustomTheme(const QString &name, const ColorScheme &colors, 
                                   const QString &fontFamily, int fontSize)
{
    if (name.isEmpty() || !isValidColorScheme(colors)) {
        return false;
    }
    
    Theme theme;
    theme.name = name;
    theme.description = "Custom theme created by user";
    theme.type = Custom;
    theme.colors = colors;
    theme.fontFamily = fontFamily.isEmpty() ? "Segoe UI" : fontFamily;
    theme.fontSize = fontSize <= 0 ? 9 : fontSize;
    theme.styleSheet = generateStyleSheet(colors, theme.fontFamily, theme.fontSize);
    theme.isCustom = true;
    
    return saveTheme(theme);
}

bool ThemeManager::updateCustomTheme(const QString &name, const Theme &theme)
{
    if (!m_themes.contains(name) || !m_themes[name].isCustom) {
        return false;
    }
    
    return saveTheme(theme);
}

void ThemeManager::saveSettings()
{
    m_settings->setValue("currentTheme", m_currentThemeName);
    m_settings->setValue("fontFamily", m_currentTheme.fontFamily);
    m_settings->setValue("fontSize", m_currentTheme.fontSize);
    
    emit settingsSaved();
}

void ThemeManager::loadSettings()
{
    m_currentThemeName = m_settings->value("currentTheme", "Modern").toString();
    QString fontFamily = m_settings->value("fontFamily", "Segoe UI").toString();
    int fontSize = m_settings->value("fontSize", 9).toInt();
    
    if (m_themes.contains(m_currentThemeName)) {
        m_currentTheme = m_themes[m_currentThemeName];
        m_currentTheme.fontFamily = fontFamily;
        m_currentTheme.fontSize = fontSize;
        m_currentTheme.styleSheet = generateStyleSheet(m_currentTheme.colors, fontFamily, fontSize);
    }
    
    emit settingsLoaded();
}

void ThemeManager::resetToDefaults()
{
    m_settings->clear();
    loadTheme(Modern);
    saveSettings();
}

QString ThemeManager::generateStyleSheet(const ColorScheme &colors, const QString &fontFamily, int fontSize) const
{
    QString styleSheet;
    QTextStream stream(&styleSheet);
    
    // Base styles
    stream << generateBaseStyleSheet(colors);
    
    // Widget-specific styles
    stream << generateWidgetStyleSheet(colors);
    stream << generateButtonStyleSheet(colors);
    stream << generateTableStyleSheet(colors);
    stream << generateDialogStyleSheet(colors);
    stream << generateMenuStyleSheet(colors);
    stream << generateToolbarStyleSheet(colors);
    stream << generateStatusBarStyleSheet(colors);
    
    // Font settings
    if (!fontFamily.isEmpty() && fontSize > 0) {
        stream << QString("QWidget { font-family: '%1'; font-size: %2pt; }\n")
                  .arg(fontFamily, QString::number(fontSize));
    }
    
    return styleSheet;
}

QPalette ThemeManager::generatePalette(const ColorScheme &colors) const
{
    QPalette palette;
    
    // Set color roles
    palette.setColor(QPalette::Window, colors.background);
    palette.setColor(QPalette::WindowText, colors.text);
    palette.setColor(QPalette::Base, colors.surface);
    palette.setColor(QPalette::AlternateBase, adjustColor(colors.surface, 10));
    palette.setColor(QPalette::ToolTipBase, colors.surface);
    palette.setColor(QPalette::ToolTipText, colors.text);
    palette.setColor(QPalette::Text, colors.text);
    palette.setColor(QPalette::Button, colors.surface);
    palette.setColor(QPalette::ButtonText, colors.text);
    palette.setColor(QPalette::BrightText, colors.accent);
    palette.setColor(QPalette::Link, colors.primary);
    palette.setColor(QPalette::Highlight, colors.primary);
    palette.setColor(QPalette::HighlightedText, colors.background);
    
    return palette;
}

bool ThemeManager::isValidColorScheme(const ColorScheme &scheme) const
{
    return !scheme.name.isEmpty() && 
           scheme.primary.isValid() && 
           scheme.background.isValid() && 
           scheme.text.isValid();
}

QString ThemeManager::getThemeFilePath(const QString &themeName) const
{
    QString themeDir = getThemeDirectory();
    return QDir(themeDir).filePath(themeName + ".json");
}

void ThemeManager::initializeDefaultThemes()
{
    createLightTheme();
    createDarkTheme();
    createHighContrastTheme();
    createProfessionalTheme();
    createModernTheme();
    createClassicTheme();
}

void ThemeManager::createLightTheme()
{
    Theme theme;
    theme.name = "Light";
    theme.description = "Clean and bright light theme";
    theme.type = Light;
    theme.colors = createLightColorScheme();
    theme.fontFamily = "Segoe UI";
    theme.fontSize = 9;
    theme.styleSheet = generateStyleSheet(theme.colors, theme.fontFamily, theme.fontSize);
    theme.isCustom = false;
    
    m_themes["Light"] = theme;
}

void ThemeManager::createDarkTheme()
{
    Theme theme;
    theme.name = "Dark";
    theme.description = "Modern dark theme for reduced eye strain";
    theme.type = Dark;
    theme.colors = createDarkColorScheme();
    theme.fontFamily = "Segoe UI";
    theme.fontSize = 9;
    theme.styleSheet = generateStyleSheet(theme.colors, theme.fontFamily, theme.fontSize);
    theme.isCustom = false;
    
    m_themes["Dark"] = theme;
}

void ThemeManager::createHighContrastTheme()
{
    Theme theme;
    theme.name = "High Contrast";
    theme.description = "High contrast theme for accessibility";
    theme.type = HighContrast;
    theme.colors = createHighContrastColorScheme();
    theme.fontFamily = "Segoe UI";
    theme.fontSize = 10;
    theme.styleSheet = generateStyleSheet(theme.colors, theme.fontFamily, theme.fontSize);
    theme.isCustom = false;
    
    m_themes["High Contrast"] = theme;
}

void ThemeManager::createProfessionalTheme()
{
    Theme theme;
    theme.name = "Professional";
    theme.description = "Professional theme for business environments";
    theme.type = Professional;
    theme.colors = createProfessionalColorScheme();
    theme.fontFamily = "Calibri";
    theme.fontSize = 9;
    theme.styleSheet = generateStyleSheet(theme.colors, theme.fontFamily, theme.fontSize);
    theme.isCustom = false;
    
    m_themes["Professional"] = theme;
}

void ThemeManager::createModernTheme()
{
    Theme theme;
    theme.name = "Modern";
    theme.description = "Modern flat design theme";
    theme.type = Modern;
    theme.colors = createModernColorScheme();
    theme.fontFamily = "Segoe UI";
    theme.fontSize = 9;
    theme.styleSheet = generateStyleSheet(theme.colors, theme.fontFamily, theme.fontSize);
    theme.isCustom = false;
    
    m_themes["Modern"] = theme;
}

void ThemeManager::createClassicTheme()
{
    Theme theme;
    theme.name = "Classic";
    theme.description = "Classic Windows-style theme";
    theme.type = Classic;
    theme.colors = createClassicColorScheme();
    theme.fontFamily = "MS Shell Dlg 2";
    theme.fontSize = 8;
    theme.styleSheet = generateStyleSheet(theme.colors, theme.fontFamily, theme.fontSize);
    theme.isCustom = false;
    
    m_themes["Classic"] = theme;
}

ThemeManager::ColorScheme ThemeManager::createLightColorScheme() const
{
    ColorScheme scheme;
    scheme.name = "Light";
    scheme.description = "Light color scheme";
    scheme.primary = QColor("#2196F3");
    scheme.secondary = QColor("#FF9800");
    scheme.accent = QColor("#4CAF50");
    scheme.background = QColor("#FFFFFF");
    scheme.surface = QColor("#F5F5F5");
    scheme.text = QColor("#212121");
    scheme.textSecondary = QColor("#757575");
    scheme.border = QColor("#E0E0E0");
    scheme.shadow = QColor("#000000");
    scheme.success = QColor("#4CAF50");
    scheme.warning = QColor("#FF9800");
    scheme.error = QColor("#F44336");
    scheme.info = QColor("#2196F3");
    return scheme;
}

ThemeManager::ColorScheme ThemeManager::createDarkColorScheme() const
{
    ColorScheme scheme;
    scheme.name = "Dark";
    scheme.description = "Dark color scheme";
    scheme.primary = QColor("#2196F3");
    scheme.secondary = QColor("#FF9800");
    scheme.accent = QColor("#4CAF50");
    scheme.background = QColor("#121212");
    scheme.surface = QColor("#1E1E1E");
    scheme.text = QColor("#FFFFFF");
    scheme.textSecondary = QColor("#B3B3B3");
    scheme.border = QColor("#333333");
    scheme.shadow = QColor("#000000");
    scheme.success = QColor("#4CAF50");
    scheme.warning = QColor("#FF9800");
    scheme.error = QColor("#F44336");
    scheme.info = QColor("#2196F3");
    return scheme;
}

ThemeManager::ColorScheme ThemeManager::createHighContrastColorScheme() const
{
    ColorScheme scheme;
    scheme.name = "High Contrast";
    scheme.description = "High contrast color scheme";
    scheme.primary = QColor("#FFFFFF");
    scheme.secondary = QColor("#FFFF00");
    scheme.accent = QColor("#00FF00");
    scheme.background = QColor("#000000");
    scheme.surface = QColor("#000000");
    scheme.text = QColor("#FFFFFF");
    scheme.textSecondary = QColor("#FFFF00");
    scheme.border = QColor("#FFFFFF");
    scheme.shadow = QColor("#FFFFFF");
    scheme.success = QColor("#00FF00");
    scheme.warning = QColor("#FFFF00");
    scheme.error = QColor("#FF0000");
    scheme.info = QColor("#00FFFF");
    return scheme;
}

ThemeManager::ColorScheme ThemeManager::createProfessionalColorScheme() const
{
    ColorScheme scheme;
    scheme.name = "Professional";
    scheme.description = "Professional color scheme";
    scheme.primary = QColor("#2C3E50");
    scheme.secondary = QColor("#34495E");
    scheme.accent = QColor("#3498DB");
    scheme.background = QColor("#ECF0F1");
    scheme.surface = QColor("#FFFFFF");
    scheme.text = QColor("#2C3E50");
    scheme.textSecondary = QColor("#7F8C8D");
    scheme.border = QColor("#BDC3C7");
    scheme.shadow = QColor("#000000");
    scheme.success = QColor("#27AE60");
    scheme.warning = QColor("#F39C12");
    scheme.error = QColor("#E74C3C");
    scheme.info = QColor("#3498DB");
    return scheme;
}

ThemeManager::ColorScheme ThemeManager::createModernColorScheme() const
{
    ColorScheme scheme;
    scheme.name = "Modern";
    scheme.description = "Modern color scheme";
    scheme.primary = QColor("#6200EE");
    scheme.secondary = QColor("#03DAC6");
    scheme.accent = QColor("#FF6B6B");
    scheme.background = QColor("#FAFAFA");
    scheme.surface = QColor("#FFFFFF");
    scheme.text = QColor("#1A1A1A");
    scheme.textSecondary = QColor("#757575");
    scheme.border = QColor("#E0E0E0");
    scheme.shadow = QColor("#000000");
    scheme.success = QColor("#4CAF50");
    scheme.warning = QColor("#FF9800");
    scheme.error = QColor("#F44336");
    scheme.info = QColor("#2196F3");
    return scheme;
}

ThemeManager::ColorScheme ThemeManager::createClassicColorScheme() const
{
    ColorScheme scheme;
    scheme.name = "Classic";
    scheme.description = "Classic color scheme";
    scheme.primary = QColor("#000080");
    scheme.secondary = QColor("#808080");
    scheme.accent = QColor("#008000");
    scheme.background = QColor("#C0C0C0");
    scheme.surface = QColor("#FFFFFF");
    scheme.text = QColor("#000000");
    scheme.textSecondary = QColor("#808080");
    scheme.border = QColor("#808080");
    scheme.shadow = QColor("#000000");
    scheme.success = QColor("#008000");
    scheme.warning = QColor("#808000");
    scheme.error = QColor("#800000");
    scheme.info = QColor("#000080");
    return scheme;
}

QString ThemeManager::generateBaseStyleSheet(const ColorScheme &colors) const
{
    return QString(R"(
        QWidget {
            background-color: %1;
            color: %2;
            border: none;
        }
        
        QMainWindow {
            background-color: %1;
        }
        
        QDialog {
            background-color: %1;
        }
    )").arg(colorToHex(colors.background), colorToHex(colors.text));
}

QString ThemeManager::generateWidgetStyleSheet(const ColorScheme &colors) const
{
    return QString(R"(
        QGroupBox {
            border: 2px solid %1;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
            font-weight: bold;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            background-color: %2;
        }
        
        QTabWidget::pane {
            border: 1px solid %1;
            background-color: %2;
        }
        
        QTabBar::tab {
            background-color: %3;
            color: %4;
            padding: 8px 16px;
            margin-right: 2px;
        }
        
        QTabBar::tab:selected {
            background-color: %5;
            color: %6;
        }
        
        QTabBar::tab:hover {
            background-color: %7;
        }
    )").arg(colorToHex(colors.border),
            colorToHex(colors.surface),
            colorToHex(adjustColor(colors.surface, -10)),
            colorToHex(colors.text),
            colorToHex(colors.primary),
            colorToHex(colors.background),
            colorToHex(adjustColor(colors.surface, -5)));
}

QString ThemeManager::generateButtonStyleSheet(const ColorScheme &colors) const
{
    return QString(R"(
        QPushButton {
            background-color: %1;
            color: %2;
            border: 1px solid %3;
            border-radius: 4px;
            padding: 8px 16px;
            font-weight: bold;
        }
        
        QPushButton:hover {
            background-color: %4;
        }
        
        QPushButton:pressed {
            background-color: %5;
        }
        
        QPushButton:disabled {
            background-color: %6;
            color: %7;
        }
        
        QPushButton[type="primary"] {
            background-color: %8;
            color: %9;
        }
        
        QPushButton[type="primary"]:hover {
            background-color: %10;
        }
    )").arg(colorToHex(colors.surface),
            colorToHex(colors.text),
            colorToHex(colors.border),
            colorToHex(adjustColor(colors.surface, -5)),
            colorToHex(adjustColor(colors.surface, -10)),
            colorToHex(adjustColor(colors.surface, 10)),
            colorToHex(colors.textSecondary),
            colorToHex(colors.primary),
            colorToHex(colors.background),
            colorToHex(adjustColor(colors.primary, -10)));
}

QString ThemeManager::generateTableStyleSheet(const ColorScheme &colors) const
{
    return QString(R"(
        QTableWidget {
            background-color: %1;
            alternate-background-color: %2;
            gridline-color: %3;
            border: 1px solid %3;
        }
        
        QTableWidget::item {
            padding: 4px;
            border: none;
        }
        
        QTableWidget::item:selected {
            background-color: %4;
            color: %5;
        }
        
        QHeaderView::section {
            background-color: %6;
            color: %7;
            padding: 8px;
            border: 1px solid %3;
            font-weight: bold;
        }
        
        QHeaderView::section:hover {
            background-color: %8;
        }
    )").arg(colorToHex(colors.surface),
            colorToHex(adjustColor(colors.surface, 5)),
            colorToHex(colors.border),
            colorToHex(colors.primary),
            colorToHex(colors.background),
            colorToHex(adjustColor(colors.surface, -10)),
            colorToHex(colors.text),
            colorToHex(adjustColor(colors.surface, -5)));
}

QString ThemeManager::generateDialogStyleSheet(const ColorScheme &colors) const
{
    return QString(R"(
        QDialog {
            background-color: %1;
        }
        
        QDialog QLabel {
            color: %2;
        }
        
        QDialog QLineEdit,
        QDialog QTextEdit,
        QDialog QComboBox {
            background-color: %3;
            color: %2;
            border: 1px solid %4;
            border-radius: 3px;
            padding: 4px;
        }
        
        QDialog QLineEdit:focus,
        QDialog QTextEdit:focus,
        QDialog QComboBox:focus {
            border: 2px solid %5;
        }
    )").arg(colorToHex(colors.background),
            colorToHex(colors.text),
            colorToHex(colors.surface),
            colorToHex(colors.border),
            colorToHex(colors.primary));
}

QString ThemeManager::generateMenuStyleSheet(const ColorScheme &colors) const
{
    return QString(R"(
        QMenuBar {
            background-color: %1;
            color: %2;
            border-bottom: 1px solid %3;
        }
        
        QMenuBar::item {
            background-color: transparent;
            padding: 8px 12px;
        }
        
        QMenuBar::item:selected {
            background-color: %4;
        }
        
        QMenu {
            background-color: %5;
            color: %6;
            border: 1px solid %7;
        }
        
        QMenu::item {
            padding: 8px 20px;
        }
        
        QMenu::item:selected {
            background-color: %8;
        }
        
        QMenu::separator {
            height: 1px;
            background-color: %7;
            margin: 4px 0;
        }
    )").arg(colorToHex(colors.surface),
            colorToHex(colors.text),
            colorToHex(colors.border),
            colorToHex(colors.primary),
            colorToHex(colors.surface),
            colorToHex(colors.text),
            colorToHex(colors.border),
            colorToHex(colors.primary));
}

QString ThemeManager::generateToolbarStyleSheet(const ColorScheme &colors) const
{
    return QString(R"(
        QToolBar {
            background-color: %1;
            border: 1px solid %2;
            spacing: 3px;
            padding: 3px;
        }
        
        QToolBar::handle {
            background-color: %3;
            width: 1px;
            margin: 2px;
        }
        
        QToolButton {
            background-color: transparent;
            border: 1px solid transparent;
            border-radius: 3px;
            padding: 4px;
        }
        
        QToolButton:hover {
            background-color: %4;
            border: 1px solid %5;
        }
        
        QToolButton:pressed {
            background-color: %6;
        }
    )").arg(colorToHex(colors.surface),
            colorToHex(colors.border),
            colorToHex(colors.border),
            colorToHex(adjustColor(colors.surface, -5)),
            colorToHex(colors.border),
            colorToHex(adjustColor(colors.surface, -10)));
}

QString ThemeManager::generateStatusBarStyleSheet(const ColorScheme &colors) const
{
    return QString(R"(
        QStatusBar {
            background-color: %1;
            color: %2;
            border-top: 1px solid %3;
        }
        
        QStatusBar::item {
            border: none;
        }
        
        QProgressBar {
            border: 1px solid %3;
            border-radius: 3px;
            text-align: center;
        }
        
        QProgressBar::chunk {
            background-color: %4;
            border-radius: 2px;
        }
    )").arg(colorToHex(colors.surface),
            colorToHex(colors.text),
            colorToHex(colors.border),
            colorToHex(colors.primary));
}

QString ThemeManager::colorToHex(const QColor &color) const
{
    return color.name();
}

QString ThemeManager::colorToRgba(const QColor &color) const
{
    return QString("rgba(%1, %2, %3, %4)")
           .arg(color.red())
           .arg(color.green())
           .arg(color.blue())
           .arg(color.alphaF(), 0, 'f', 2);
}

QColor ThemeManager::adjustColor(const QColor &color, int lightness, int saturation) const
{
    QColor adjusted = color;
    adjusted.setHsl(adjusted.hue(), 
                   qBound(0, adjusted.saturation() + saturation, 255),
                   qBound(0, adjusted.lightness() + lightness, 255));
    return adjusted;
}

QColor ThemeManager::blendColors(const QColor &color1, const QColor &color2, double ratio) const
{
    return QColor::fromRgbF(
        color1.redF() * (1 - ratio) + color2.redF() * ratio,
        color1.greenF() * (1 - ratio) + color2.greenF() * ratio,
        color1.blueF() * (1 - ratio) + color2.blueF() * ratio,
        color1.alphaF() * (1 - ratio) + color2.alphaF() * ratio
    );
}

bool ThemeManager::isDarkTheme(const ColorScheme &scheme) const
{
    // Simple heuristic: if background is dark, it's a dark theme
    return scheme.background.lightness() < 128;
}

QString ThemeManager::getThemeDirectory() const
{
    return QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/themes";
}

bool ThemeManager::ensureThemeDirectory() const
{
    QString themeDir = getThemeDirectory();
    QDir dir(themeDir);
    if (!dir.exists()) {
        return dir.mkpath(themeDir);
    }
    return true;
}

void ThemeManager::onApplicationStyleChanged()
{
    // Reapply theme when application style changes
    if (QApplication *app = qobject_cast<QApplication*>(QApplication::instance())) {
        applyThemeToApplication(app);
    }
} 