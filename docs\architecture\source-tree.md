# 源代码树结构

## 目录结构

```plaintext
high-jump-scorer/
├── CMakeLists.txt              # 主CMake构建配置文件
│
├── src/                        # 应用程序所有源代码
│   ├── main.cpp                # 应用程序主入口点
│   ├── api/                    # 负责与服务器API通信的模块
│   │   ├── api_client.h
│   │   └── api_client.cpp
│   ├── core/                   # 核心业务逻辑 (比赛规则引擎)
│   │   ├── rules_engine.h
│   │   ├── rules_engine.cpp
│   │   ├── competition_state.h
│   │   └── competition_state.cpp
│   ├── models/                 # 数据模型
│   │   ├── competition_model.h
│   │   ├── competition_model.cpp
│   │   ├── athlete_model.h
│   │   └── athlete_model.cpp
│   ├── persistence/            # 持久化层模块 (SQLite)
│   │   ├── database_manager.h
│   │   └── database_manager.cpp
│   ├── ui/                     # 用户界面 (窗口和自定义控件)
│   │   ├── main_window.h
│   │   ├── main_window.cpp
│   │   ├── competition_view.h
│   │   ├── competition_view.cpp
│   │   ├── athlete_table.h
│   │   └── athlete_table.cpp
│   └── utils/                  # 通用工具或辅助函数
│       ├── config_manager.h
│       ├── config_manager.cpp
│       ├── export_utils.h
│       └── export_utils.cpp
│
├── resources/                  # 资源文件 (图标、样式表等)
│   ├── icons/
│   ├── styles/
│   │   └── theme.qss
│   └── resources.qrc
│
├── tests/                      # 测试代码
│   ├── unit/                   # 单元测试
│   │   ├── test_rules_engine.cpp
│   │   ├── test_database_manager.cpp
│   │   └── test_competition_model.cpp
│   └── integration/            # 集成测试
│       └── test_api_integration.cpp
│
├── docs/                       # 项目文档
│   ├── prd/                    # 产品需求文档
│   ├── architecture/           # 架构文档
│   └── stories/                # 用户故事
│
├── scripts/                    # 构建和部署脚本
│   ├── build.sh
│   └── deploy.sh
│
├── config.ini                  # 配置文件模板
├── .gitignore
└── README.md
```

## 模块职责说明

### src/api/
**职责**: 处理与服务器的所有网络通信
- `ApiClient`: 单例类，管理所有API请求
- 网络状态检测和重连机制
- 请求队列和离线同步管理

### src/core/
**职责**: 核心业务逻辑和规则引擎
- `RulesEngine`: 实现跳高竞赛规则
- `CompetitionState`: 维护比赛状态
- 排名计算和比赛流程控制

### src/models/
**职责**: 数据模型和状态管理
- `CompetitionModel`: 主要数据模型，实现Qt Model/View架构
- `AthleteModel`: 运动员数据管理
- 与持久化层和UI层的数据桥接

### src/persistence/
**职责**: 数据持久化和数据库操作
- `DatabaseManager`: SQLite数据库管理
- 数据库初始化和版本管理
- 离线数据存储和同步队列管理

### src/ui/
**职责**: 用户界面和交互逻辑
- `MainWindow`: 主窗口和布局管理
- `CompetitionView`: 赛事选择和主计分界面
- `AthleteTable`: 运动员成绩表格控件

### src/utils/
**职责**: 通用工具和辅助功能
- `ConfigManager`: 配置文件管理
- `ExportUtils`: PDF和CSV导出功能
- 其他通用工具函数

## 文件命名规范

### 源文件规范
- 头文件: `.h` 扩展名
- 实现文件: `.cpp` 扩展名
- Qt UI文件: `.ui` 扩展名
- 资源文件: `.qrc` 扩展名

### 目录命名规范
- 使用小写字母和下划线
- 保持简洁和描述性
- 避免过深的嵌套层级

## 依赖关系

### 模块依赖层次
```
UI层 (src/ui/)
    ↓
模型层 (src/models/)
    ↓
核心层 (src/core/)
    ↓
持久化层 (src/persistence/) ← → API层 (src/api/)
    ↓
工具层 (src/utils/)
```

### 依赖原则
- 上层模块可以依赖下层模块
- 同层模块通过接口或信号槽通信
- 避免循环依赖
- 核心层不依赖UI层和API层