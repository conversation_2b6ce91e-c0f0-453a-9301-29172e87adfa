#include "report_generator.h"
#include <QApplication>
#include <QDebug>
#include <QFile>
#include <QTextStream>
#include <QTextDocumentWriter>
#include <QTextCursor>
#include <QTextTable>
#include <QTextTableFormat>
#include <QTextCharFormat>
#include <QTextBlockFormat>
#include <QTextList>
#include <QTextListFormat>
#include <QPrinter>
#include <QPrintDialog>
#include <QPrintPreviewDialog>
#include <QPrintPreviewWidget>
#include <QDialog>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QLabel>
#include <QProgressBar>
#include <QMessageBox>
#include <QFileDialog>
#include <QStandardPaths>
#include <QDir>
#include <QDateTime>
#include <QTime>
#include <QRegularExpression>
#include <QRegularExpressionMatch>

ReportGenerator::ReportGenerator(QObject *parent)
    : QObject(parent)
{
    initializeDefaultTemplates();
}

ReportGenerator::~ReportGenerator()
{
    // Smart pointers will automatically clean up
}

bool ReportGenerator::generateReport(ReportType type, ReportFormat format, const QString &outputPath, 
                                   const ReportData &data)
{
    emit reportGenerationStarted(QString("Generating %1 report").arg(getFormatExtension(format)));
    emit reportGenerationProgress(10);

    if (!validateReportData(data)) {
        emit reportGenerationFailed("Invalid report data");
        return false;
    }

    emit reportGenerationProgress(30);

    bool success = false;
    switch (format) {
        case PDF:
            success = generatePDFReport(type, outputPath, data);
            break;
        case Excel:
            success = generateExcelReport(type, outputPath, data);
            break;
        case HTML:
            success = generateHTMLReport(type, outputPath, data);
            break;
    }

    emit reportGenerationProgress(90);

    if (success) {
        emit reportGenerationCompleted(outputPath);
        emit reportGenerationProgress(100);
    } else {
        emit reportGenerationFailed("Failed to generate report");
    }

    return success;
}

bool ReportGenerator::generateReportFromTemplate(const QString &templateName, ReportFormat format, 
                                                const QString &outputPath, const ReportData &data)
{
    // Find the template
    ReportTemplate reportTemplate;
    bool found = false;
    for (const auto &t : m_templates) {
        if (t.name == templateName) {
            reportTemplate = t;
            found = true;
            break;
        }
    }

    if (!found) {
        emit reportGenerationFailed(QString("Template '%1' not found").arg(templateName));
        return false;
    }

    return generateReport(reportTemplate.type, format, outputPath, data);
}

QList<ReportGenerator::ReportTemplate> ReportGenerator::getAvailableTemplates() const
{
    return m_templates;
}

bool ReportGenerator::addTemplate(const ReportTemplate &reportTemplate)
{
    // Check if template with same name already exists
    for (const auto &t : m_templates) {
        if (t.name == reportTemplate.name) {
            return false;
        }
    }

    m_templates.append(reportTemplate);
    return true;
}

bool ReportGenerator::removeTemplate(const QString &templateName)
{
    for (int i = 0; i < m_templates.size(); ++i) {
        if (m_templates[i].name == templateName) {
            m_templates.removeAt(i);
            return true;
        }
    }
    return false;
}

bool ReportGenerator::updateTemplate(const QString &templateName, const ReportTemplate &reportTemplate)
{
    for (int i = 0; i < m_templates.size(); ++i) {
        if (m_templates[i].name == templateName) {
            m_templates[i] = reportTemplate;
            return true;
        }
    }
    return false;
}

QWidget* ReportGenerator::createPreviewWidget(ReportType type, const ReportData &data)
{
    if (!m_document) {
        m_document.reset(new QTextDocument(this));
    }
    
    m_document->setHtml(generateHTMLContent(type, data));
    
    QPrintPreviewWidget *previewWidget = new QPrintPreviewWidget(m_document.data());
    return previewWidget;
}

bool ReportGenerator::showPreviewDialog(ReportType type, const ReportData &data, QWidget *parent)
{
    if (!m_document) {
        m_document.reset(new QTextDocument(this));
    }

    m_document->setHtml(generateHTMLContent(type, data));

    QPrinter printer(QPrinter::HighResolution);
    QPrintPreviewDialog dialog(&printer, parent);
    
    connect(&dialog, &QPrintPreviewDialog::paintRequested, 
            [this, &printer](QPrinter *previewPrinter) {
        m_document->print(previewPrinter);
    });

    return dialog.exec() == QDialog::Accepted;
}

QString ReportGenerator::getDefaultOutputPath(ReportType type, ReportFormat format) const
{
    QString documentsPath = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation);
    QString fileName = QString("high_jump_report_%1_%2.%3")
                      .arg(QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss"))
                      .arg(type)
                      .arg(getFormatExtension(format));
    
    return QDir(documentsPath).filePath(fileName);
}

bool ReportGenerator::validateReportData(const ReportData &data) const
{
    if (data.title.isEmpty()) {
        return false;
    }
    
    if (data.competitionData.isEmpty() && data.athletesData.isEmpty()) {
        return false;
    }
    
    return true;
}

QString ReportGenerator::getFormatExtension(ReportFormat format) const
{
    switch (format) {
        case PDF: return "pdf";
        case Excel: return "xlsx";
        case HTML: return "html";
        default: return "txt";
    }
}

void ReportGenerator::cancelGeneration()
{
    // Cancel any ongoing report generation
    if (m_printer && m_printer->isActive()) {
        m_printer->abort();
    }
    
    // Clear any pending operations
    if (m_document) {
        m_document->clear();
    }
    
    emit reportGenerationFailed("Report generation cancelled by user");
}

bool ReportGenerator::generatePDFReport(ReportType type, const QString &outputPath, const ReportData &data)
{
    if (!m_printer) {
        m_printer.reset(new QPrinter(QPrinter::HighResolution));
    }

    m_printer->setOutputFormat(QPrinter::PdfFormat);
    m_printer->setOutputFileName(outputPath);
    m_printer->setPageMargins(20, 20, 20, 20, QPrinter::Millimeter);

    if (!m_document) {
        m_document.reset(new QTextDocument(this));
    }

    m_document->setHtml(generateHTMLContent(type, data));
    
    m_document->print(m_printer.data());
    return true;
}

bool ReportGenerator::generateExcelReport(ReportType type, const QString &outputPath, const ReportData &data)
{
    // For Excel, we'll generate CSV format which can be opened in Excel
    // In a full implementation, you might want to use a library like OpenXLSX or similar
    QString csvContent = generateCSVContent(type, data);
    
    QFile file(outputPath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        return false;
    }

    QTextStream out(&file);
    out.setEncoding(QStringConverter::Utf8);
    out << csvContent;
    file.close();

    return true;
}

bool ReportGenerator::generateHTMLReport(ReportType type, const QString &outputPath, const ReportData &data)
{
    QString htmlContent = generateHTMLContent(type, data);
    
    QFile file(outputPath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        return false;
    }

    QTextStream out(&file);
    out.setEncoding(QStringConverter::Utf8);
    out << htmlContent;
    file.close();

    return true;
}

QTextDocument* ReportGenerator::createTextDocument(ReportType type, const ReportData &data)
{
    QTextDocument *doc = new QTextDocument();
    doc->setHtml(generateHTMLContent(type, data));
    return doc;
}

QString ReportGenerator::generateHTMLContent(ReportType type, const ReportData &data)
{
    QString html = R"(
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>%1</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .title { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
        .subtitle { font-size: 16px; color: #666; margin-bottom: 20px; }
        .section { margin-bottom: 25px; }
        .section-title { font-size: 18px; font-weight: bold; margin-bottom: 10px; border-bottom: 2px solid #333; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
        .stats { display: flex; justify-content: space-around; margin: 20px 0; }
        .stat-item { text-align: center; }
        .stat-value { font-size: 24px; font-weight: bold; color: #333; }
        .stat-label { font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="header">
        <div class="title">%1</div>
        <div class="subtitle">%2</div>
        <div>Generated on: %3</div>
    </div>
)";

    html = html.arg(data.title, data.subtitle, formatDateTime(data.generatedAt));

    switch (type) {
        case CompetitionSummary:
            html += generateCompetitionSummaryHTML(data);
            break;
        case AthleteDetails:
            html += generateAthleteDetailsHTML(data);
            break;
        case ResultsTable:
            html += generateResultsTableHTML(data);
            break;
        case PerformanceAnalysis:
            html += generatePerformanceAnalysisHTML(data);
            break;
        default:
            html += generateCustomReportHTML(data);
            break;
    }

    html += R"(
    <div class="footer">
        <p>High Jump Competition Management System</p>
        <p>Report generated automatically</p>
    </div>
</body>
</html>
)";

    return html;
}

QString ReportGenerator::generateCSVContent(ReportType type, const ReportData &data)
{
    QString csv;
    
    switch (type) {
        case ResultsTable:
            csv = "Rank,Athlete Name,Country,Best Height,Attempts,Status\n";
            for (const QVariant &athlete : data.athletesData) {
                QVariantMap athleteMap = athlete.toMap();
                csv += QString("\"%1\",\"%2\",\"%3\",\"%4\",\"%5\",\"%6\"\n")
                      .arg(athleteMap["rank"].toString())
                      .arg(athleteMap["name"].toString())
                      .arg(athleteMap["country"].toString())
                      .arg(formatHeight(athleteMap["bestHeight"].toDouble()))
                      .arg(athleteMap["attempts"].toString())
                      .arg(athleteMap["status"].toString());
            }
            break;
            
        default:
            csv = "Data,Value\n";
            for (auto it = data.statistics.begin(); it != data.statistics.end(); ++it) {
                csv += QString("\"%1\",\"%2\"\n").arg(it.key(), it.value().toString());
            }
            break;
    }
    
    return csv;
}

QString ReportGenerator::generateCompetitionSummaryHTML(const ReportData &data)
{
    QString html = R"(
    <div class="section">
        <div class="section-title">Competition Summary</div>
        <div class="stats">
            <div class="stat-item">
                <div class="stat-value">%1</div>
                <div class="stat-label">Total Athletes</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">%2</div>
                <div class="stat-label">Heights Attempted</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">%3</div>
                <div class="stat-label">Total Attempts</div>
            </div>
        </div>
    </div>
)";

    return html.arg(data.statistics["totalAthletes"].toString(),
                   data.statistics["totalHeights"].toString(),
                   data.statistics["totalAttempts"].toString());
}

QString ReportGenerator::generateAthleteDetailsHTML(const ReportData &data)
{
    QString html = R"(
    <div class="section">
        <div class="section-title">Athlete Details</div>
        <table>
            <tr>
                <th>Rank</th>
                <th>Name</th>
                <th>Country</th>
                <th>Best Height</th>
                <th>Attempts</th>
                <th>Status</th>
            </tr>
)";

    for (const QVariant &athlete : data.athletesData) {
        QVariantMap athleteMap = athlete.toMap();
        html += QString(R"(
            <tr>
                <td>%1</td>
                <td>%2</td>
                <td>%3</td>
                <td>%4</td>
                <td>%5</td>
                <td>%6</td>
            </tr>
        )").arg(athleteMap["rank"].toString(),
                athleteMap["name"].toString(),
                athleteMap["country"].toString(),
                formatHeight(athleteMap["bestHeight"].toDouble()),
                athleteMap["attempts"].toString(),
                athleteMap["status"].toString());
    }

    html += "</table></div>";
    return html;
}

QString ReportGenerator::generateResultsTableHTML(const ReportData &data)
{
    return generateAthleteDetailsHTML(data); // Similar structure for results
}

QString ReportGenerator::generatePerformanceAnalysisHTML(const ReportData &data)
{
    QString html = R"(
    <div class="section">
        <div class="section-title">Performance Analysis</div>
        <div class="stats">
            <div class="stat-item">
                <div class="stat-value">%1</div>
                <div class="stat-label">Average Height</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">%2</div>
                <div class="stat-label">Success Rate</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">%3</div>
                <div class="stat-label">Competition Duration</div>
            </div>
        </div>
    </div>
)";

    return html.arg(formatHeight(data.statistics["averageHeight"].toDouble()),
                   QString("%1%").arg(data.statistics["successRate"].toDouble()),
                   data.statistics["duration"].toString());
}

QString ReportGenerator::generateCustomReportHTML(const ReportData &data)
{
    QString html = R"(
    <div class="section">
        <div class="section-title">Custom Report</div>
        <table>
            <tr><th>Metric</th><th>Value</th></tr>
)";

    for (auto it = data.statistics.begin(); it != data.statistics.end(); ++it) {
        html += QString("<tr><td>%1</td><td>%2</td></tr>")
                .arg(it.key(), it.value().toString());
    }

    html += "</table></div>";
    return html;
}

QString ReportGenerator::processTemplate(const QString &templateContent, const ReportData &data)
{
    QString processed = templateContent;
    
    // Replace basic placeholders
    processed.replace("{{TITLE}}", data.title);
    processed.replace("{{SUBTITLE}}", data.subtitle);
    processed.replace("{{GENERATED_AT}}", formatDateTime(data.generatedAt));
    
    // Replace competition data placeholders
    for (auto it = data.competitionData.begin(); it != data.competitionData.end(); ++it) {
        processed.replace(QString("{{COMPETITION_%1}}").arg(it.key().toUpper()), it.value().toString());
    }
    
    return processed;
}

QString ReportGenerator::replacePlaceholders(const QString &content, const QVariantMap &data)
{
    QString result = content;
    for (auto it = data.begin(); it != data.end(); ++it) {
        result.replace(QString("{{%1}}").arg(it.key()), it.value().toString());
    }
    return result;
}

QString ReportGenerator::formatDateTime(const QDateTime &dateTime) const
{
    return dateTime.toString("yyyy-MM-dd hh:mm:ss");
}

QString ReportGenerator::formatHeight(double height) const
{
    return QString("%1 cm").arg(height, 0, 'f', 1);
}

QString ReportGenerator::formatTime(const QTime &time) const
{
    return time.toString("hh:mm:ss");
}

QString ReportGenerator::formatStatistics(const QVariantMap &stats) const
{
    QString result;
    for (auto it = stats.begin(); it != stats.end(); ++it) {
        if (!result.isEmpty()) result += ", ";
        result += QString("%1: %2").arg(it.key(), it.value().toString());
    }
    return result;
}

void ReportGenerator::initializeDefaultTemplates()
{
    // Competition Summary Template
    ReportTemplate summaryTemplate;
    summaryTemplate.name = "Competition Summary";
    summaryTemplate.description = "Standard competition summary report";
    summaryTemplate.type = CompetitionSummary;
    summaryTemplate.templatePath = "";
    summaryTemplate.defaultSettings["includeStatistics"] = true;
    summaryTemplate.defaultSettings["includeAthleteList"] = true;
    m_templates.append(summaryTemplate);

    // Results Table Template
    ReportTemplate resultsTemplate;
    resultsTemplate.name = "Results Table";
    resultsTemplate.description = "Detailed results table with rankings";
    resultsTemplate.type = ResultsTable;
    resultsTemplate.templatePath = "";
    resultsTemplate.defaultSettings["includeAttempts"] = true;
    resultsTemplate.defaultSettings["includeTiming"] = false;
    m_templates.append(resultsTemplate);

    // Performance Analysis Template
    ReportTemplate analysisTemplate;
    analysisTemplate.name = "Performance Analysis";
    analysisTemplate.description = "Statistical analysis of competition performance";
    analysisTemplate.type = PerformanceAnalysis;
    analysisTemplate.templatePath = "";
    analysisTemplate.defaultSettings["includeCharts"] = true;
    analysisTemplate.defaultSettings["includeTrends"] = true;
    m_templates.append(analysisTemplate);
}

QString ReportGenerator::getDefaultTemplate(ReportType type) const
{
    for (const auto &reportTemplate : m_templates) {
        if (reportTemplate.type == type) {
            return reportTemplate.name;
        }
    }
    return QString();
}

void ReportGenerator::onPrinterError(int error)
{
    QString errorMessage;
    switch (error) {
        case QPrinter::NoError:
            return;
        case QPrinter::OutOfPaper:
            errorMessage = "Printer is out of paper";
            break;
        case QPrinter::OutOfMemory:
            errorMessage = "Printer is out of memory";
            break;
        case QPrinter::UnknownError:
        default:
            errorMessage = "Unknown printer error";
            break;
    }
    
    emit reportGenerationFailed(errorMessage);
} 