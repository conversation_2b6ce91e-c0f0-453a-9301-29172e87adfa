# High Jump Competition Management System - Final Verification Test
# This script performs comprehensive verification of Sprint 2 implementation

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "High Jump Competition Management System" -ForegroundColor Cyan
Write-Host "Sprint 2 Final Verification Test" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Store original location
$originalLocation = Get-Location

# Verification results
$verificationResults = @()

# Function to add verification result
function Add-VerificationResult {
    param($Component, $Status, $Details)
    $verificationResults += [PSCustomObject]@{
        Component = $Component
        Status = $Status
        Details = $Details
    }
}

# 1. Verify Sprint 2 Component Files
Write-Host "1. Verifying Sprint 2 Component Files..." -ForegroundColor Yellow

$sprint2Components = @(
    @{Name="ReportGenerator"; Path="src/utils/report_generator.h"},
    @{Name="ReportGenerator Impl"; Path="src/utils/report_generator.cpp"},
    @{Name="PerformanceMonitor"; Path="src/utils/performance_monitor.h"},
    @{Name="PerformanceMonitor Impl"; Path="src/utils/performance_monitor.cpp"},
    @{Name="ThemeManager"; Path="src/ui/theme_manager.h"},
    @{Name="ThemeManager Impl"; Path="src/ui/theme_manager.cpp"},
    @{Name="ReportDialog"; Path="src/ui/report_dialog.h"},
    @{Name="ReportDialog Impl"; Path="src/ui/report_dialog.cpp"}
)

foreach ($component in $sprint2Components) {
    if (Test-Path $component.Path) {
        Write-Host "  ✓ $($component.Name): Found" -ForegroundColor Green
        Add-VerificationResult $component.Name "✅ VERIFIED" "File exists at $($component.Path)"
    } else {
        Write-Host "  ✗ $($component.Name): Missing" -ForegroundColor Red
        Add-VerificationResult $component.Name "❌ MISSING" "File not found at $($component.Path)"
    }
}

Write-Host ""

# 2. Verify Test Infrastructure
Write-Host "2. Verifying Test Infrastructure..." -ForegroundColor Yellow

$testFiles = @(
    @{Name="Database Manager Test"; Path="build/bin/Release/test_database_manager.exe"},
    @{Name="Config Manager Test"; Path="build/bin/Release/test_config_manager.exe"},
    @{Name="API Client Test"; Path="build/bin/Release/test_api_client.exe"},
    @{Name="E2E Workflow Test"; Path="build/bin/Release/test_e2e_workflow.exe"}
)

foreach ($test in $testFiles) {
    if (Test-Path $test.Path) {
        Write-Host "  ✓ $($test.Name): Built" -ForegroundColor Green
        Add-VerificationResult $test.Name "✅ BUILT" "Executable exists"
    } else {
        Write-Host "  ✗ $($test.Name): Not Built" -ForegroundColor Red
        Add-VerificationResult $test.Name "❌ NOT BUILT" "Executable missing"
    }
}

Write-Host ""

# 3. Run Core Tests
Write-Host "3. Running Core Test Suite..." -ForegroundColor Yellow

if (Test-Path "build/bin/Release") {
    Set-Location "build/bin/Release"
    
    # Set Qt environment
    $env:QT_PLUGIN_PATH = $PWD.Path
    $env:QT_DEBUG_PLUGINS = "0"
    
    $coreTests = @("test_database_manager.exe", "test_config_manager.exe", "test_api_client.exe")
    $passedTests = 0
    $totalTests = $coreTests.Count
    
    foreach ($test in $coreTests) {
        if (Test-Path $test) {
            Write-Host "  Running $test..." -ForegroundColor Cyan
            try {
                $process = Start-Process -FilePath ".\$test" -Wait -PassThru -NoNewWindow -RedirectStandardOutput "test_output.txt" -RedirectStandardError "test_error.txt"
                if ($process.ExitCode -eq 0) {
                    Write-Host "  ✓ ${test}: PASSED" -ForegroundColor Green
                    Add-VerificationResult $test "✅ PASSED" "All tests passed"
                    $passedTests++
                } else {
                    Write-Host "  ✗ ${test}: FAILED (Exit Code: $($process.ExitCode))" -ForegroundColor Red
                    Add-VerificationResult $test "❌ FAILED" "Exit code: $($process.ExitCode)"
                }
            } catch {
                Write-Host "  ✗ ${test}: ERROR ($($_.Exception.Message))" -ForegroundColor Red
                Add-VerificationResult $test "❌ ERROR" $_.Exception.Message
            }
        }
    }
    
    Write-Host ""
    Write-Host "Core Test Results: $passedTests/$totalTests passed" -ForegroundColor $(if ($passedTests -eq $totalTests) { "Green" } else { "Yellow" })
    
    Set-Location $originalLocation
} else {
    Write-Host "  ✗ Release directory not found" -ForegroundColor Red
    Add-VerificationResult "Test Environment" "❌ FAILED" "Release directory missing"
}

Write-Host ""

# 4. Verify Documentation
Write-Host "4. Verifying Documentation..." -ForegroundColor Yellow

$docFiles = @(
    @{Name="Sprint 2 Implementation"; Path="docs/stories/2.0.sprint-2-implementation-complete.md"},
    @{Name="QA Report"; Path="docs/qa/qa-report-sprint-2.md"},
    @{Name="Test Setup Guide"; Path="TEST_SETUP_GUIDE.md"},
    @{Name="Verification Report"; Path="SPRINT_2_VERIFICATION_REPORT.md"}
)

foreach ($doc in $docFiles) {
    if (Test-Path $doc.Path) {
        Write-Host "  ✓ $($doc.Name): Available" -ForegroundColor Green
        Add-VerificationResult $doc.Name "✅ AVAILABLE" "Documentation exists"
    } else {
        Write-Host "  ✗ $($doc.Name): Missing" -ForegroundColor Red
        Add-VerificationResult $doc.Name "❌ MISSING" "Documentation not found"
    }
}

Write-Host ""

# 5. Generate Final Report
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Final Verification Summary" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

$totalComponents = $verificationResults.Count
$verifiedComponents = ($verificationResults | Where-Object { $_.Status -like "*✅*" }).Count
$failedComponents = ($verificationResults | Where-Object { $_.Status -like "*❌*" }).Count

Write-Host ""
Write-Host "Overall Results:" -ForegroundColor White
Write-Host "  Total Components Checked: $totalComponents" -ForegroundColor White
Write-Host "  Verified Components: $verifiedComponents" -ForegroundColor Green
Write-Host "  Failed Components: $failedComponents" -ForegroundColor $(if ($failedComponents -eq 0) { "Green" } else { "Red" })
Write-Host "  Success Rate: $([math]::Round(($verifiedComponents / $totalComponents) * 100, 1))%" -ForegroundColor $(if ($verifiedComponents -eq $totalComponents) { "Green" } else { "Yellow" })

Write-Host ""
Write-Host "Component Status Details:" -ForegroundColor White
foreach ($result in $verificationResults) {
    $color = if ($result.Status -like "*✅*") { "Green" } else { "Red" }
    Write-Host "  $($result.Component): $($result.Status)" -ForegroundColor $color
}

Write-Host ""

# Final Assessment
if ($verifiedComponents -eq $totalComponents) {
    Write-Host "🎉 SPRINT 2 VERIFICATION: COMPLETE SUCCESS!" -ForegroundColor Green
    Write-Host "✅ All components verified and working correctly" -ForegroundColor Green
    Write-Host "✅ System ready for production deployment" -ForegroundColor Green
} elseif ($verifiedComponents / $totalComponents -ge 0.8) {
    Write-Host "⚠️ SPRINT 2 VERIFICATION: MOSTLY SUCCESSFUL" -ForegroundColor Yellow
    Write-Host "✅ Core functionality verified" -ForegroundColor Green
    Write-Host "⚠️ Some minor issues need attention" -ForegroundColor Yellow
} else {
    Write-Host "❌ SPRINT 2 VERIFICATION: NEEDS ATTENTION" -ForegroundColor Red
    Write-Host "❌ Multiple components need fixes" -ForegroundColor Red
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Verification Complete" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

Read-Host "Press Enter to exit"
