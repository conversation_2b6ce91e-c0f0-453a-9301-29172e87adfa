#ifndef THEME_MANAGER_H
#define THEME_MANAGER_H

#include <QObject>
#include <QString>
#include <QMap>
#include <QColor>
#include <QPalette>
#include <QFont>
#include <QApplication>
#include <QWidget>
#include <QStyle>
#include <QStyleFactory>
#include <QSettings>
#include <QDir>
#include <QFile>
#include <QTextStream>

/**
 * @brief Modern theme management system for High Jump Competition Management
 * 
 * This class provides comprehensive theme management capabilities for the High Jump
 * Competition Management System, including modern themes, color schemes, font management,
 * and user customization options. It implements a sophisticated theming system that
 * supports both predefined themes and user-created custom themes.
 * 
 * The ThemeManager implements the Singleton pattern to ensure consistent theme
 * application throughout the application lifecycle. It provides:
 * - Multiple predefined themes (Light, Dark, High Contrast, Professional, etc.)
 * - Dynamic color scheme management with real-time preview
 * - Font family and size customization
 * - Custom CSS stylesheet support
 * - Theme persistence and user preferences
 * - Automatic theme application to all UI components
 * - Color manipulation and blending utilities
 * 
 * The manager supports both programmatic theme changes and user-initiated
 * customization through the application's theme settings interface.
 */
class ThemeManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief Theme type enumeration
     * 
     * Defines the different types of themes available in the system.
     * Each type represents a distinct visual style and color scheme.
     */
    enum ThemeType {
        Light,          ///< Light theme with bright colors and dark text
        Dark,           ///< Dark theme with dark colors and light text
        HighContrast,   ///< High contrast theme for accessibility
        Professional,   ///< Professional theme with business-appropriate colors
        Modern,         ///< Modern theme with contemporary design elements
        Classic,        ///< Classic theme with traditional appearance
        Custom          ///< User-defined custom theme
    };

    /**
     * @brief Color scheme structure
     * 
     * Defines a complete color scheme with all necessary colors for
     * consistent theming across the application interface.
     */
    struct ColorScheme {
        QString name;           ///< Color scheme name
        QString description;    ///< Color scheme description
        QColor primary;         ///< Primary brand color
        QColor secondary;       ///< Secondary brand color
        QColor accent;          ///< Accent color for highlights
        QColor background;      ///< Main background color
        QColor surface;         ///< Surface/panel background color
        QColor text;            ///< Primary text color
        QColor textSecondary;   ///< Secondary text color
        QColor border;          ///< Border color
        QColor shadow;          ///< Shadow color
        QColor success;         ///< Success state color
        QColor warning;         ///< Warning state color
        QColor error;           ///< Error state color
        QColor info;            ///< Information state color
    };

    /**
     * @brief Complete theme structure
     * 
     * Defines a complete theme including colors, fonts, and stylesheets.
     * This structure contains all elements needed for a complete visual theme.
     */
    struct Theme {
        QString name;           ///< Theme name
        QString description;    ///< Theme description
        ThemeType type;         ///< Theme type
        ColorScheme colors;     ///< Color scheme for the theme
        QString fontFamily;     ///< Font family name
        int fontSize;           ///< Font size in points
        QString styleSheet;     ///< Custom CSS stylesheet
        bool isCustom;          ///< Whether this is a custom theme
    };

    /**
     * @brief Constructs the theme manager
     * @param parent Parent QObject (default: nullptr)
     * 
     * Initializes the theme manager with default themes and color schemes.
     * Sets up the initial theme and loads user preferences.
     */
    explicit ThemeManager(QObject *parent = nullptr);
    
    /**
     * @brief Destructor
     * 
     * Ensures proper cleanup of theme resources and saves user preferences.
     */
    ~ThemeManager();

    // Theme management
    /**
     * @brief Loads a theme by name
     * @param themeName Name of the theme to load
     * @return true if theme loaded successfully, false otherwise
     * 
     * Loads and applies a theme with the specified name. The theme can be
     * either a predefined theme or a custom user-created theme.
     */
    bool loadTheme(const QString &themeName);
    
    /**
     * @brief Loads a theme by type
     * @param type Theme type to load
     * @return true if theme loaded successfully, false otherwise
     * 
     * Loads and applies a predefined theme of the specified type.
     * This is useful for quick theme switching based on theme categories.
     */
    bool loadTheme(ThemeType type);
    
    /**
     * @brief Saves a custom theme
     * @param theme Theme to save
     * @return true if theme saved successfully, false otherwise
     * 
     * Saves a custom theme to persistent storage. The theme will be
     * available for future loading and can be shared between sessions.
     */
    bool saveTheme(const Theme &theme);
    
    /**
     * @brief Deletes a custom theme
     * @param themeName Name of the theme to delete
     * @return true if theme deleted successfully, false otherwise
     * 
     * Removes a custom theme from persistent storage. Predefined themes
     * cannot be deleted.
     */
    bool deleteTheme(const QString &themeName);
    
    // Theme information
    /**
     * @brief Gets all available themes
     * @return List of all available themes
     * 
     * Returns a list of all themes available in the system, including
     * both predefined and custom themes.
     */
    QList<Theme> getAvailableThemes() const;
    
    /**
     * @brief Gets the currently active theme
     * @return Current theme information
     * 
     * Returns the complete theme information for the currently
     * active theme.
     */
    Theme getCurrentTheme() const;
    
    /**
     * @brief Gets the name of the current theme
     * @return Current theme name
     * 
     * Returns the name of the currently active theme.
     */
    QString getCurrentThemeName() const;
    
    /**
     * @brief Gets the type of the current theme
     * @return Current theme type
     * 
     * Returns the type of the currently active theme.
     */
    ThemeType getCurrentThemeType() const;
    
    // Color scheme management
    /**
     * @brief Gets the current color scheme
     * @return Current color scheme
     * 
     * Returns the color scheme of the currently active theme.
     */
    ColorScheme getCurrentColorScheme() const;
    
    /**
     * @brief Sets a new color scheme
     * @param scheme Color scheme to apply
     * @return true if color scheme applied successfully, false otherwise
     * 
     * Applies a new color scheme to the current theme. This allows
     * for dynamic color customization without changing the entire theme.
     */
    bool setColorScheme(const ColorScheme &scheme);
    
    /**
     * @brief Gets all available color schemes
     * @return List of available color schemes
     * 
     * Returns a list of all predefined color schemes available
     * for theme customization.
     */
    QList<ColorScheme> getAvailableColorSchemes() const;
    
    // Font management
    /**
     * @brief Gets the current font family
     * @return Current font family name
     * 
     * Returns the font family currently used by the active theme.
     */
    QString getCurrentFontFamily() const;
    
    /**
     * @brief Gets the current font size
     * @return Current font size in points
     * 
     * Returns the font size currently used by the active theme.
     */
    int getCurrentFontSize() const;
    
    /**
     * @brief Sets a new font
     * @param family Font family name
     * @param size Font size in points
     * @return true if font applied successfully, false otherwise
     * 
     * Applies a new font family and size to the current theme.
     * This allows for dynamic font customization.
     */
    bool setFont(const QString &family, int size);
    
    /**
     * @brief Gets all available fonts
     * @return List of available font families
     * 
     * Returns a list of all font families available on the system
     * for theme customization.
     */
    QList<QString> getAvailableFonts() const;
    
    // Style management
    /**
     * @brief Gets the current stylesheet
     * @return Current CSS stylesheet
     * 
     * Returns the CSS stylesheet currently applied to the application.
     */
    QString getCurrentStyleSheet() const;
    
    /**
     * @brief Sets a new stylesheet
     * @param styleSheet CSS stylesheet to apply
     * @return true if stylesheet applied successfully, false otherwise
     * 
     * Applies a custom CSS stylesheet to the application. This allows
     * for fine-grained control over the visual appearance.
     */
    bool setStyleSheet(const QString &styleSheet);
    
    /**
     * @brief Loads a stylesheet from file
     * @param filePath Path to the CSS file
     * @return true if stylesheet loaded successfully, false otherwise
     * 
     * Loads and applies a CSS stylesheet from a file. This is useful
     * for external theme files or user-created stylesheets.
     */
    bool loadStyleSheetFromFile(const QString &filePath);
    
    /**
     * @brief Saves the current stylesheet to file
     * @param filePath Path where to save the CSS file
     * @return true if stylesheet saved successfully, false otherwise
     * 
     * Saves the current CSS stylesheet to a file for backup or sharing.
     */
    bool saveStyleSheetToFile(const QString &filePath);
    
    // Theme application
    /**
     * @brief Applies theme to the entire application
     * @param app QApplication instance to theme
     * 
     * Applies the current theme to the entire Qt application, including
     * all widgets and dialogs. This is the primary method for theme application.
     */
    void applyThemeToApplication(QApplication *app);
    
    /**
     * @brief Applies theme to a specific widget
     * @param widget Widget to apply theme to
     * 
     * Applies the current theme to a specific widget and its children.
     * Useful for theming individual components or dialogs.
     */
    void applyThemeToWidget(QWidget *widget);
    
    /**
     * @brief Applies theme to all widgets
     * 
     * Applies the current theme to all widgets in the application.
     * This ensures consistent theming across the entire interface.
     */
    void applyThemeToAllWidgets();
    
    // Custom theme creation
    /**
     * @brief Creates a custom theme
     * @param name Name for the custom theme
     * @param colors Color scheme for the theme
     * @param fontFamily Font family (optional, uses current if empty)
     * @param fontSize Font size (optional, uses current if 0)
     * @return true if theme created successfully, false otherwise
     * 
     * Creates a new custom theme with the specified parameters.
     * The theme will be saved and available for future use.
     */
    bool createCustomTheme(const QString &name, const ColorScheme &colors, 
                          const QString &fontFamily = QString(), int fontSize = 0);
    
    /**
     * @brief Updates an existing custom theme
     * @param name Name of the theme to update
     * @param theme New theme data
     * @return true if theme updated successfully, false otherwise
     * 
     * Updates an existing custom theme with new data.
     * The updated theme will be saved to persistent storage.
     */
    bool updateCustomTheme(const QString &name, const Theme &theme);
    
    // Settings management
    /**
     * @brief Saves theme settings
     * 
     * Saves the current theme configuration and user preferences
     * to persistent storage for future sessions.
     */
    void saveSettings();
    
    /**
     * @brief Loads theme settings
     * 
     * Loads theme configuration and user preferences from
     * persistent storage.
     */
    void loadSettings();
    
    /**
     * @brief Resets theme to defaults
     * 
     * Resets the theme to the default system theme and clears
     * all custom settings.
     */
    void resetToDefaults();

signals:
    /**
     * @brief Emitted when theme changes
     * @param themeName Name of the new theme
     * 
     * Signal emitted when a new theme is loaded and applied.
     * Used for UI updates and theme change notifications.
     */
    void themeChanged(const QString &themeName);
    
    /**
     * @brief Emitted when color scheme changes
     * @param scheme New color scheme
     * 
     * Signal emitted when the color scheme is updated.
     * Used for real-time color preview and UI updates.
     */
    void colorSchemeChanged(const ColorScheme &scheme);
    
    /**
     * @brief Emitted when font changes
     * @param family New font family
     * @param size New font size
     * 
     * Signal emitted when the font family or size is changed.
     * Used for font preview and UI updates.
     */
    void fontChanged(const QString &family, int size);
    
    /**
     * @brief Emitted when stylesheet changes
     * @param styleSheet New CSS stylesheet
     * 
     * Signal emitted when the CSS stylesheet is updated.
     * Used for stylesheet preview and validation.
     */
    void styleSheetChanged(const QString &styleSheet);
    
    /**
     * @brief Emitted when settings are saved
     * 
     * Signal emitted when theme settings are successfully saved.
     * Used for confirmation and status updates.
     */
    void settingsSaved();
    
    /**
     * @brief Emitted when settings are loaded
     * 
     * Signal emitted when theme settings are successfully loaded.
     * Used for initialization confirmation.
     */
    void settingsLoaded();

private slots:
    /**
     * @brief Handles application style changes
     * 
     * Slot for handling system-level style changes.
     * Ensures theme consistency when system appearance changes.
     */
    void onApplicationStyleChanged();

private:
    // Initialization methods
    /**
     * @brief Initializes default themes
     * 
     * Creates and registers all predefined themes including
     * Light, Dark, High Contrast, Professional, Modern, and Classic themes.
     */
    void initializeDefaultThemes();
    
    /**
     * @brief Creates the light theme
     * 
     * Creates and registers the default light theme with
     * bright colors and dark text for good readability.
     */
    void createLightTheme();
    
    /**
     * @brief Creates the dark theme
     * 
     * Creates and registers the default dark theme with
     * dark colors and light text for reduced eye strain.
     */
    void createDarkTheme();
    
    /**
     * @brief Creates the high contrast theme
     * 
     * Creates and registers the high contrast theme for
     * accessibility and improved visibility.
     */
    void createHighContrastTheme();
    
    /**
     * @brief Creates the professional theme
     * 
     * Creates and registers the professional theme with
     * business-appropriate colors and styling.
     */
    void createProfessionalTheme();
    
    /**
     * @brief Creates the modern theme
     * 
     * Creates and registers the modern theme with
     * contemporary design elements and colors.
     */
    void createModernTheme();
    
    /**
     * @brief Creates the classic theme
     * 
     * Creates and registers the classic theme with
     * traditional appearance and colors.
     */
    void createClassicTheme();
    
    /**
     * @brief Initializes default color schemes
     * 
     * Creates and registers all predefined color schemes
     * for use in theme customization.
     */
    void initializeDefaultColorSchemes();
    
    // Stylesheet generation methods
    /**
     * @brief Generates a complete stylesheet
     * @param colors Color scheme to use
     * @param fontFamily Font family (optional)
     * @param fontSize Font size (optional)
     * @return Generated CSS stylesheet
     * 
     * Generates a complete CSS stylesheet based on the provided
     * color scheme and font settings.
     */
    QString generateStyleSheet(const ColorScheme &colors, const QString &fontFamily = QString(), int fontSize = 0) const;
    
    /**
     * @brief Generates a Qt palette
     * @param colors Color scheme to use
     * @return Generated QPalette
     * 
     * Generates a Qt palette object from the provided color scheme
     * for native widget theming.
     */
    QPalette generatePalette(const ColorScheme &colors) const;
    
    /**
     * @brief Validates a color scheme
     * @param scheme Color scheme to validate
     * @return true if color scheme is valid, false otherwise
     * 
     * Validates that a color scheme contains all required colors
     * and has appropriate contrast ratios.
     */
    bool isValidColorScheme(const ColorScheme &scheme) const;
    
    /**
     * @brief Gets the file path for a theme
     * @param themeName Name of the theme
     * @return File path for the theme
     * 
     * Returns the file path where a theme is stored or should be stored.
     */
    QString getThemeFilePath(const QString &themeName) const;
    
    // Component-specific stylesheet generation
    /**
     * @brief Generates base stylesheet
     * @param colors Color scheme to use
     * @return Base CSS stylesheet
     * 
     * Generates the base CSS stylesheet with fundamental styling
     * for the application.
     */
    QString generateBaseStyleSheet(const ColorScheme &colors) const;
    
    /**
     * @brief Generates widget stylesheet
     * @param colors Color scheme to use
     * @return Widget CSS stylesheet
     * 
     * Generates CSS stylesheet for general widgets and containers.
     */
    QString generateWidgetStyleSheet(const ColorScheme &colors) const;
    
    /**
     * @brief Generates button stylesheet
     * @param colors Color scheme to use
     * @return Button CSS stylesheet
     * 
     * Generates CSS stylesheet for buttons and button-like controls.
     */
    QString generateButtonStyleSheet(const ColorScheme &colors) const;
    
    /**
     * @brief Generates table stylesheet
     * @param colors Color scheme to use
     * @return Table CSS stylesheet
     * 
     * Generates CSS stylesheet for tables and table-like widgets.
     */
    QString generateTableStyleSheet(const ColorScheme &colors) const;
    
    /**
     * @brief Generates dialog stylesheet
     * @param colors Color scheme to use
     * @return Dialog CSS stylesheet
     * 
     * Generates CSS stylesheet for dialogs and modal windows.
     */
    QString generateDialogStyleSheet(const ColorScheme &colors) const;
    
    /**
     * @brief Generates menu stylesheet
     * @param colors Color scheme to use
     * @return Menu CSS stylesheet
     * 
     * Generates CSS stylesheet for menus and menu items.
     */
    QString generateMenuStyleSheet(const ColorScheme &colors) const;
    
    /**
     * @brief Generates toolbar stylesheet
     * @param colors Color scheme to use
     * @return Toolbar CSS stylesheet
     * 
     * Generates CSS stylesheet for toolbars and toolbar items.
     */
    QString generateToolbarStyleSheet(const ColorScheme &colors) const;
    
    /**
     * @brief Generates status bar stylesheet
     * @param colors Color scheme to use
     * @return Status bar CSS stylesheet
     * 
     * Generates CSS stylesheet for status bars and status items.
     */
    QString generateStatusBarStyleSheet(const ColorScheme &colors) const;
    
    // Color utility methods
    /**
     * @brief Converts color to hex string
     * @param color Color to convert
     * @return Hex color string
     * 
     * Converts a QColor to a hex color string for CSS.
     */
    QString colorToHex(const QColor &color) const;
    
    /**
     * @brief Converts color to RGBA string
     * @param color Color to convert
     * @return RGBA color string
     * 
     * Converts a QColor to an RGBA color string for CSS.
     */
    QString colorToRgba(const QColor &color) const;
    
    /**
     * @brief Adjusts color lightness and saturation
     * @param color Base color
     * @param lightness Lightness adjustment (-100 to 100)
     * @param saturation Saturation adjustment (-100 to 100)
     * @return Adjusted color
     * 
     * Adjusts the lightness and saturation of a color while
     * preserving the hue.
     */
    QColor adjustColor(const QColor &color, int lightness = 0, int saturation = 0) const;
    
    /**
     * @brief Blends two colors
     * @param color1 First color
     * @param color2 Second color
     * @param ratio Blend ratio (0.0 to 1.0)
     * @return Blended color
     * 
     * Blends two colors using the specified ratio.
     * Ratio 0.0 returns color1, ratio 1.0 returns color2.
     */
    QColor blendColors(const QColor &color1, const QColor &color2, double ratio = 0.5) const;
    
    /**
     * @brief Checks if a color scheme is dark
     * @param scheme Color scheme to check
     * @return true if dark theme, false otherwise
     * 
     * Determines if a color scheme represents a dark theme
     * based on background color analysis.
     */
    bool isDarkTheme(const ColorScheme &scheme) const;
    
    // Member variables
    QMap<QString, Theme> m_themes;           ///< Available themes
    QMap<QString, ColorScheme> m_colorSchemes; ///< Available color schemes
    Theme m_currentTheme;                    ///< Current active theme
    QString m_currentThemeName;              ///< Current theme name
    QSettings *m_settings;                   ///< Settings storage
    QString m_themeDirectory;                ///< Theme file directory
    bool m_isInitialized;                    ///< Initialization status
    
    // Color scheme creation methods
    ColorScheme createLightColorScheme() const;
    ColorScheme createDarkColorScheme() const;
    ColorScheme createHighContrastColorScheme() const;
    ColorScheme createProfessionalColorScheme() const;
    ColorScheme createModernColorScheme() const;
    ColorScheme createClassicColorScheme() const;
    
    // Utility methods
    QString getThemeDirectory() const;
    bool ensureThemeDirectory() const;
};

#endif // THEME_MANAGER_H 