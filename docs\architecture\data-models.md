# 数据模型

## 核心业务模型

### Athlete (运动员)

**目的**: 表示参赛运动员的基本信息和状态

**关键属性**:
- `id`: int - 运动员唯一标识符
- `name`: QString - 运动员姓名
- `number`: int - 参赛号码
- `team`: QString - 所属队伍/团体
- `personalBest`: int - 个人最佳成绩(厘米)
- `seasonBest`: int - 赛季最佳成绩(厘米)
- `currentStatus`: AthleteStatus - 当前状态(活跃/淘汰/退赛)

**关系**:
- 一对多关系到 AttemptRecord
- 多对一关系到 Competition

```cpp
class Athlete
{
public:
    enum AthleteStatus {
        Active,      // 仍在比赛中
        Eliminated,  // 已被淘汰
        Retired,     // 主动退赛
        Finished     // 比赛完成
    };

    int id() const;
    QString name() const;
    int number() const;
    QString team() const;
    int personalBest() const;
    int currentBestHeight() const;
    AthleteStatus status() const;
    
private:
    int m_id;
    QString m_name;
    int m_number;
    QString m_team;
    int m_personalBest;
    int m_seasonBest;
    AthleteStatus m_status;
};
```

### Competition (比赛)

**目的**: 表示一场跳高比赛的基本信息和配置

**关键属性**:
- `id`: int - 比赛唯一标识符
- `name`: QString - 比赛名称
- `date`: QDateTime - 比赛日期时间
- `venue`: QString - 比赛场地
- `startingHeight`: int - 起跳高度(厘米)
- `heightIncrement`: int - 升杆幅度(厘米)
- `status`: CompetitionStatus - 比赛状态

**关系**:
- 一对多关系到 Athlete
- 一对多关系到 HeightSetting

```cpp
class Competition
{
public:
    enum CompetitionStatus {
        NotStarted,  // 未开始
        InProgress,  // 进行中
        Paused,      // 暂停
        Finished     // 已结束
    };

    int id() const;
    QString name() const;
    QDateTime date() const;
    QString venue() const;
    QList<int> heightSequence() const;
    CompetitionStatus status() const;
    
private:
    int m_id;
    QString m_name;
    QDateTime m_date;
    QString m_venue;
    int m_startingHeight;
    int m_heightIncrement;
    CompetitionStatus m_status;
    QList<int> m_heightSequence;
};
```

### AttemptRecord (试跳记录)

**目的**: 记录每次试跳的详细信息和结果

**关键属性**:
- `id`: int - 记录唯一标识符
- `athleteId`: int - 运动员ID
- `height`: int - 试跳高度(厘米)
- `attemptNumber`: int - 该高度第几次试跳(1-3)
- `result`: AttemptResult - 试跳结果
- `timestamp`: QDateTime - 试跳时间戳

**关系**:
- 多对一关系到 Athlete
- 多对一关系到 Competition

```cpp
class AttemptRecord
{
public:
    enum AttemptResult {
        Success,    // 成功 (O)
        Failure,    // 失败 (X)
        Pass,       // 免跳 (-)
        Retire      // 退赛 (R)
    };

    int id() const;
    int athleteId() const;
    int height() const;
    int attemptNumber() const;
    AttemptResult result() const;
    QDateTime timestamp() const;
    
    static QString resultToString(AttemptResult result);
    static AttemptResult stringToResult(const QString &str);
    
private:
    int m_id;
    int m_athleteId;
    int m_height;
    int m_attemptNumber;
    AttemptResult m_result;
    QDateTime m_timestamp;
};
```

### RankingEntry (排名条目)

**目的**: 表示实时计算的运动员排名信息

**关键属性**:
- `athleteId`: int - 运动员ID
- `rank`: int - 当前排名
- `bestHeight`: int - 最佳成绩高度
- `totalFailures`: int - 总失败次数
- `failuresAtBest`: int - 最佳高度的失败次数

```cpp
class RankingEntry
{
public:
    int athleteId() const;
    int rank() const;
    int bestHeight() const;
    int totalFailures() const;
    int failuresAtBest() const;
    
    // 排名比较逻辑
    bool operator<(const RankingEntry &other) const;
    
private:
    int m_athleteId;
    int m_rank;
    int m_bestHeight;
    int m_totalFailures;
    int m_failuresAtBest;
};
```

## 状态管理模型

### CompetitionState (比赛状态)

**目的**: 维护当前比赛的实时状态信息

```cpp
class CompetitionState : public QObject
{
    Q_OBJECT

public:
    explicit CompetitionState(QObject *parent = nullptr);
    
    // 状态查询
    int currentHeight() const;
    int currentAthleteId() const;
    QList<RankingEntry> currentRanking() const;
    bool isCompetitionFinished() const;
    
    // 状态更新
    void setCurrentHeight(int height);
    void setCurrentAthlete(int athleteId);
    void recordAttempt(int athleteId, int height, AttemptRecord::AttemptResult result);
    
signals:
    void currentHeightChanged(int newHeight);
    void currentAthleteChanged(int athleteId);
    void rankingUpdated();
    void competitionFinished();

private:
    void updateRanking();
    bool checkIfFinished();
    
    Competition *m_competition;
    QList<Athlete*> m_athletes;
    QList<AttemptRecord*> m_attempts;
    QList<RankingEntry> m_ranking;
    
    int m_currentHeight;
    int m_currentAthleteId;
};
```

## 同步队列模型

### SyncQueueEntry (同步队列条目)

**目的**: 记录需要同步到服务器的操作

```cpp
class SyncQueueEntry
{
public:
    enum OperationType {
        CreateAttempt,
        UpdateAthlete,
        UpdateCompetition
    };
    
    enum SyncStatus {
        Pending,     // 待同步
        InProgress,  // 同步中
        Completed,   // 已完成
        Failed       // 同步失败
    };

    int id() const;
    OperationType operationType() const;
    QJsonObject data() const;
    SyncStatus status() const;
    QDateTime createdAt() const;
    
private:
    int m_id;
    OperationType m_operationType;
    QJsonObject m_data;
    SyncStatus m_status;
    QDateTime m_createdAt;
    QDateTime m_lastAttempt;
    int m_retryCount;
};
```

## 数据传输对象 (DTO)

### API通信数据结构

```cpp
// 服务器响应的比赛数据
struct CompetitionDto
{
    int id;
    QString name;
    QString date;
    QString venue;
    QJsonArray athletes;
    QJsonArray heightSequence;
    QString status;
    
    Competition toModel() const;
    static CompetitionDto fromJson(const QJsonObject &json);
};

// 试跳记录上传数据
struct AttemptDto
{
    int athleteId;
    int height;
    int attemptNumber;
    QString result;
    QString timestamp;
    
    QJsonObject toJson() const;
    static AttemptDto fromModel(const AttemptRecord &record);
};
```

## 模型关系图

```
Competition (1) ←→ (N) Athlete
    ↓                   ↓
    1                   1
    ↓                   ↓
    N                   N
HeightSetting    AttemptRecord
                       ↓
                  SyncQueueEntry
```

## 数据验证规则

### 业务规则验证

```cpp
class DataValidator
{
public:
    static bool validateAthleteNumber(int number, int competitionId);
    static bool validateHeight(int height);
    static bool validateAttemptSequence(int athleteId, int height, int attemptNumber);
    static bool canAthleteAttempt(int athleteId, int height);
    
private:
    static bool isHeightInSequence(int height, int competitionId);
    static int getAthleteFailureCount(int athleteId);
};
```