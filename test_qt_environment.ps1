# Qt Environment Test Script
# This script checks if Qt environment is properly set up for tests

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Qt Environment Test" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

$releaseDir = "build\bin\Release"

if (-not (Test-Path $releaseDir)) {
    Write-Host "✗ ERROR: Release directory not found: $releaseDir" -ForegroundColor Red
    exit 1
}

# Change to Release directory
$originalLocation = Get-Location
Set-Location $releaseDir

Write-Host "Checking Qt environment in: $(Get-Location)" -ForegroundColor Cyan
Write-Host ""

# Check Qt DLLs
$qtDlls = @(
    "Qt6Core.dll",
    "Qt6Sql.dll", 
    "Qt6Test.dll",
    "Qt6Widgets.dll",
    "Qt6Network.dll"
)

Write-Host "Qt DLL Files:" -ForegroundColor Yellow
foreach ($dll in $qtDlls) {
    if (Test-Path $dll) {
        Write-Host "  ✓ $dll" -ForegroundColor Green
    } else {
        Write-Host "  ✗ $dll (missing)" -ForegroundColor Red
    }
}
Write-Host ""

# Check Qt SQLite plugin
Write-Host "Qt SQLite Plugin:" -ForegroundColor Yellow
$sqliteFiles = @(
    "qsqlite.dll",
    "sqldrivers\qsqlite.dll"
)

foreach ($file in $sqliteFiles) {
    if (Test-Path $file) {
        Write-Host "  ✓ $file" -ForegroundColor Green
    } else {
        Write-Host "  ✗ $file (missing)" -ForegroundColor Red
    }
}
Write-Host ""

# Check test executables
Write-Host "Test Executables:" -ForegroundColor Yellow
$testExes = @(
    "test_database_manager.exe",
    "test_config_manager.exe",
    "test_api_client.exe",
    "test_e2e_workflow.exe"
)

foreach ($exe in $testExes) {
    if (Test-Path $exe) {
        Write-Host "  ✓ $exe" -ForegroundColor Green
    } else {
        Write-Host "  ✗ $exe (missing)" -ForegroundColor Red
    }
}
Write-Host ""

# Set Qt environment variables
$env:QT_PLUGIN_PATH = $PWD.Path
$env:QT_DEBUG_PLUGINS = "1"  # Enable debug output

Write-Host "Environment Variables:" -ForegroundColor Yellow
Write-Host "  QT_PLUGIN_PATH = $env:QT_PLUGIN_PATH" -ForegroundColor Cyan
Write-Host "  QT_DEBUG_PLUGINS = $env:QT_DEBUG_PLUGINS" -ForegroundColor Cyan
Write-Host ""

# Test a simple Qt application (if available)
if (Test-Path "test_database_manager.exe") {
    Write-Host "Testing Qt SQLite plugin loading..." -ForegroundColor Yellow
    Write-Host "Running test_database_manager.exe with debug output:" -ForegroundColor Cyan
    Write-Host "----------------------------------------" -ForegroundColor Gray
    
    try {
        $process = Start-Process -FilePath ".\test_database_manager.exe" -Wait -PassThru -NoNewWindow
        Write-Host "----------------------------------------" -ForegroundColor Gray
        if ($process.ExitCode -eq 0) {
            Write-Host "✓ Test completed successfully" -ForegroundColor Green
        } else {
            Write-Host "✗ Test failed with exit code: $($process.ExitCode)" -ForegroundColor Red
        }
    } catch {
        Write-Host "✗ Test failed with exception: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "⚠ No test executable found to verify Qt setup" -ForegroundColor Yellow
}

# Restore original location
Set-Location $originalLocation

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Qt Environment Test Complete" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

Read-Host "Press Enter to exit"
