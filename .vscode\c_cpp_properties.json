{"configurations": [{"name": "Win32", "includePath": ["${workspaceFolder}/**", "${workspaceFolder}/src", "${workspaceFolder}/src/persistence", "${workspaceFolder}/src/utils", "${workspaceFolder}/src/ui", "${workspaceFolder}/src/api", "${workspaceFolder}/tests", "${workspaceFolder}/tests/unit", "${workspaceFolder}/tests/integration", "C:/Qt/install-x64/include", "C:/Qt/install-x64/include/QtCore", "C:/Qt/install-x64/include/QtWidgets", "C:/Qt/install-x64/include/QtSql", "C:/Qt/install-x64/include/QtNetwork", "C:/Qt/install-x64/include/QtTest", "C:/Qt/install-x64/include/QtGui", "C:/Qt/install-x64/include/QtPrintSupport", "C:/PROJECT/HighJumpTerminal/vcpkg/installed/x64-windows/include"], "defines": ["_DEBUG", "UNICODE", "_UNICODE", "WIN32", "_WINDOWS", "QT_CORE_LIB", "QT_WIDGETS_LIB", "QT_GUI_LIB", "QT_SQL_LIB", "QT_NETWORK_LIB", "QT_TEST_LIB", "QT_PRINTSUPPORT_LIB"], "windowsSdkVersion": "10.0.22621.0", "compilerPath": "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/cl.exe", "cStandard": "c17", "cppStandard": "c++17", "intelliSenseMode": "windows-msvc-x64", "configurationProvider": "ms-vscode.cmake-tools"}], "version": 4}