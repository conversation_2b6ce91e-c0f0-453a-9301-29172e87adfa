#ifndef CONFIG_MANAGER_H
#define CONFIG_MANAGER_H

#include <QObject>
#include <QSettings>
#include <QString>
#include <QUrl>
#include <QMutex>

/**
 * @brief Comprehensive configuration management system for High Jump Competition Management
 * 
 * This class provides a centralized configuration management system for the High Jump
 * Competition Management System, implementing the Singleton pattern to ensure consistent
 * configuration access throughout the application lifecycle.
 * 
 * The ConfigManager provides:
 * - Centralized configuration storage and retrieval
 * - API configuration management (base URL, timeout, retries, version)
 * - Database configuration settings
 * - Application settings (language, auto-sync, sync interval)
 * - Configuration persistence and validation
 * - Thread-safe configuration access
 * - Default value management and configuration reset
 * - Configuration change notifications
 * 
 * The system supports both programmatic configuration changes and user-initiated
 * settings modifications through the application's configuration interface.
 * 
 * @note This class implements the Singleton pattern to ensure consistent
 *       configuration access across the entire application.
 */
class ConfigManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief Gets the singleton instance of ConfigManager
     * @return Pointer to the ConfigManager instance
     * 
     * Returns the singleton instance of the configuration manager.
     * Creates a new instance if one doesn't exist.
     * 
     * @note This method is thread-safe and ensures only one instance exists.
     */
    static ConfigManager* instance();
    
    // Configuration lifecycle
    /**
     * @brief Initializes the configuration manager
     * @return true if initialization successful, false otherwise
     * 
     * Initializes the configuration system by loading settings from file,
     * creating default configuration if needed, and validating settings.
     * This method must be called before using any configuration methods.
     */
    bool initialize();
    
    /**
     * @brief Checks if the configuration manager is initialized
     * @return true if initialized, false otherwise
     * 
     * Returns whether the configuration manager has been successfully
     * initialized and is ready for use.
     */
    bool isInitialized() const;
    
    /**
     * @brief Gets the configuration file path
     * @return Path to the configuration file
     * 
     * Returns the full path to the configuration file where settings
     * are stored and loaded from.
     */
    QString getConfigPath() const;
    
    // API Configuration
    /**
     * @brief Gets the API base URL
     * @return API base URL string
     * 
     * Returns the base URL for API communication.
     * This is the root URL for all API endpoints.
     */
    QString getApiBaseUrl() const;
    
    /**
     * @brief Sets the API base URL
     * @param url New API base URL
     * 
     * Sets the base URL for API communication.
     * Emits configurationChanged() signal when the value changes.
     */
    void setApiBaseUrl(const QString &url);
    
    /**
     * @brief Gets the API timeout value
     * @return API timeout in milliseconds
     * 
     * Returns the timeout value for API requests in milliseconds.
     * This determines how long to wait for API responses.
     */
    int getApiTimeout() const;
    
    /**
     * @brief Sets the API timeout value
     * @param timeoutMs New timeout value in milliseconds
     * 
     * Sets the timeout value for API requests.
     * Emits configurationChanged() signal when the value changes.
     */
    void setApiTimeout(int timeoutMs);
    
    /**
     * @brief Gets the maximum number of API retries
     * @return Maximum number of retries
     * 
     * Returns the maximum number of retry attempts for failed API requests.
     * This helps handle temporary network issues.
     */
    int getApiMaxRetries() const;
    
    /**
     * @brief Sets the maximum number of API retries
     * @param retries New maximum retry count
     * 
     * Sets the maximum number of retry attempts for failed API requests.
     * Emits configurationChanged() signal when the value changes.
     */
    void setApiMaxRetries(int retries);
    
    /**
     * @brief Gets the API version
     * @return API version string
     * 
     * Returns the API version to use for communication.
     * This ensures compatibility with the correct API version.
     */
    QString getApiVersion() const;
    
    /**
     * @brief Sets the API version
     * @param version New API version
     * 
     * Sets the API version for communication.
     * Emits configurationChanged() signal when the value changes.
     */
    void setApiVersion(const QString &version);
    
    // Database Configuration
    /**
     * @brief Gets the database file path
     * @return Database file path
     * 
     * Returns the path to the SQLite database file.
     * This is where all application data is stored.
     */
    QString getDatabasePath() const;
    
    /**
     * @brief Sets the database file path
     * @param path New database file path
     * 
     * Sets the path to the SQLite database file.
     * Emits configurationChanged() signal when the value changes.
     */
    void setDatabasePath(const QString &path);
    
    // Application Configuration
    /**
     * @brief Gets the application language
     * @return Language code (e.g., "en", "zh-CN")
     * 
     * Returns the current application language setting.
     * This affects UI text and localization.
     */
    QString getApplicationLanguage() const;
    
    /**
     * @brief Sets the application language
     * @param language New language code
     * 
     * Sets the application language for UI localization.
     * Emits configurationChanged() signal when the value changes.
     */
    void setApplicationLanguage(const QString &language);
    
    /**
     * @brief Checks if auto-sync is enabled
     * @return true if auto-sync is enabled, false otherwise
     * 
     * Returns whether automatic data synchronization is enabled.
     * When enabled, data is automatically synced with the server.
     */
    bool getAutoSyncEnabled() const;
    
    /**
     * @brief Enables or disables auto-sync
     * @param enabled Whether auto-sync should be enabled
     * 
     * Controls whether automatic data synchronization is active.
     * Emits configurationChanged() signal when the value changes.
     */
    void setAutoSyncEnabled(bool enabled);
    
    /**
     * @brief Gets the sync interval
     * @return Sync interval in seconds
     * 
     * Returns the interval between automatic synchronization attempts.
     * This determines how frequently data is synced with the server.
     */
    int getSyncInterval() const;
    
    /**
     * @brief Sets the sync interval
     * @param intervalSeconds New sync interval in seconds
     * 
     * Sets the interval between automatic synchronization attempts.
     * Emits configurationChanged() signal when the value changes.
     */
    void setSyncInterval(int intervalSeconds);
    
    // Configuration management
    /**
     * @brief Saves configuration to file
     * 
     * Saves all current configuration settings to the configuration file.
     * This ensures settings persist between application sessions.
     */
    void save();
    
    /**
     * @brief Reloads configuration from file
     * 
     * Reloads all configuration settings from the configuration file.
     * This discards any unsaved changes and restores file settings.
     */
    void reload();
    
    /**
     * @brief Resets configuration to default values
     * 
     * Resets all configuration settings to their default values.
     * This is useful for troubleshooting or restoring factory settings.
     */
    void resetToDefaults();

signals:
    /**
     * @brief Emitted when a configuration value changes
     * @param key Configuration key that changed
     * @param value New configuration value
     * 
     * Signal emitted when any configuration setting is modified.
     * Used for UI updates and configuration change notifications.
     */
    void configurationChanged(const QString &key, const QVariant &value);
    
    /**
     * @brief Emitted when API configuration changes
     * 
     * Signal emitted when any API-related configuration setting is modified.
     * Used for API client reconfiguration and connection updates.
     */
    void apiConfigurationChanged();

private slots:
    /**
     * @brief Handles settings changes
     * 
     * Slot for handling external settings changes.
     * Ensures configuration consistency when settings are modified externally.
     */
    void onSettingsChanged();

private:
    /**
     * @brief Private constructor for singleton pattern
     * @param parent Parent QObject (default: nullptr)
     * 
     * Private constructor to prevent direct instantiation.
     * Use instance() method to get the singleton instance.
     */
    explicit ConfigManager(QObject *parent = nullptr);
    
    /**
     * @brief Destructor
     * 
     * Ensures proper cleanup of configuration resources and saves
     * any pending configuration changes.
     */
    ~ConfigManager();
    
    // Initialization helpers
    /**
     * @brief Creates the default configuration file
     * @return true if file created successfully, false otherwise
     * 
     * Creates a new configuration file with default values.
     * This is called when no configuration file exists.
     */
    bool createDefaultConfigFile();
    
    /**
     * @brief Loads default configuration values
     * 
     * Loads all default configuration values into memory.
     * These values are used when no configuration file exists.
     */
    void loadDefaultValues();
    
    /**
     * @brief Validates configuration settings
     * 
     * Validates all configuration settings to ensure they are
     * within acceptable ranges and formats.
     */
    void validateConfiguration();
    
    // Configuration value helpers
    /**
     * @brief Gets a configuration value
     * @param key Configuration key
     * @param defaultValue Default value if key doesn't exist
     * @return Configuration value
     * 
     * Retrieves a configuration value by key with optional default value.
     * Thread-safe method for accessing configuration data.
     */
    QVariant getValue(const QString &key, const QVariant &defaultValue = QVariant()) const;
    
    /**
     * @brief Sets a configuration value
     * @param key Configuration key
     * @param value New configuration value
     * 
     * Sets a configuration value by key.
     * Thread-safe method for modifying configuration data.
     */
    void setValue(const QString &key, const QVariant &value);
    
    /**
     * @brief Sets a configuration value without acquiring mutex
     * @param key Configuration key
     * @param value New configuration value
     * 
     * Internal method for setting configuration values when mutex is already held.
     * Used to avoid deadlocks during initialization.
     */
    void setValueInternal(const QString &key, const QVariant &value);
    
    // Member variables
    QScopedPointer<QSettings> m_settings;  ///< Qt settings object for configuration storage
    QString m_configPath;                  ///< Path to configuration file
    QMutex m_mutex;                        ///< Thread safety mutex
    bool m_isInitialized;                  ///< Initialization status
    
    static ConfigManager* s_instance;      ///< Singleton instance
    
    // Configuration keys
    static const QString KEY_API_BASE_URL;         ///< API base URL configuration key
    static const QString KEY_API_TIMEOUT;          ///< API timeout configuration key
    static const QString KEY_API_MAX_RETRIES;      ///< API max retries configuration key
    static const QString KEY_API_VERSION;          ///< API version configuration key
    static const QString KEY_DATABASE_PATH;        ///< Database path configuration key
    static const QString KEY_APPLICATION_LANGUAGE; ///< Application language configuration key
    static const QString KEY_AUTO_SYNC_ENABLED;    ///< Auto-sync enabled configuration key
    static const QString KEY_SYNC_INTERVAL;        ///< Sync interval configuration key
    
    // Default values
    static const QString DEFAULT_API_BASE_URL;         ///< Default API base URL
    static const int DEFAULT_API_TIMEOUT;              ///< Default API timeout (ms)
    static const int DEFAULT_API_MAX_RETRIES;          ///< Default API max retries
    static const QString DEFAULT_API_VERSION;          ///< Default API version
    static const QString DEFAULT_APPLICATION_LANGUAGE; ///< Default application language
    static const bool DEFAULT_AUTO_SYNC_ENABLED;       ///< Default auto-sync enabled state
    static const int DEFAULT_SYNC_INTERVAL;            ///< Default sync interval (seconds)
};

#endif // CONFIG_MANAGER_H