﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>ARM64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{50F1EA66-9AF2-3632-A868-A4181D64B5EB}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ALL_BUILD</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>C:\PROJECT\HighJump\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>C:\PROJECT\HighJump\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <Midl>
      <AdditionalIncludeDirectories>C:\PROJECT\HighJump\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <Midl>
      <AdditionalIncludeDirectories>C:\PROJECT\HighJump\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\PROJECT\HighJump\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/PROJECT/HighJump/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/PROJECT/HighJump -BC:/PROJECT/HighJump/build --check-stamp-file C:/PROJECT/HighJump/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\PROJECT\HighJump\build\CMakeFiles\3.31.6-msvc6\CMakeCXXCompiler.cmake;C:\PROJECT\HighJump\build\CMakeFiles\3.31.6-msvc6\CMakeRCCompiler.cmake;C:\PROJECT\HighJump\build\CMakeFiles\3.31.6-msvc6\CMakeSystem.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckLibraryExists.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindThreads.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\GNUInstallDirs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;C:\Qt\install-x64\lib\cmake\Qt6\FindWrapAtomic.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6Config.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6Dependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6Targets.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6VersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtFeature.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtFeatureCommon.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtInstallPaths.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicAndroidHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicExternalProjectHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicGitHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicPluginHelpers_v2.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomAttributionHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomCpeHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomDepHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomFileHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomGenerationHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomLicenseHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomOpsHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomPurlHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomPythonHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomQtEntityHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomSystemDepHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicWindowsHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreConfigExtras.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreMacros.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6CoreTools\Qt6CoreToolsAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6CoreTools\Qt6CoreToolsDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6CoreTools\Qt6CoreToolsVersionlessTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6GuiAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6GuiConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6GuiConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6GuiConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6GuiDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6GuiPlugins.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6GuiTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6GuiTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6GuiVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QGifPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QGifPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QICNSPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QICNSPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QICNSPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QICNSPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QICOPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QICOPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QJpegPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QJpegPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QSvgPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QSvgPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTgaPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTgaPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTgaPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTgaPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTiffPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTiffPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTiffPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTiffPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QVirtualKeyboardPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QVirtualKeyboardPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QVirtualKeyboardPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QVirtualKeyboardPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWbmpPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWbmpPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWbmpPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWbmpPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWebpPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWebpPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWebpPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWebpPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiTools\Qt6GuiToolsAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiTools\Qt6GuiToolsDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiTools\Qt6GuiToolsVersionlessTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6NetworkAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6NetworkConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6NetworkConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6NetworkConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6NetworkDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6NetworkPlugins.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6NetworkTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6NetworkTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6NetworkVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QNLMNIPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QNLMNIPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupport\Qt6PrintSupportAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupport\Qt6PrintSupportConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupport\Qt6PrintSupportConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupport\Qt6PrintSupportConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupport\Qt6PrintSupportDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupport\Qt6PrintSupportPlugins.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupport\Qt6PrintSupportTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupport\Qt6PrintSupportTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupport\Qt6PrintSupportVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupportPrivate\Qt6PrintSupportPrivateAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupportPrivate\Qt6PrintSupportPrivateConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupportPrivate\Qt6PrintSupportPrivateConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupportPrivate\Qt6PrintSupportPrivateConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupportPrivate\Qt6PrintSupportPrivateDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupportPrivate\Qt6PrintSupportPrivateTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupportPrivate\Qt6PrintSupportPrivateVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6QODBCDriverPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6QODBCDriverPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6QODBCDriverPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6QODBCDriverPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6QSQLiteDriverPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6QSQLiteDriverPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6QSQLiteDriverPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6QSQLiteDriverPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6SqlAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6SqlConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6SqlConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6SqlConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6SqlDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6SqlPlugins.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6SqlTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6SqlTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6SqlVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6SqlPrivate\Qt6SqlPrivateAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6SqlPrivate\Qt6SqlPrivateConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6SqlPrivate\Qt6SqlPrivateConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6SqlPrivate\Qt6SqlPrivateConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6SqlPrivate\Qt6SqlPrivateDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6SqlPrivate\Qt6SqlPrivateTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6SqlPrivate\Qt6SqlPrivateVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Test\Qt6TestAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Test\Qt6TestConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Test\Qt6TestConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6Test\Qt6TestConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6Test\Qt6TestDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6Test\Qt6TestTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Test\Qt6TestTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Test\Qt6TestVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Test\QtTestProperties.cmake;C:\Qt\install-x64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsMacros.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsPlugins.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsVersionlessTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateVersionlessAliasTargets.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\PROJECT\HighJump\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/PROJECT/HighJump/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/PROJECT/HighJump -BC:/PROJECT/HighJump/build --check-stamp-file C:/PROJECT/HighJump/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\PROJECT\HighJump\build\CMakeFiles\3.31.6-msvc6\CMakeCXXCompiler.cmake;C:\PROJECT\HighJump\build\CMakeFiles\3.31.6-msvc6\CMakeRCCompiler.cmake;C:\PROJECT\HighJump\build\CMakeFiles\3.31.6-msvc6\CMakeSystem.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckLibraryExists.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindThreads.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\GNUInstallDirs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;C:\Qt\install-x64\lib\cmake\Qt6\FindWrapAtomic.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6Config.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6Dependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6Targets.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6VersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtFeature.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtFeatureCommon.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtInstallPaths.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicAndroidHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicExternalProjectHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicGitHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicPluginHelpers_v2.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomAttributionHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomCpeHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomDepHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomFileHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomGenerationHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomLicenseHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomOpsHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomPurlHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomPythonHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomQtEntityHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomSystemDepHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicWindowsHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreConfigExtras.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreMacros.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6CoreTools\Qt6CoreToolsAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6CoreTools\Qt6CoreToolsDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6CoreTools\Qt6CoreToolsVersionlessTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6GuiAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6GuiConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6GuiConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6GuiConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6GuiDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6GuiPlugins.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6GuiTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6GuiTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6GuiVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QGifPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QGifPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QICNSPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QICNSPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QICNSPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QICNSPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QICOPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QICOPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QJpegPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QJpegPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QSvgPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QSvgPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTgaPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTgaPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTgaPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTgaPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTiffPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTiffPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTiffPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTiffPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QVirtualKeyboardPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QVirtualKeyboardPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QVirtualKeyboardPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QVirtualKeyboardPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWbmpPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWbmpPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWbmpPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWbmpPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWebpPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWebpPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWebpPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWebpPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiTools\Qt6GuiToolsAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiTools\Qt6GuiToolsDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiTools\Qt6GuiToolsVersionlessTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6NetworkAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6NetworkConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6NetworkConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6NetworkConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6NetworkDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6NetworkPlugins.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6NetworkTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6NetworkTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6NetworkVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QNLMNIPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QNLMNIPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupport\Qt6PrintSupportAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupport\Qt6PrintSupportConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupport\Qt6PrintSupportConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupport\Qt6PrintSupportConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupport\Qt6PrintSupportDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupport\Qt6PrintSupportPlugins.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupport\Qt6PrintSupportTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupport\Qt6PrintSupportTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupport\Qt6PrintSupportVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupportPrivate\Qt6PrintSupportPrivateAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupportPrivate\Qt6PrintSupportPrivateConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupportPrivate\Qt6PrintSupportPrivateConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupportPrivate\Qt6PrintSupportPrivateConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupportPrivate\Qt6PrintSupportPrivateDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupportPrivate\Qt6PrintSupportPrivateTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupportPrivate\Qt6PrintSupportPrivateVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6QODBCDriverPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6QODBCDriverPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6QODBCDriverPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6QODBCDriverPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6QSQLiteDriverPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6QSQLiteDriverPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6QSQLiteDriverPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6QSQLiteDriverPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6SqlAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6SqlConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6SqlConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6SqlConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6SqlDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6SqlPlugins.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6SqlTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6SqlTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6SqlVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6SqlPrivate\Qt6SqlPrivateAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6SqlPrivate\Qt6SqlPrivateConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6SqlPrivate\Qt6SqlPrivateConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6SqlPrivate\Qt6SqlPrivateConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6SqlPrivate\Qt6SqlPrivateDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6SqlPrivate\Qt6SqlPrivateTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6SqlPrivate\Qt6SqlPrivateVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Test\Qt6TestAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Test\Qt6TestConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Test\Qt6TestConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6Test\Qt6TestConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6Test\Qt6TestDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6Test\Qt6TestTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Test\Qt6TestTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Test\Qt6TestVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Test\QtTestProperties.cmake;C:\Qt\install-x64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsMacros.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsPlugins.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsVersionlessTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateVersionlessAliasTargets.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\PROJECT\HighJump\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule C:/PROJECT/HighJump/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/PROJECT/HighJump -BC:/PROJECT/HighJump/build --check-stamp-file C:/PROJECT/HighJump/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\PROJECT\HighJump\build\CMakeFiles\3.31.6-msvc6\CMakeCXXCompiler.cmake;C:\PROJECT\HighJump\build\CMakeFiles\3.31.6-msvc6\CMakeRCCompiler.cmake;C:\PROJECT\HighJump\build\CMakeFiles\3.31.6-msvc6\CMakeSystem.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckLibraryExists.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindThreads.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\GNUInstallDirs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;C:\Qt\install-x64\lib\cmake\Qt6\FindWrapAtomic.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6Config.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6Dependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6Targets.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6VersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtFeature.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtFeatureCommon.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtInstallPaths.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicAndroidHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicExternalProjectHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicGitHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicPluginHelpers_v2.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomAttributionHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomCpeHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomDepHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomFileHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomGenerationHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomLicenseHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomOpsHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomPurlHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomPythonHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomQtEntityHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomSystemDepHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicWindowsHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreConfigExtras.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreMacros.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6CoreTools\Qt6CoreToolsAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6CoreTools\Qt6CoreToolsDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6CoreTools\Qt6CoreToolsVersionlessTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6GuiAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6GuiConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6GuiConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6GuiConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6GuiDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6GuiPlugins.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6GuiTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6GuiTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6GuiVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QGifPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QGifPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QICNSPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QICNSPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QICNSPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QICNSPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QICOPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QICOPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QJpegPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QJpegPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QSvgPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QSvgPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTgaPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTgaPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTgaPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTgaPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTiffPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTiffPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTiffPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTiffPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QVirtualKeyboardPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QVirtualKeyboardPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QVirtualKeyboardPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QVirtualKeyboardPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWbmpPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWbmpPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWbmpPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWbmpPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWebpPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWebpPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWebpPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWebpPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiTools\Qt6GuiToolsAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiTools\Qt6GuiToolsDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiTools\Qt6GuiToolsVersionlessTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6NetworkAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6NetworkConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6NetworkConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6NetworkConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6NetworkDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6NetworkPlugins.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6NetworkTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6NetworkTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6NetworkVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QNLMNIPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QNLMNIPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupport\Qt6PrintSupportAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupport\Qt6PrintSupportConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupport\Qt6PrintSupportConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupport\Qt6PrintSupportConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupport\Qt6PrintSupportDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupport\Qt6PrintSupportPlugins.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupport\Qt6PrintSupportTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupport\Qt6PrintSupportTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupport\Qt6PrintSupportVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupportPrivate\Qt6PrintSupportPrivateAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupportPrivate\Qt6PrintSupportPrivateConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupportPrivate\Qt6PrintSupportPrivateConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupportPrivate\Qt6PrintSupportPrivateConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupportPrivate\Qt6PrintSupportPrivateDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupportPrivate\Qt6PrintSupportPrivateTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupportPrivate\Qt6PrintSupportPrivateVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6QODBCDriverPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6QODBCDriverPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6QODBCDriverPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6QODBCDriverPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6QSQLiteDriverPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6QSQLiteDriverPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6QSQLiteDriverPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6QSQLiteDriverPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6SqlAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6SqlConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6SqlConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6SqlConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6SqlDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6SqlPlugins.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6SqlTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6SqlTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6SqlVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6SqlPrivate\Qt6SqlPrivateAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6SqlPrivate\Qt6SqlPrivateConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6SqlPrivate\Qt6SqlPrivateConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6SqlPrivate\Qt6SqlPrivateConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6SqlPrivate\Qt6SqlPrivateDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6SqlPrivate\Qt6SqlPrivateTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6SqlPrivate\Qt6SqlPrivateVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Test\Qt6TestAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Test\Qt6TestConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Test\Qt6TestConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6Test\Qt6TestConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6Test\Qt6TestDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6Test\Qt6TestTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Test\Qt6TestTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Test\Qt6TestVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Test\QtTestProperties.cmake;C:\Qt\install-x64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsMacros.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsPlugins.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsVersionlessTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateVersionlessAliasTargets.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\PROJECT\HighJump\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule C:/PROJECT/HighJump/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/PROJECT/HighJump -BC:/PROJECT/HighJump/build --check-stamp-file C:/PROJECT/HighJump/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\PROJECT\HighJump\build\CMakeFiles\3.31.6-msvc6\CMakeCXXCompiler.cmake;C:\PROJECT\HighJump\build\CMakeFiles\3.31.6-msvc6\CMakeRCCompiler.cmake;C:\PROJECT\HighJump\build\CMakeFiles\3.31.6-msvc6\CMakeSystem.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\CheckLibraryExists.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\FindThreads.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\GNUInstallDirs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;C:\Qt\install-x64\lib\cmake\Qt6\FindWrapAtomic.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6Config.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6Dependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6Targets.cmake;C:\Qt\install-x64\lib\cmake\Qt6\Qt6VersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtFeature.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtFeatureCommon.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtInstallPaths.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicAndroidHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicExternalProjectHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicGitHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicPluginHelpers_v2.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomAttributionHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomCpeHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomDepHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomFileHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomGenerationHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomLicenseHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomOpsHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomPurlHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomPythonHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomQtEntityHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicSbomSystemDepHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6\QtPublicWindowsHelpers.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreConfigExtras.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreMacros.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Core\Qt6CoreVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6CorePrivate\Qt6CorePrivateVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6CoreTools\Qt6CoreToolsAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6CoreTools\Qt6CoreToolsDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6CoreTools\Qt6CoreToolsVersionlessTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6GuiAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6GuiConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6GuiConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6GuiConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6GuiDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6GuiPlugins.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6GuiTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6GuiTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6GuiVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QGifPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QGifPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QICNSPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QICNSPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QICNSPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QICNSPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QICOPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QICOPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QJpegPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QJpegPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QSvgPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QSvgPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTgaPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTgaPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTgaPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTgaPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTiffPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTiffPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTiffPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTiffPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QVirtualKeyboardPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QVirtualKeyboardPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QVirtualKeyboardPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QVirtualKeyboardPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWbmpPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWbmpPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWbmpPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWbmpPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWebpPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWebpPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWebpPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWebpPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiPrivate\Qt6GuiPrivateVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiTools\Qt6GuiToolsAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiTools\Qt6GuiToolsDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6GuiTools\Qt6GuiToolsVersionlessTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6NetworkAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6NetworkConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6NetworkConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6NetworkConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6NetworkDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6NetworkPlugins.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6NetworkTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6NetworkTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6NetworkVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QNLMNIPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QNLMNIPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6NetworkPrivate\Qt6NetworkPrivateVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupport\Qt6PrintSupportAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupport\Qt6PrintSupportConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupport\Qt6PrintSupportConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupport\Qt6PrintSupportConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupport\Qt6PrintSupportDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupport\Qt6PrintSupportPlugins.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupport\Qt6PrintSupportTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupport\Qt6PrintSupportTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupport\Qt6PrintSupportVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupportPrivate\Qt6PrintSupportPrivateAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupportPrivate\Qt6PrintSupportPrivateConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupportPrivate\Qt6PrintSupportPrivateConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupportPrivate\Qt6PrintSupportPrivateConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupportPrivate\Qt6PrintSupportPrivateDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupportPrivate\Qt6PrintSupportPrivateTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6PrintSupportPrivate\Qt6PrintSupportPrivateVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6QODBCDriverPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6QODBCDriverPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6QODBCDriverPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6QODBCDriverPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6QSQLiteDriverPluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6QSQLiteDriverPluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6QSQLiteDriverPluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6QSQLiteDriverPluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6SqlAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6SqlConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6SqlConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6SqlConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6SqlDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6SqlPlugins.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6SqlTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6SqlTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Sql\Qt6SqlVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6SqlPrivate\Qt6SqlPrivateAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6SqlPrivate\Qt6SqlPrivateConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6SqlPrivate\Qt6SqlPrivateConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6SqlPrivate\Qt6SqlPrivateConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6SqlPrivate\Qt6SqlPrivateDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6SqlPrivate\Qt6SqlPrivateTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6SqlPrivate\Qt6SqlPrivateVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Test\Qt6TestAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Test\Qt6TestConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Test\Qt6TestConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6Test\Qt6TestConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6Test\Qt6TestDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6Test\Qt6TestTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Test\Qt6TestTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Test\Qt6TestVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Test\QtTestProperties.cmake;C:\Qt\install-x64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6TestPrivate\Qt6TestPrivateVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6QModernWindowsStylePluginTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsMacros.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsPlugins.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6Widgets\Qt6WidgetsVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsPrivate\Qt6WidgetsPrivateVersionlessAliasTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsDependencies.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-release.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsVersionlessTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateAdditionalTargetInfo.cmake;C:\Qt\install-x64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfig.cmake;C:\Qt\install-x64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersion.cmake;C:\Qt\install-x64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersionImpl.cmake;C:\Qt\install-x64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateTargets.cmake;C:\Qt\install-x64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateVersionlessAliasTargets.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\PROJECT\HighJump\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="C:\PROJECT\HighJump\build\ZERO_CHECK.vcxproj">
      <Project>{F9C87542-EA78-3B7C-B54D-2CB91B9FD646}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="C:\PROJECT\HighJump\build\high-jump-scorer.vcxproj">
      <Project>{EF9487CE-FD72-3F94-8709-3E16A24853C4}</Project>
      <Name>high-jump-scorer</Name>
    </ProjectReference>
    <ProjectReference Include="C:\PROJECT\HighJump\build\tests\test_api_client.vcxproj">
      <Project>{C0E42CA3-FAEC-3551-9C5D-A06676D437E3}</Project>
      <Name>test_api_client</Name>
    </ProjectReference>
    <ProjectReference Include="C:\PROJECT\HighJump\build\tests\test_config_manager.vcxproj">
      <Project>{8A910539-0962-31B0-A46C-E13D63618B60}</Project>
      <Name>test_config_manager</Name>
    </ProjectReference>
    <ProjectReference Include="C:\PROJECT\HighJump\build\tests\test_database_manager.vcxproj">
      <Project>{85326FA8-D048-34D4-835F-F7518A4D63BB}</Project>
      <Name>test_database_manager</Name>
    </ProjectReference>
    <ProjectReference Include="C:\PROJECT\HighJump\build\tests\test_e2e_workflow.vcxproj">
      <Project>{8AF23479-573B-3E2D-BE2D-EF9BCFD68F7A}</Project>
      <Name>test_e2e_workflow</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>