# 离线模式与数据同步架构

## 核心设计理念

应用采用"**离线优先 (Offline-First)**"工作流。用户的每一次记分操作，都**首先**写入本地的SQLite数据库并立即更新UI，此操作不依赖于网络连接。

## 数据同步流程

### 1. 操作队列机制

任何数据变更操作，在写入本地数据库的同时，会被记录到一个"待同步"队列中。

```cpp
class SyncQueueManager : public QObject
{
    Q_OBJECT

public:
    explicit SyncQueueManager(QObject *parent = nullptr);
    
    // 队列操作
    void addOperation(const SyncOperation &operation);
    QList<SyncOperation> getPendingOperations();
    void markAsCompleted(int operationId);
    void markAsFailed(int operationId, const QString &error);
    
    // 队列统计
    int pendingCount() const;
    int failedCount() const;
    QDateTime lastSyncTime() const;

signals:
    void operationAdded(const SyncOperation &operation);
    void operationCompleted(int operationId);
    void operationFailed(int operationId, const QString &error);

private:
    DatabaseManager *m_dbManager;
    QQueue<SyncOperation> m_pendingQueue;
};
```

### 2. 网络状态检测

`ApiClient` 负责检测与服务器的网络连接状态。

```cpp
class NetworkStatusDetector : public QObject
{
    Q_OBJECT

public:
    explicit NetworkStatusDetector(QObject *parent = nullptr);
    
    bool isOnline() const;
    void startMonitoring();
    void stopMonitoring();
    
    // 连接测试
    void testConnection();
    void setTestEndpoint(const QUrl &endpoint);

signals:
    void statusChanged(bool isOnline);
    void connectionRestored();
    void connectionLost();

private slots:
    void checkNetworkStatus();
    void onTestRequestFinished();

private:
    void updateStatus(bool isOnline);
    
    QNetworkAccessManager *m_manager;
    QTimer *m_checkTimer;
    QUrl m_testEndpoint;
    bool m_isOnline;
    int m_checkInterval;
};
```

### 3. 自动同步机制

一旦网络连接恢复，`ApiClient` 将自动处理"待同步"队列，按顺序将本地操作发送给服务器。

```cpp
class AutoSyncManager : public QObject
{
    Q_OBJECT

public:
    explicit AutoSyncManager(QObject *parent = nullptr);
    
    // 同步控制
    void startAutoSync();
    void stopAutoSync();
    void forceSyncNow();
    
    // 同步配置
    void setSyncInterval(int seconds);
    void setMaxRetries(int retries);
    void setBatchSize(int size);

signals:
    void syncStarted();
    void syncProgress(int completed, int total);
    void syncCompleted(bool success, int processedCount);
    void syncError(const QString &message);

private slots:
    void onNetworkStatusChanged(bool isOnline);
    void performScheduledSync();
    void processSyncBatch();

private:
    void processNextBatch();
    bool processSyncOperation(const SyncOperation &operation);
    
    NetworkStatusDetector *m_networkDetector;
    SyncQueueManager *m_queueManager;
    ApiClient *m_apiClient;
    
    QTimer *m_syncTimer;
    bool m_isSyncing;
    int m_syncInterval;
    int m_maxRetries;
    int m_batchSize;
};
```

### 4. 状态反馈

UI界面需有图标向用户展示当前的同步状态（已同步、同步中、离线/同步失败）。

```cpp
class SyncStatusIndicator : public QWidget
{
    Q_OBJECT

public:
    enum SyncStatus {
        Synchronized,    // 已同步 - 绿色
        Syncing,        // 同步中 - 黄色动画
        Offline,        // 离线 - 灰色
        Error           // 错误 - 红色
    };

    explicit SyncStatusIndicator(QWidget *parent = nullptr);
    
    void setStatus(SyncStatus status);
    void setMessage(const QString &message);
    void setPendingCount(int count);

protected:
    void paintEvent(QPaintEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;

private slots:
    void updateAnimation();

private:
    void drawStatusIcon(QPainter &painter);
    void showStatusTooltip();
    
    SyncStatus m_status;
    QString m_message;
    int m_pendingCount;
    QTimer *m_animationTimer;
    int m_animationFrame;
};
```

## 冲突解决策略

### Client-Wins策略

为简化MVP复杂度，采用"**客户端优先 (Client-Wins)**"策略。网络恢复同步时，本地的数据将被视为最权威的版本，并覆盖服务器上的相应数据。

```cpp
class ConflictResolver
{
public:
    enum ConflictStrategy {
        ClientWins,     // 客户端优先
        ServerWins,     // 服务器优先
        ManualResolve   // 手动解决
    };

    static ConflictStrategy currentStrategy();
    static void setStrategy(ConflictStrategy strategy);
    
    // 冲突处理
    static SyncOperation resolveConflict(
        const SyncOperation &localOp,
        const QJsonObject &serverData
    );

private:
    static ConflictStrategy s_strategy;
};
```

### 时间戳管理

```cpp
class TimestampManager
{
public:
    // 获取标准化时间戳
    static QDateTime getUtcTimestamp();
    static QString formatTimestamp(const QDateTime &dateTime);
    static QDateTime parseTimestamp(const QString &timestamp);
    
    // 时间同步
    static void syncWithServer();
    static qint64 getServerTimeOffset();

private:
    static qint64 s_serverTimeOffset;
    static QDateTime s_lastSyncTime;
};
```

## 数据完整性保证

### 事务管理

```cpp
class OfflineTransactionManager
{
public:
    explicit OfflineTransactionManager(DatabaseManager *dbManager);
    
    // 事务操作
    bool beginTransaction();
    bool commitTransaction();
    bool rollbackTransaction();
    
    // 批量操作
    bool executeBatch(const QList<SyncOperation> &operations);
    
    // 完整性检查
    bool validateDataIntegrity();
    bool repairCorruptedData();

private:
    DatabaseManager *m_dbManager;
    bool m_transactionActive;
    QStringList m_transactionLog;
};
```

### 数据验证

```cpp
class DataValidator
{
public:
    // 本地数据验证
    static bool validateAttemptRecord(const AttemptRecord &record);
    static bool validateAthleteData(const Athlete &athlete);
    static bool validateCompetitionData(const Competition &competition);
    
    // 同步数据验证
    static bool validateSyncOperation(const SyncOperation &operation);
    static bool validateServerResponse(const QJsonObject &response);
    
    // 完整性检查
    static QStringList checkDataConsistency();
    static bool fixDataInconsistencies();

private:
    static bool checkRequiredFields(const QJsonObject &data, const QStringList &fields);
    static bool checkDataTypes(const QJsonObject &data);
    static bool checkBusinessRules(const QJsonObject &data);
};
```

## 性能优化

### 批量同步

```cpp
class BatchSyncProcessor
{
public:
    explicit BatchSyncProcessor(int batchSize = 50);
    
    // 批量处理
    void addToBatch(const SyncOperation &operation);
    bool processBatch();
    void clearBatch();
    
    // 批量优化
    QList<SyncOperation> optimizeBatch(const QList<SyncOperation> &operations);
    void mergeSimilarOperations(QList<SyncOperation> &operations);

private:
    QList<SyncOperation> m_currentBatch;
    int m_batchSize;
    int m_maxBatchProcessingTime;
};
```

### 增量同步

```cpp
class IncrementalSyncManager
{
public:
    explicit IncrementalSyncManager(QObject *parent = nullptr);
    
    // 增量同步
    void performIncrementalSync();
    void setLastSyncTimestamp(const QDateTime &timestamp);
    QDateTime getLastSyncTimestamp() const;
    
    // 变更检测
    QList<SyncOperation> getChangesSince(const QDateTime &since);
    bool hasUnsyncedChanges() const;

private:
    void trackDataChanges();
    QList<SyncOperation> detectChanges();
    
    QDateTime m_lastSyncTimestamp;
    QHash<QString, QDateTime> m_tableLastModified;
};
```

## 错误恢复机制

### 同步错误处理

```cpp
class SyncErrorRecovery
{
public:
    // 错误分类
    enum ErrorType {
        NetworkError,
        ServerError,
        DataError,
        ConflictError,
        AuthError
    };
    
    static bool canRecover(ErrorType errorType);
    static void handleSyncError(const SyncOperation &operation, ErrorType error);
    static void scheduleRetry(const SyncOperation &operation, int delaySeconds);
    
    // 数据恢复
    static bool recoverFromDataCorruption();
    static bool rebuildsyncQueue();

private:
    static void logSyncError(const SyncOperation &operation, const QString &error);
    static int calculateRetryDelay(int attemptCount);
};
```

### 备份和恢复

```cpp
class DataBackupManager
{
public:
    // 备份操作
    static bool createBackup(const QString &backupPath);
    static bool restoreFromBackup(const QString &backupPath);
    static QStringList getAvailableBackups();
    
    // 自动备份
    static void enableAutoBackup(bool enabled);
    static void setBackupInterval(int hours);
    static void setMaxBackupCount(int count);

private:
    static QString generateBackupFileName();
    static bool compressBackup(const QString &sourcePath, const QString &targetPath);
    static bool decompressBackup(const QString &sourcePath, const QString &targetPath);
};
```

## 监控和诊断

### 同步状态监控

```cpp
class SyncMonitor : public QObject
{
    Q_OBJECT

public:
    struct SyncMetrics {
        int totalOperations;
        int successfulOperations;
        int failedOperations;
        int pendingOperations;
        QDateTime lastSyncTime;
        qint64 totalSyncTime;
        double averageOperationTime;
    };

    static SyncMonitor* instance();
    
    SyncMetrics getCurrentMetrics() const;
    void recordOperationStart(int operationId);
    void recordOperationComplete(int operationId, bool success);
    
    // 诊断
    QStringList getDiagnosticInfo() const;
    bool runDiagnostics();

signals:
    void metricsUpdated(const SyncMetrics &metrics);

private:
    SyncMetrics m_metrics;
    QHash<int, QDateTime> m_operationStartTimes;
};
```

### 调试工具

```cpp
class SyncDebugger
{
public:
    // 调试模式
    static void enableDebugMode(bool enabled);
    static bool isDebugModeEnabled();
    
    // 日志记录
    static void logSyncOperation(const SyncOperation &operation);
    static void logNetworkRequest(const QNetworkRequest &request);
    static void logNetworkResponse(const QByteArray &response);
    
    // 模拟工具
    static void simulateNetworkError();
    static void simulateServerError();
    static void simulateSlowNetwork(int delayMs);

private:
    static bool s_debugEnabled;
    static QFile s_debugLogFile;
};
```