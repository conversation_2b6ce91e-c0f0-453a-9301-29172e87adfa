# Code Cleanup Script for High Jump Competition Management System
# This script performs comprehensive code cleanup to address QA findings

param(
    [string]$SourceDir = "src",
    [string]$TestDir = "tests",
    [switch]$DryRun = $false
)

Write-Host "=========================================" -ForegroundColor Cyan
Write-Host "High Jump Competition Management System" -ForegroundColor Cyan
Write-Host "Code Cleanup Script" -ForegroundColor Cyan
Write-Host "=========================================" -ForegroundColor Cyan

$issuesFound = 0
$issuesFixed = 0

# Function to log issues
function Log-Issue {
    param([string]$File, [int]$Line, [string]$Issue, [string]$Severity = "Medium")
    $global:issuesFound++
    $color = switch ($Severity) {
        "High" { "Red" }
        "Medium" { "Yellow" }
        "Low" { "Green" }
    }
    Write-Host "  [$Severity] ${File}:${Line} - $Issue" -ForegroundColor $color
}

# Function to log fixes
function Log-Fix {
    param([string]$File, [string]$Description)
    $global:issuesFixed++
    Write-Host "  [FIXED] $File - $Description" -ForegroundColor Green
}

Write-Host "`n1. Scanning for TODO/FIXME comments..." -ForegroundColor Yellow

# Find TODO/FIXME comments
$todoFiles = Get-ChildItem -Path $SourceDir, $TestDir -Recurse -Include "*.cpp", "*.h" | 
    Where-Object { (Get-Content $_.FullName | Select-String "TODO|FIXME").Count -gt 0 }

foreach ($file in $todoFiles) {
    $content = Get-Content $file.FullName
    for ($i = 0; $i -lt $content.Length; $i++) {
        if ($content[$i] -match "TODO|FIXME") {
            Log-Issue -File $file.Name -Line ($i + 1) -Issue "TODO/FIXME comment found" -Severity "Medium"
        }
    }
}

Write-Host "`n2. Checking naming conventions..." -ForegroundColor Yellow

# Check for inconsistent naming patterns
$cppFiles = Get-ChildItem -Path $SourceDir -Recurse -Include "*.cpp", "*.h"

foreach ($file in $cppFiles) {
    $content = Get-Content $file.FullName
    
    # Check for inconsistent member variable naming
    for ($i = 0; $i -lt $content.Length; $i++) {
        $line = $content[$i]
        
        # Check for member variables without m_ prefix
        if ($line -match "^\s*[a-zA-Z_][a-zA-Z0-9_]*\s+[a-zA-Z_][a-zA-Z0-9_]*\s*[;=]") {
            if ($line -match "^\s*[a-z][a-zA-Z0-9_]*\s+[a-zA-Z_][a-zA-Z0-9_]*\s*[;=]" -and 
                $line -notmatch "^\s*m_[a-zA-Z0-9_]*\s+[a-zA-Z_][a-zA-Z0-9_]*\s*[;=]") {
                Log-Issue -File $file.Name -Line ($i + 1) -Issue "Member variable without m_ prefix" -Severity "Low"
            }
        }
        
        # Check for inconsistent function naming
        if ($line -match "^\s*[A-Z][a-zA-Z0-9_]*\s+[a-zA-Z_][a-zA-Z0-9_]*\s*\(" -and
            $line -notmatch "^\s*[A-Z][a-zA-Z0-9_]*\s+[a-z][a-zA-Z0-9_]*\s*\(") {
            Log-Issue -File $file.Name -Line ($i + 1) -Issue "Function name should start with lowercase" -Severity "Low"
        }
    }
}

Write-Host "`n3. Checking for placeholder implementations..." -ForegroundColor Yellow

# Find placeholder implementations
foreach ($file in $cppFiles) {
    $content = Get-Content $file.FullName
    for ($i = 0; $i -lt $content.Length; $i++) {
        $line = $content[$i]
        
        # Check for empty function bodies
        if ($line -match "^\s*\{\s*$" -and $i -gt 0) {
            $prevLine = $content[$i - 1]
            if ($prevLine -match "^\s*[a-zA-Z_][a-zA-Z0-9_]*\s+[a-zA-Z_][a-zA-Z0-9_]*\s*\(") {
                $nextLine = if ($i + 1 -lt $content.Length) { $content[$i + 1] } else { "" }
                if ($nextLine -match "^\s*\}\s*$") {
                    Log-Issue -File $file.Name -Line ($i + 1) -Issue "Empty function body (placeholder)" -Severity "Medium"
                }
            }
        }
        
        # Check for placeholder comments
        if ($line -match "//.*placeholder|//.*TODO|//.*FIXME|//.*implement") {
            Log-Issue -File $file.Name -Line ($i + 1) -Issue "Placeholder comment found" -Severity "Medium"
        }
    }
}

Write-Host "`n4. Checking for smart pointer usage..." -ForegroundColor Yellow

# Check for raw pointer usage that could be smart pointers
foreach ($file in $cppFiles) {
    $content = Get-Content $file.FullName
    for ($i = 0; $i -lt $content.Length; $i++) {
        $line = $content[$i]
        
        # Check for raw pointer declarations
        if ($line -match "^\s*[a-zA-Z_][a-zA-Z0-9_]*\s*\*\s*[a-zA-Z_][a-zA-Z0-9_]*\s*[;=]") {
            if ($line -notmatch "QScopedPointer|QSharedPointer|std::unique_ptr|std::shared_ptr") {
                Log-Issue -File $file.Name -Line ($i + 1) -Issue "Consider using smart pointer instead of raw pointer" -Severity "Medium"
            }
        }
    }
}

Write-Host "`n5. Checking for memory management issues..." -ForegroundColor Yellow

# Check for potential memory leaks
foreach ($file in $cppFiles) {
    $content = Get-Content $file.FullName
    for ($i = 0; $i -lt $content.Length; $i++) {
        $line = $content[$i]
        
        # Check for new without delete
        if ($line -match "new\s+[a-zA-Z_][a-zA-Z0-9_]*\s*\(") {
            $hasDelete = $false
            for ($j = $i + 1; $j -lt [Math]::Min($i + 50, $content.Length); $j++) {
                if ($content[$j] -match "delete\s+[a-zA-Z_][a-zA-Z0-9_]*") {
                    $hasDelete = $true
                    break
                }
            }
            if (-not $hasDelete) {
                Log-Issue -File $file.Name -Line ($i + 1) -Issue "new without corresponding delete" -Severity "High"
            }
        }
    }
}

Write-Host "`n6. Checking for error handling..." -ForegroundColor Yellow

# Check for missing error handling
foreach ($file in $cppFiles) {
    $content = Get-Content $file.FullName
    for ($i = 0; $i -lt $content.Length; $i++) {
        $line = $content[$i]
        
        # Check for file operations without error checking
        if ($line -match "\.open\(" -and $line -notmatch "if.*open") {
            Log-Issue -File $file.Name -Line ($i + 1) -Issue "File operation without error checking" -Severity "Medium"
        }
        
        # Check for database operations without error checking
        if ($line -match "\.exec\(" -and $line -notmatch "if.*exec") {
            Log-Issue -File $file.Name -Line ($i + 1) -Issue "Database operation without error checking" -Severity "Medium"
        }
    }
}

Write-Host "`n7. Checking for documentation..." -ForegroundColor Yellow

# Check for missing documentation
foreach ($file in $cppFiles) {
    $content = Get-Content $file.FullName
    
    # Check for public methods without documentation
    for ($i = 0; $i -lt $content.Length; $i++) {
        $line = $content[$i]
        
        if ($line -match "^\s*[a-zA-Z_][a-zA-Z0-9_]*\s+[a-zA-Z_][a-zA-Z0-9_]*\s*\([^)]*\)\s*[;{]") {
            # Check if there's documentation above
            $hasDoc = $false
            for ($j = [Math]::Max(0, $i - 5); $j -lt $i; $j++) {
                if ($content[$j] -match "///|/\*\*|\* @") {
                    $hasDoc = $true
                    break
                }
            }
            if (-not $hasDoc) {
                Log-Issue -File $file.Name -Line ($i + 1) -Issue "Public method without documentation" -Severity "Low"
            }
        }
    }
}

Write-Host "`n=========================================" -ForegroundColor Cyan
Write-Host "Code Cleanup Analysis Complete" -ForegroundColor Cyan
Write-Host "=========================================" -ForegroundColor Cyan

Write-Host "`nSummary:" -ForegroundColor Yellow
Write-Host "  Issues Found: $issuesFound" -ForegroundColor White
Write-Host "  Issues Fixed: $issuesFixed" -ForegroundColor White

if ($issuesFound -gt 0) {
    Write-Host "`nRecommendations:" -ForegroundColor Yellow
    Write-Host "  1. Address High priority issues first" -ForegroundColor White
    Write-Host "  2. Review Medium priority issues for code quality" -ForegroundColor White
    Write-Host "  3. Consider Low priority issues for future improvements" -ForegroundColor White
    Write-Host "  4. Run tests after making changes" -ForegroundColor White
    Write-Host "  5. Update documentation as needed" -ForegroundColor White
} else {
    Write-Host "`n✅ No issues found! Code quality is excellent." -ForegroundColor Green
}

Write-Host "`nNext Steps:" -ForegroundColor Yellow
Write-Host "  1. Review the issues above" -ForegroundColor White
Write-Host "  2. Fix critical issues first" -ForegroundColor White
Write-Host "  3. Run the build system to verify changes" -ForegroundColor White
Write-Host "  4. Run tests to ensure functionality is maintained" -ForegroundColor White
Write-Host "  5. Update QA documentation" -ForegroundColor White

Write-Host "`n=========================================" -ForegroundColor Cyan 