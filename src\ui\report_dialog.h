#ifndef REPORT_DIALOG_H
#define REPORT_DIALOG_H

#include <QDialog>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLabel>
#include <QComboBox>
#include <QPushButton>
#include <QLineEdit>
#include <QTextEdit>
#include <QProgressBar>
#include <QGroupBox>
#include <QCheckBox>
#include <QSpinBox>
#include <QDateTimeEdit>
#include <QFileDialog>
#include <QMessageBox>
#include <QListWidget>
#include <QTabWidget>
#include <QTableWidget>
#include <QHeaderView>
#include <QApplication>
#include <QStandardPaths>
#include <QDir>
#include <QDateTime>
#include <QTimer>

#include "../utils/report_generator.h"

/**
 * @brief Dialog for generating and previewing reports
 * 
 * This dialog provides a comprehensive interface for generating various types
 * of reports in different formats (PDF, Excel, HTML) with preview capabilities.
 */
class ReportDialog : public QDialog
{
    Q_OBJECT

public:
    explicit ReportDialog(QWidget *parent = nullptr);
    ~ReportDialog();

    // Data setters for populating report data
    void setCompetitionData(const QVariantMap &competitionData);
    void setAthletesData(const QVariantList &athletesData);
    void setResultsData(const QVariantList &resultsData);
    void setStatistics(const QVariantMap &statistics);

signals:
    void reportGenerated(const QString &filePath);
    void reportGenerationFailed(const QString &error);

private slots:
    void onReportTypeChanged(int index);
    void onFormatChanged(int index);
    void onTemplateChanged(int index);
    void onBrowseOutputPath();
    void onGenerateReport();
    void onPreviewReport();
    void onCancel();
    
    // Report generator signals
    void onReportGenerationStarted(const QString &reportName);
    void onReportGenerationProgress(int percentage);
    void onReportGenerationCompleted(const QString &outputPath);
    void onReportGenerationFailed(const QString &error);

private:
    // UI setup methods
    void setupUI();
    void setupConnections();
    void setupReportTypeGroup();
    void setupFormatGroup();
    void setupTemplateGroup();
    void setupOutputGroup();
    void setupOptionsGroup();
    void setupPreviewGroup();
    void setupButtons();

    // Data handling methods
    void populateTemplates();
    void updateOutputPath();
    void updatePreview();
    ReportGenerator::ReportData prepareReportData();
    void populateAthletesTable();
    void populateStatisticsTable();

    // Validation methods
    bool validateInputs();
    QString getDefaultOutputPath();

    // UI Components
    QTabWidget *m_tabWidget;
    
    // Report Type Group
    QGroupBox *m_reportTypeGroup;
    QComboBox *m_reportTypeCombo;
    QLabel *m_reportTypeDescription;
    
    // Format Group
    QGroupBox *m_formatGroup;
    QComboBox *m_formatCombo;
    QLabel *m_formatDescription;
    
    // Template Group
    QGroupBox *m_templateGroup;
    QComboBox *m_templateCombo;
    QLabel *m_templateDescription;
    QPushButton *m_manageTemplatesButton;
    
    // Output Group
    QGroupBox *m_outputGroup;
    QLineEdit *m_outputPathEdit;
    QPushButton *m_browseButton;
    QCheckBox *m_openAfterGenerationCheck;
    
    // Options Group
    QGroupBox *m_optionsGroup;
    QCheckBox *m_includeStatisticsCheck;
    QCheckBox *m_includeAthleteListCheck;
    QCheckBox *m_includeAttemptsCheck;
    QCheckBox *m_includeTimingCheck;
    QCheckBox *m_includeChartsCheck;
    QCheckBox *m_includeTrendsCheck;
    
    // Preview Group
    QGroupBox *m_previewGroup;
    QTableWidget *m_athletesTable;
    QTableWidget *m_statisticsTable;
    QTextEdit *m_previewText;
    
    // Progress and Buttons
    QProgressBar *m_progressBar;
    QPushButton *m_generateButton;
    QPushButton *m_previewButton;
    QPushButton *m_cancelButton;
    
    // Data
    ReportGenerator *m_reportGenerator;
    QVariantMap m_competitionData;
    QVariantList m_athletesData;
    QVariantList m_resultsData;
    QVariantMap m_statistics;
    
    // State
    bool m_isGenerating;
    QTimer *m_progressTimer;
};

#endif // REPORT_DIALOG_H 