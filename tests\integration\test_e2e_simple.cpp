#include <QTest>
#include <QApplication>
#include <QSignalSpy>
#include <QTimer>
#include <QEventLoop>
#include <QDir>
#include <QFile>
#include <QStandardPaths>
#include <QTemporaryDir>
#include <QSqlQuery>
#include <QSqlError>

// Only include core components that we know work
#include "../../src/persistence/database_manager.h"
#include "../../src/utils/config_manager.h"
#include "../../src/api/api_client.h"

/**
 * @brief Simplified End-to-End Workflow Test
 * 
 * This test suite validates the core workflow of the High Jump Competition
 * Management System, focusing on:
 * - Database initialization and management
 * - Configuration management
 * - API client integration
 * - Component integration
 * 
 * The tests simulate real user scenarios and verify that core components
 * work together correctly.
 */
class TestE2ESimple : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    
    // Core workflow tests
    void testSystemInitialization();
    void testDatabaseWorkflow();
    void testConfigurationWorkflow();
    void testApiClientIntegration();
    void testComponentIntegration();

private:
    // Helper methods
    void setupTestCompetition();
    void addTestAthletes();
    void verifyDatabaseOperations();
    void verifyConfigurationPersistence();
    void verifyApiClientConfiguration();
    
    // Test data
    QVariantMap m_testCompetition;
    QList<QVariantMap> m_testAthletes;
    
    // Component instances
    DatabaseManager* m_dbManager;
    ConfigManager* m_configManager;
    ApiClient* m_apiClient;
};

void TestE2ESimple::initTestCase()
{
    QApplication::setApplicationName("HighJumpE2ETest");
    QApplication::setOrganizationName("HighJumpE2ETest");
    
    QString testDataDir = QStandardPaths::writableLocation(QStandardPaths::TempLocation) + "/highjump_e2e_test";
    QDir().mkpath(testDataDir);
    
    m_configManager = ConfigManager::instance();
    // Use public methods to configure the system
    m_configManager->setDatabasePath(testDataDir + "/test_e2e.db");
    m_configManager->setApiBaseUrl("http://localhost:8080");
    m_configManager->setAutoSyncEnabled(false);
    
    m_dbManager = DatabaseManager::instance();
    m_apiClient = ApiClient::instance();
    
    setupTestCompetition();
    addTestAthletes();
}

void TestE2ESimple::cleanupTestCase()
{
    QString testDataDir = QStandardPaths::writableLocation(QStandardPaths::TempLocation) + "/highjump_e2e_test";
    QDir(testDataDir).removeRecursively();
}

void TestE2ESimple::setupTestCompetition()
{
    m_testCompetition["id"] = 1;
    m_testCompetition["name"] = "E2E Test Competition";
    m_testCompetition["date"] = QDate::currentDate().toString(Qt::ISODate);
    m_testCompetition["location"] = "Test Stadium";
    m_testCompetition["status"] = "active";
}

void TestE2ESimple::addTestAthletes()
{
    QVariantMap athlete1;
    athlete1["id"] = 1;
    athlete1["name"] = "John Doe";
    athlete1["country"] = "USA";
    athlete1["personalBest"] = 220;
    
    QVariantMap athlete2;
    athlete2["id"] = 2;
    athlete2["name"] = "Jane Smith";
    athlete2["country"] = "CAN";
    athlete2["personalBest"] = 215;
    
    m_testAthletes.append(athlete1);
    m_testAthletes.append(athlete2);
}

void TestE2ESimple::testSystemInitialization()
{
    // Test that all core components are properly initialized
    QVERIFY(m_dbManager != nullptr);
    QVERIFY(m_configManager != nullptr);
    QVERIFY(m_apiClient != nullptr);
    
    // Test database initialization
    QVERIFY(m_dbManager->isConnected());
    
    // Test configuration access
    QVERIFY(!m_configManager->getDatabasePath().isEmpty());
    QVERIFY(!m_configManager->getApiBaseUrl().isEmpty());
    
    qDebug() << "System initialization test completed successfully";
}

void TestE2ESimple::testDatabaseWorkflow()
{
    // Test database operations workflow
    verifyDatabaseOperations();
    
    qDebug() << "Database workflow test completed successfully";
}

void TestE2ESimple::testConfigurationWorkflow()
{
    // Test configuration persistence workflow
    verifyConfigurationPersistence();
    
    qDebug() << "Configuration workflow test completed successfully";
}

void TestE2ESimple::testApiClientIntegration()
{
    // Test API client configuration integration
    verifyApiClientConfiguration();
    
    qDebug() << "API client integration test completed successfully";
}

void TestE2ESimple::testComponentIntegration()
{
    // Test that all components work together
    
    // 1. Configuration affects database path
    QString originalPath = m_configManager->getDatabasePath();
    QString newPath = QStandardPaths::writableLocation(QStandardPaths::TempLocation) + "/integration_test.db";
    
    m_configManager->setDatabasePath(newPath);
    QCOMPARE(m_configManager->getDatabasePath(), newPath);
    
    // 2. Configuration affects API client
    QString originalUrl = m_configManager->getApiBaseUrl();
    QString newUrl = "http://integration-test.example.com";
    
    m_configManager->setApiBaseUrl(newUrl);
    QCOMPARE(m_configManager->getApiBaseUrl(), newUrl);
    
    // 3. Restore original values
    m_configManager->setDatabasePath(originalPath);
    m_configManager->setApiBaseUrl(originalUrl);
    
    QCOMPARE(m_configManager->getDatabasePath(), originalPath);
    QCOMPARE(m_configManager->getApiBaseUrl(), originalUrl);
    
    qDebug() << "Component integration test completed successfully";
}

void TestE2ESimple::verifyDatabaseOperations()
{
    // Test basic database operations
    QSqlQuery query(m_dbManager->m_database);
    
    // Test table creation
    bool success = query.exec("CREATE TABLE IF NOT EXISTS test_table (id INTEGER PRIMARY KEY, name TEXT)");
    QVERIFY(success);
    
    // Test data insertion
    success = query.exec("INSERT INTO test_table (name) VALUES ('Test Entry')");
    QVERIFY(success);
    
    // Test data retrieval
    success = query.exec("SELECT COUNT(*) FROM test_table");
    QVERIFY(success);
    QVERIFY(query.next());
    int count = query.value(0).toInt();
    QVERIFY(count > 0);
    
    // Test cleanup
    success = query.exec("DROP TABLE test_table");
    QVERIFY(success);
}

void TestE2ESimple::verifyConfigurationPersistence()
{
    // Test configuration persistence
    QString testKey = "test_key";
    QString testValue = "test_value_" + QString::number(QDateTime::currentMSecsSinceEpoch());
    
    // Set a test value
    m_configManager->setApiTimeout(12345);
    
    // Verify it's set
    QCOMPARE(m_configManager->getApiTimeout(), 12345);
    
    // Test reload functionality
    m_configManager->reload();
    
    // Verify configuration is still accessible after reload
    QVERIFY(m_configManager->getApiTimeout() > 0);
}

void TestE2ESimple::verifyApiClientConfiguration()
{
    // Test that API client uses configuration values
    
    // Set specific configuration values
    m_configManager->setApiTimeout(30000);
    m_configManager->setApiMaxRetries(5);
    
    // Verify configuration is applied
    QCOMPARE(m_configManager->getApiTimeout(), 30000);
    QCOMPARE(m_configManager->getApiMaxRetries(), 5);
    
    // Test API client initialization (should not crash)
    QVERIFY(m_apiClient != nullptr);
}

QTEST_MAIN(TestE2ESimple)
#include "test_e2e_simple.moc"
