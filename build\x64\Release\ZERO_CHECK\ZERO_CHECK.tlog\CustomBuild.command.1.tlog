^C:\PROJECT\HIGHJUMP\BUILD\CMAKEFILES\D88CA3C2106AB113E89312078E4C97DE\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/PROJECT/HighJump -BC:/PROJECT/HighJump/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/PROJECT/HighJump/build/high-jump-scorer.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
