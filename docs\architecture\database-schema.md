# 数据库架构

## SQLite数据库设计

### 数据库文件
- **文件名**: `competition_data.sqlite`
- **位置**: 应用程序数据目录
- **编码**: UTF-8
- **版本管理**: 通过 `schema_version` 表管理

## 数据表结构

### competitions (比赛表)

```sql
CREATE TABLE competitions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    date TEXT NOT NULL,           -- ISO 8601 格式
    venue TEXT,
    starting_height INTEGER NOT NULL,
    height_increment INTEGER NOT NULL DEFAULT 3,
    status TEXT NOT NULL DEFAULT 'not_started',
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CHECK (starting_height > 0),
    CHECK (height_increment > 0),
    CHECK (status IN ('not_started', 'in_progress', 'paused', 'finished'))
);

CREATE INDEX idx_competitions_date ON competitions(date);
CREATE INDEX idx_competitions_status ON competitions(status);
```

### athletes (运动员表)

```sql
CREATE TABLE athletes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    competition_id INTEGER NOT NULL,
    name TEXT NOT NULL,
    number INTEGER NOT NULL,
    team TEXT,
    personal_best INTEGER,        -- 个人最佳成绩(厘米)
    season_best INTEGER,          -- 赛季最佳成绩(厘米)
    status TEXT NOT NULL DEFAULT 'active',
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (competition_id) REFERENCES competitions(id) ON DELETE CASCADE,
    UNIQUE (competition_id, number),
    CHECK (number > 0),
    CHECK (personal_best IS NULL OR personal_best > 0),
    CHECK (season_best IS NULL OR season_best > 0),
    CHECK (status IN ('active', 'eliminated', 'retired', 'finished'))
);

CREATE INDEX idx_athletes_competition ON athletes(competition_id);
CREATE INDEX idx_athletes_number ON athletes(competition_id, number);
CREATE INDEX idx_athletes_status ON athletes(status);
```

### height_settings (高度设置表)

```sql
CREATE TABLE height_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    competition_id INTEGER NOT NULL,
    height INTEGER NOT NULL,
    sequence_order INTEGER NOT NULL,
    is_current BOOLEAN NOT NULL DEFAULT 0,
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (competition_id) REFERENCES competitions(id) ON DELETE CASCADE,
    UNIQUE (competition_id, height),
    UNIQUE (competition_id, sequence_order),
    CHECK (height > 0),
    CHECK (sequence_order >= 0)
);

CREATE INDEX idx_height_settings_competition ON height_settings(competition_id);
CREATE INDEX idx_height_settings_order ON height_settings(competition_id, sequence_order);
CREATE INDEX idx_height_settings_current ON height_settings(is_current);
```

### attempt_records (试跳记录表)

```sql
CREATE TABLE attempt_records (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    athlete_id INTEGER NOT NULL,
    height INTEGER NOT NULL,
    attempt_number INTEGER NOT NULL,  -- 该高度第几次试跳 (1-3)
    result TEXT NOT NULL,            -- 'success', 'failure', 'pass', 'retire'
    timestamp TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (athlete_id) REFERENCES athletes(id) ON DELETE CASCADE,
    UNIQUE (athlete_id, height, attempt_number),
    CHECK (attempt_number BETWEEN 1 AND 3),
    CHECK (height > 0),
    CHECK (result IN ('success', 'failure', 'pass', 'retire'))
);

CREATE INDEX idx_attempt_records_athlete ON attempt_records(athlete_id);
CREATE INDEX idx_attempt_records_height ON attempt_records(height);
CREATE INDEX idx_attempt_records_timestamp ON attempt_records(timestamp);
CREATE INDEX idx_attempt_records_result ON attempt_records(result);
```

### sync_queue (同步队列表)

```sql
CREATE TABLE sync_queue (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    operation_type TEXT NOT NULL,   -- 'create_attempt', 'update_athlete', etc.
    data_json TEXT NOT NULL,       -- JSON格式的操作数据
    status TEXT NOT NULL DEFAULT 'pending',
    retry_count INTEGER NOT NULL DEFAULT 0,
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_attempt_at TEXT,
    completed_at TEXT,
    error_message TEXT,
    
    CHECK (operation_type IN ('create_attempt', 'update_athlete', 'update_competition')),
    CHECK (status IN ('pending', 'in_progress', 'completed', 'failed')),
    CHECK (retry_count >= 0)
);

CREATE INDEX idx_sync_queue_status ON sync_queue(status);
CREATE INDEX idx_sync_queue_created ON sync_queue(created_at);
CREATE INDEX idx_sync_queue_operation ON sync_queue(operation_type);
```

### schema_version (架构版本表)

```sql
CREATE TABLE schema_version (
    version INTEGER PRIMARY KEY,
    applied_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    description TEXT
);

-- 插入当前版本
INSERT INTO schema_version (version, description) 
VALUES (1, 'Initial schema with basic competition management');
```

## 视图定义

### athlete_summary (运动员汇总视图)

```sql
CREATE VIEW athlete_summary AS
SELECT 
    a.id,
    a.competition_id,
    a.name,
    a.number,
    a.team,
    a.status,
    COALESCE(MAX(CASE WHEN ar.result = 'success' THEN ar.height END), 0) as best_height,
    COUNT(CASE WHEN ar.result = 'failure' THEN 1 END) as total_failures,
    COUNT(CASE WHEN ar.result = 'failure' AND ar.height = (
        SELECT MAX(CASE WHEN ar2.result = 'success' THEN ar2.height END)
        FROM attempt_records ar2 
        WHERE ar2.athlete_id = a.id
    ) THEN 1 END) as failures_at_best
FROM athletes a
LEFT JOIN attempt_records ar ON a.id = ar.athlete_id
GROUP BY a.id, a.competition_id, a.name, a.number, a.team, a.status;
```

### current_rankings (当前排名视图)

```sql
CREATE VIEW current_rankings AS
SELECT 
    *,
    ROW_NUMBER() OVER (
        PARTITION BY competition_id 
        ORDER BY 
            best_height DESC,
            failures_at_best ASC,
            total_failures ASC,
            number ASC
    ) as rank
FROM athlete_summary
WHERE status IN ('active', 'finished');
```

## 数据库管理类

### DatabaseManager

```cpp
class DatabaseManager : public QObject
{
    Q_OBJECT

public:
    static DatabaseManager* instance();
    bool initialize();
    bool isConnected() const;
    
    // 比赛操作
    bool createCompetition(const Competition &competition);
    QList<Competition> getCompetitions();
    Competition getCompetition(int id);
    
    // 运动员操作
    bool createAthlete(const Athlete &athlete);
    QList<Athlete> getAthletes(int competitionId);
    bool updateAthleteStatus(int athleteId, Athlete::AthleteStatus status);
    
    // 试跳记录操作
    bool recordAttempt(const AttemptRecord &record);
    QList<AttemptRecord> getAttempts(int athleteId);
    QList<AttemptRecord> getAttemptsForHeight(int height);
    
    // 排名查询
    QList<RankingEntry> getCurrentRanking(int competitionId);
    
    // 同步队列操作
    bool addToSyncQueue(const SyncQueueEntry &entry);
    QList<SyncQueueEntry> getPendingSyncEntries();
    bool updateSyncStatus(int entryId, SyncQueueEntry::SyncStatus status);

signals:
    void databaseError(const QString &message);

private:
    explicit DatabaseManager(QObject *parent = nullptr);
    bool createTables();
    bool migrateSchema();
    int getCurrentSchemaVersion();
    
    QSqlDatabase m_database;
    static DatabaseManager *s_instance;
};
```

## 数据迁移策略

### 版本升级脚本

```cpp
class SchemaMigrator
{
public:
    static bool migrateToVersion(int targetVersion);
    
private:
    static bool migrateToV2();  // 添加新功能时的迁移
    static bool migrateToV3();  // 修改表结构时的迁移
    
    static bool executeScript(const QString &script);
    static void logMigration(int version, const QString &description);
};
```

## 性能优化

### 索引策略
- 主要查询路径都有相应索引
- 复合索引用于多字段查询
- 避免过多索引影响写入性能

### 查询优化
- 使用预编译语句防止SQL注入
- 批量操作使用事务
- 大量数据查询使用分页

### 缓存策略
- 频繁查询的数据在内存中缓存
- 使用QSqlQuery的缓存机制
- 及时清理不需要的缓存数据

## 数据完整性

### 约束规则
- 外键约束保证引用完整性
- 检查约束验证数据有效性
- 唯一约束防止重复数据

### 事务管理
```cpp
class TransactionScope
{
public:
    explicit TransactionScope(QSqlDatabase &db);
    ~TransactionScope();
    
    void commit();
    void rollback();
    
private:
    QSqlDatabase &m_db;
    bool m_committed;
};
```