#include "competition.h"
#include "athlete.h"
#include <QDebug>

Competition::Competition(QObject *parent)
    : QObject(parent)
    , m_id(-1)
    , m_status(NotStarted)
    , m_currentHeight(0)
    , m_currentHeightIndex(-1)
{
}

void Competition::setId(int id)
{
    if (m_id != id) {
        m_id = id;
        emit idChanged();
    }
}

void Competition::setName(const QString &name)
{
    if (m_name != name) {
        m_name = name;
        emit nameChanged();
    }
}

void Competition::setVenue(const QString &venue)
{
    if (m_venue != venue) {
        m_venue = venue;
        emit venueChanged();
    }
}

void Competition::setDate(const QDateTime &date)
{
    if (m_date != date) {
        m_date = date;
        emit dateChanged();
    }
}

void Competition::setStatus(CompetitionStatus status)
{
    if (m_status != status) {
        m_status = status;
        emit statusChanged();
        
        if (status == InProgress) {
            emit competitionStarted();
        } else if (status == Completed || status == Cancelled) {
            emit competitionEnded();
        }
    }
}

void Competition::setCurrentHeight(int height)
{
    if (m_currentHeight != height) {
        m_currentHeight = height;
        emit currentHeightChanged();
        emit heightChanged(height);
    }
}

void Competition::setDescription(const QString &description)
{
    if (m_description != description) {
        m_description = description;
        emit descriptionChanged();
    }
}

void Competition::addAthlete(Athlete *athlete)
{
    if (!athlete || m_athletes.contains(athlete)) {
        return;
    }
    
    m_athletes.append(athlete);
    emit athleteAdded(athlete);
}

void Competition::removeAthlete(Athlete *athlete)
{
    if (!athlete || !m_athletes.contains(athlete)) {
        return;
    }
    
    m_athletes.removeOne(athlete);
    emit athleteRemoved(athlete);
}

Athlete* Competition::findAthleteByStartNumber(int startNumber) const
{
    for (Athlete *athlete : m_athletes) {
        if (athlete && athlete->startNumber() == startNumber) {
            return athlete;
        }
    }
    return nullptr;
}

bool Competition::canStart() const
{
    return m_status == NotStarted && 
           !m_name.isEmpty() && 
           !m_athletes.isEmpty() && 
           !m_heightProgression.isEmpty();
}

void Competition::startCompetition()
{
    if (!canStart()) {
        qWarning() << "Cannot start competition - preconditions not met";
        return;
    }
    
    setStatus(InProgress);
    m_currentHeightIndex = 0;
    if (!m_heightProgression.isEmpty()) {
        setCurrentHeight(m_heightProgression.first());
    }
}

void Competition::endCompetition()
{
    if (m_status == InProgress) {
        setStatus(Completed);
    }
}

void Competition::pauseCompetition()
{
    // For future implementation - might pause timer or other ongoing processes
    qDebug() << "Competition paused";
}

void Competition::resumeCompetition()
{
    // For future implementation - might resume timer or other ongoing processes  
    qDebug() << "Competition resumed";
}

QList<int> Competition::getHeightProgression() const
{
    return m_heightProgression;
}

void Competition::setHeightProgression(const QList<int> &heights)
{
    m_heightProgression = heights;
    m_currentHeightIndex = -1;
    
    if (!heights.isEmpty() && m_status == NotStarted) {
        setCurrentHeight(heights.first());
        m_currentHeightIndex = 0;
    }
}

bool Competition::canAdvanceToNextHeight() const
{
    return m_status == InProgress && 
           m_currentHeightIndex >= 0 && 
           m_currentHeightIndex < m_heightProgression.size() - 1;
}

void Competition::advanceToNextHeight()
{
    if (canAdvanceToNextHeight()) {
        m_currentHeightIndex++;
        setCurrentHeight(m_heightProgression[m_currentHeightIndex]);
    }
}

int Competition::getNextHeight() const
{
    if (canAdvanceToNextHeight()) {
        return m_heightProgression[m_currentHeightIndex + 1];
    }
    return -1;
}

bool Competition::isValid() const
{
    return !m_name.isEmpty() && m_date.isValid();
}

QString Competition::statusString() const
{
    switch (m_status) {
        case NotStarted: return "Not Started";
        case InProgress: return "In Progress";
        case Completed: return "Completed";
        case Cancelled: return "Cancelled";
        default: return "Unknown";
    }
}

int Competition::getRemainingAthletes() const
{
    // For now, return all athletes. In future, this would exclude eliminated athletes
    return m_athletes.size();
}