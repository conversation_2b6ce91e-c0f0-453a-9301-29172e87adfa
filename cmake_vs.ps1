# CMake Wrapper Script for Visual Studio
# This script provides easy access to Visual Studio's CMake

param(
    [Parameter(ValueFromRemainingArguments=$true)]
    [string[]]$Arguments
)

# Visual Studio CMake path
$CMAKE_PATH = "C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe"

# Check if CMake exists
if (-not (Test-Path $CMAKE_PATH)) {
    Write-Host "❌ CMake not found at: $CMAKE_PATH" -ForegroundColor Red
    Write-Host "Please check your Visual Studio installation" -ForegroundColor Yellow
    exit 1
}

Write-Host "🔧 Using Visual Studio CMake: $CMAKE_PATH" -ForegroundColor Green

# Execute CMake with provided arguments
& $CMAKE_PATH @Arguments

# Show exit code
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ CMake command completed successfully" -ForegroundColor Green
} else {
    Write-Host "❌ CMake command failed with exit code: $LASTEXITCODE" -ForegroundColor Red
}

exit $LASTEXITCODE
