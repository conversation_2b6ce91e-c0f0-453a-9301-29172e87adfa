cmake_minimum_required(VERSION 3.20)

project(high-jump-scorer
    VERSION 1.0.0
    DESCRIPTION "High Jump Competition Management System"
    LANGUAGES CXX
)

# Set C++17 standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find Qt 6 components
find_package(Qt6 6.9.1 REQUIRED COMPONENTS
    Core
    Widgets
    Sql
    Network
    PrintSupport
    Test
)

# Set Qt6 specific settings
qt_standard_project_setup()

# Include directories
include_directories(${CMAKE_SOURCE_DIR}/src)

# Collect source files
file(GLOB_RECURSE SOURCES
    "${CMAKE_SOURCE_DIR}/src/*.cpp"
    "${CMAKE_SOURCE_DIR}/src/*.h"
)

# Add Sprint 2 components
list(APPEND SOURCES
    "src/utils/report_generator.cpp"
    "src/utils/report_generator.h"
    "src/utils/performance_monitor.cpp"
    "src/utils/performance_monitor.h"
    "src/ui/theme_manager.cpp"
    "src/ui/theme_manager.h"
    "src/ui/report_dialog.cpp"
    "src/ui/report_dialog.h"
)

# Create executable
qt_add_executable(${PROJECT_NAME}
    ${SOURCES}
)

# Link Qt6 libraries
target_link_libraries(${PROJECT_NAME} PRIVATE
    Qt6::Core
    Qt6::Widgets
    Qt6::Sql
    Qt6::Network
    Qt6::PrintSupport
)

# Set output directory
set_target_properties(${PROJECT_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Enable testing
enable_testing()

# Add test subdirectory if exists
if(EXISTS ${CMAKE_SOURCE_DIR}/tests)
    add_subdirectory(tests)
endif()

# Platform specific settings
if(WIN32)
    set_target_properties(${PROJECT_NAME} PROPERTIES
        WIN32_EXECUTABLE TRUE
    )
elseif(APPLE)
    set_target_properties(${PROJECT_NAME} PROPERTIES
        MACOSX_BUNDLE TRUE
    )
endif()