#ifndef ATHLETE_DIALOG_H
#define ATHLETE_DIALOG_H

#include <QDialog>
#include <QLineEdit>
#include <QSpinBox>
#include <QDateEdit>
#include <QPushButton>
#include <QFormLayout>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QDialogButtonBox>

class Athlete;

class AthleteDialog : public QDialog
{
    Q_OBJECT

public:
    explicit AthleteDialog(QWidget *parent = nullptr);
    explicit AthleteDialog(Athlete *athlete, QWidget *parent = nullptr);
    
    // Get the created/edited athlete
    Athlete* getAthlete() const;
    
    // Set athlete data for editing
    void setAthlete(Athlete *athlete);

private slots:
    void accept() override;
    void validateInput();

private:
    void setupUI();
    void setupConnections();
    void loadAthleteData();
    bool validateForm();
    
    // Form fields
    QLineEdit *m_firstNameEdit;
    QLineEdit *m_lastNameEdit;
    QLineEdit *m_countryEdit;
    QLineEdit *m_clubEdit;
    QSpinBox *m_startNumberSpinBox;
    QSpinBox *m_personalBestSpinBox;
    QSpinBox *m_seasonBestSpinBox;
    QDateEdit *m_dateOfBirthEdit;
    
    // Buttons
    QDialogButtonBox *m_buttonBox;
    
    // Data
    Athlete *m_athlete;
    bool m_isEditMode;
};

#endif // ATHLETE_DIALOG_H 