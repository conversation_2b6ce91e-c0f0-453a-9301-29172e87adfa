#include "jump_attempt.h"

JumpAttempt::JumpAttempt(QObject *parent)
    : QObject(parent)
    , m_id(-1)
    , m_athleteId(-1)
    , m_competitionId(-1)
    , m_height(0)
    , m_attemptNumber(0)
    , m_result(NotAttempted)
{
    m_timestamp = QDateTime::currentDateTime();
}

void JumpAttempt::setId(int id)
{
    if (m_id != id) {
        m_id = id;
        emit idChanged();
    }
}

void JumpAttempt::setAthleteId(int athleteId)
{
    if (m_athleteId != athleteId) {
        m_athleteId = athleteId;
        emit athleteIdChanged();
    }
}

void JumpAttempt::setCompetitionId(int competitionId)
{
    if (m_competitionId != competitionId) {
        m_competitionId = competitionId;
        emit competitionIdChanged();
    }
}

void JumpAttempt::setHeight(int height)
{
    if (m_height != height) {
        m_height = height;
        emit heightChanged();
    }
}

void JumpAttempt::setAttemptNumber(int attemptNumber)
{
    if (m_attemptNumber != attemptNumber) {
        m_attemptNumber = attemptNumber;
        emit attemptNumberChanged();
    }
}

void JumpAttempt::setResult(AttemptResult result)
{
    if (m_result != result) {
        m_result = result;
        m_timestamp = QDateTime::currentDateTime();
        emit resultChanged();
        emit timestampChanged();
    }
}

void JumpAttempt::setTimestamp(const QDateTime &timestamp)
{
    if (m_timestamp != timestamp) {
        m_timestamp = timestamp;
        emit timestampChanged();
    }
}

void JumpAttempt::setNotes(const QString &notes)
{
    if (m_notes != notes) {
        m_notes = notes;
        emit notesChanged();
    }
}

bool JumpAttempt::isValid() const
{
    return m_athleteId > 0 && 
           m_competitionId > 0 && 
           m_height > 0 && 
           m_attemptNumber > 0 && 
           m_attemptNumber <= 3;  // Maximum 3 attempts per height
}

QString JumpAttempt::resultString() const
{
    return resultToString(m_result);
}

QString JumpAttempt::resultSymbol() const
{
    return resultToSymbol(m_result);
}

bool JumpAttempt::isSuccessful() const
{
    return m_result == Pass;
}

bool JumpAttempt::isFailed() const
{
    return m_result == Fail;
}

bool JumpAttempt::isSkipped() const
{
    return m_result == Skip;
}

QString JumpAttempt::resultToString(AttemptResult result)
{
    switch (result) {
        case NotAttempted: return "Not Attempted";
        case Pass: return "Pass";
        case Fail: return "Fail";
        case Skip: return "Skip";
        default: return "Unknown";
    }
}

QString JumpAttempt::resultToSymbol(AttemptResult result)
{
    switch (result) {
        case NotAttempted: return "";
        case Pass: return "O";
        case Fail: return "X";
        case Skip: return "-";
        default: return "?";
    }
}

JumpAttempt::AttemptResult JumpAttempt::stringToResult(const QString &str)
{
    QString trimmed = str.trimmed().toLower();
    
    if (trimmed == "pass" || trimmed == "o") {
        return Pass;
    } else if (trimmed == "fail" || trimmed == "x") {
        return Fail;
    } else if (trimmed == "skip" || trimmed == "-") {
        return Skip;
    }
    
    return NotAttempted;
}