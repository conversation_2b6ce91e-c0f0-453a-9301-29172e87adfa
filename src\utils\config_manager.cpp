#include "config_manager.h"

#include <QCoreApplication>
#include <QStandardPaths>
#include <QDir>
#include <QFile>
#include <QTextStream>
#include <QDebug>
#include <QMutexLocker>

// Static member initialization
ConfigManager* ConfigManager::s_instance = nullptr;

// Configuration keys
const QString ConfigManager::KEY_API_BASE_URL = "api/base_url";
const QString ConfigManager::KEY_API_TIMEOUT = "api/timeout_ms";
const QString ConfigManager::KEY_API_MAX_RETRIES = "api/max_retries";
const QString ConfigManager::KEY_API_VERSION = "api/version";
const QString ConfigManager::KEY_DATABASE_PATH = "database/path";
const QString ConfigManager::KEY_APPLICATION_LANGUAGE = "application/language";
const QString ConfigManager::KEY_AUTO_SYNC_ENABLED = "sync/auto_enabled";
const QString ConfigManager::KEY_SYNC_INTERVAL = "sync/interval_seconds";

// Default values
const QString ConfigManager::DEFAULT_API_BASE_URL = "http://localhost:8080";
const int ConfigManager::DEFAULT_API_TIMEOUT = 30000; // 30 seconds
const int ConfigManager::DEFAULT_API_MAX_RETRIES = 3;
const QString ConfigManager::DEFAULT_API_VERSION = "v1";
const QString ConfigManager::DEFAULT_APPLICATION_LANGUAGE = "zh_CN";
const bool ConfigManager::DEFAULT_AUTO_SYNC_ENABLED = true;
const int ConfigManager::DEFAULT_SYNC_INTERVAL = 60; // 60 seconds

ConfigManager* ConfigManager::instance()
{
    if (s_instance == nullptr) {
        s_instance = new ConfigManager();
    }
    return s_instance;
}

ConfigManager::ConfigManager(QObject *parent)
    : QObject(parent)
    , m_settings(nullptr)
    , m_isInitialized(false)
{
    // Constructor implementation
}

ConfigManager::~ConfigManager()
{
    // QScopedPointer automatically handles cleanup
}

bool ConfigManager::initialize()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_isInitialized) {
        return true;
    }
    
    qDebug() << "Initializing ConfigManager...";
    
    // Get configuration directory
    QString configDir = QStandardPaths::writableLocation(QStandardPaths::AppConfigLocation);
    
    // Create directory if it doesn't exist
    QDir dir;
    if (!dir.mkpath(configDir)) {
        qCritical() << "Failed to create config directory:" << configDir;
        return false;
    }
    
    // Set config file path
    m_configPath = QDir(configDir).filePath("config.ini");
    
    // Create default config file if it doesn't exist
    if (!QFile::exists(m_configPath)) {
        if (!createDefaultConfigFile()) {
            qCritical() << "Failed to create default configuration file";
            return false;
        }
    }
    
    // Initialize QSettings
    m_settings.reset(new QSettings(m_configPath, QSettings::IniFormat, this));
    
    // Load default values for missing keys
    loadDefaultValues();
    
    // Validate configuration
    validateConfiguration();
    
    // Note: QSettings doesn't provide automatic change notifications
    // External changes need to be handled via reload() method
    
    m_isInitialized = true;
    qDebug() << "ConfigManager initialized successfully";
    qDebug() << "Configuration file:" << m_configPath;
    
    return true;
}

bool ConfigManager::createDefaultConfigFile()
{
    QFile file(m_configPath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qCritical() << "Failed to create config file:" << m_configPath;
        return false;
    }
    
    QTextStream out(&file);
    out.setEncoding(QStringConverter::Utf8);
    
    out << "# High Jump Competition Management System Configuration\n";
    out << "# This file contains application settings\n\n";
    
    out << "[api]\n";
    out << "base_url=" << DEFAULT_API_BASE_URL << "\n";
    out << "timeout_ms=" << DEFAULT_API_TIMEOUT << "\n";
    out << "max_retries=" << DEFAULT_API_MAX_RETRIES << "\n";
    out << "version=" << DEFAULT_API_VERSION << "\n\n";
    
    out << "[database]\n";
    out << "# Database path will be auto-determined if empty\n";
    out << "path=\n\n";
    
    out << "[application]\n";
    out << "language=" << DEFAULT_APPLICATION_LANGUAGE << "\n\n";
    
    out << "[sync]\n";
    out << "auto_enabled=" << (DEFAULT_AUTO_SYNC_ENABLED ? "true" : "false") << "\n";
    out << "interval_seconds=" << DEFAULT_SYNC_INTERVAL << "\n";
    
    file.close();
    
    qDebug() << "Created default configuration file:" << m_configPath;
    return true;
}

void ConfigManager::loadDefaultValues()
{
    // Set default values for any missing configuration keys
    if (!m_settings->contains(KEY_API_BASE_URL)) {
        m_settings->setValue(KEY_API_BASE_URL, DEFAULT_API_BASE_URL);
    }
    
    if (!m_settings->contains(KEY_API_TIMEOUT)) {
        m_settings->setValue(KEY_API_TIMEOUT, DEFAULT_API_TIMEOUT);
    }
    
    if (!m_settings->contains(KEY_API_MAX_RETRIES)) {
        m_settings->setValue(KEY_API_MAX_RETRIES, DEFAULT_API_MAX_RETRIES);
    }
    
    if (!m_settings->contains(KEY_API_VERSION)) {
        m_settings->setValue(KEY_API_VERSION, DEFAULT_API_VERSION);
    }
    
    if (!m_settings->contains(KEY_APPLICATION_LANGUAGE)) {
        m_settings->setValue(KEY_APPLICATION_LANGUAGE, DEFAULT_APPLICATION_LANGUAGE);
    }
    
    if (!m_settings->contains(KEY_AUTO_SYNC_ENABLED)) {
        m_settings->setValue(KEY_AUTO_SYNC_ENABLED, DEFAULT_AUTO_SYNC_ENABLED);
    }
    
    if (!m_settings->contains(KEY_SYNC_INTERVAL)) {
        m_settings->setValue(KEY_SYNC_INTERVAL, DEFAULT_SYNC_INTERVAL);
    }
    
    m_settings->sync();
}

void ConfigManager::validateConfiguration()
{
    // Validate API timeout
    int timeout = getApiTimeout();
    if (timeout < 1000 || timeout > 300000) {  // 1s to 5min
        qWarning() << "Invalid API timeout:" << timeout << "ms, using default";
        setValueInternal(KEY_API_TIMEOUT, DEFAULT_API_TIMEOUT);
        emit apiConfigurationChanged();
    }
    
    // Validate API max retries
    int retries = getApiMaxRetries();
    if (retries < 0 || retries > 10) {
        qWarning() << "Invalid API max retries:" << retries << ", using default";
        setValueInternal(KEY_API_MAX_RETRIES, DEFAULT_API_MAX_RETRIES);
        emit apiConfigurationChanged();
    }
    
    // Validate sync interval
    int interval = getSyncInterval();
    if (interval < 10 || interval > 3600) {  // 10s to 1hour
        qWarning() << "Invalid sync interval:" << interval << "s, using default";
        setValueInternal(KEY_SYNC_INTERVAL, DEFAULT_SYNC_INTERVAL);
    }
}

bool ConfigManager::isInitialized() const
{
    return m_isInitialized;
}

QString ConfigManager::getConfigPath() const
{
    return m_configPath;
}

QString ConfigManager::getApiBaseUrl() const
{
    return getValue(KEY_API_BASE_URL, DEFAULT_API_BASE_URL).toString();
}

void ConfigManager::setApiBaseUrl(const QString &url)
{
    setValue(KEY_API_BASE_URL, url);
    emit apiConfigurationChanged();
}

int ConfigManager::getApiTimeout() const
{
    return getValue(KEY_API_TIMEOUT, DEFAULT_API_TIMEOUT).toInt();
}

void ConfigManager::setApiTimeout(int timeoutMs)
{
    // Validate timeout value - must be positive
    if (timeoutMs <= 0) {
        qWarning() << "Invalid timeout value:" << timeoutMs << "- using default:" << DEFAULT_API_TIMEOUT;
        timeoutMs = DEFAULT_API_TIMEOUT;
    }
    setValue(KEY_API_TIMEOUT, timeoutMs);
    emit apiConfigurationChanged();
}

int ConfigManager::getApiMaxRetries() const
{
    return getValue(KEY_API_MAX_RETRIES, DEFAULT_API_MAX_RETRIES).toInt();
}

void ConfigManager::setApiMaxRetries(int retries)
{
    // Validate retries value - must be non-negative
    if (retries < 0) {
        qWarning() << "Invalid retries value:" << retries << "- using default:" << DEFAULT_API_MAX_RETRIES;
        retries = DEFAULT_API_MAX_RETRIES;
    }
    setValue(KEY_API_MAX_RETRIES, retries);
    emit apiConfigurationChanged();
}

QString ConfigManager::getApiVersion() const
{
    return getValue(KEY_API_VERSION, DEFAULT_API_VERSION).toString();
}

void ConfigManager::setApiVersion(const QString &version)
{
    setValue(KEY_API_VERSION, version);
    emit apiConfigurationChanged();
}

QString ConfigManager::getDatabasePath() const
{
    return getValue(KEY_DATABASE_PATH, QString()).toString();
}

void ConfigManager::setDatabasePath(const QString &path)
{
    setValue(KEY_DATABASE_PATH, path);
}

QString ConfigManager::getApplicationLanguage() const
{
    return getValue(KEY_APPLICATION_LANGUAGE, DEFAULT_APPLICATION_LANGUAGE).toString();
}

void ConfigManager::setApplicationLanguage(const QString &language)
{
    setValue(KEY_APPLICATION_LANGUAGE, language);
}

bool ConfigManager::getAutoSyncEnabled() const
{
    return getValue(KEY_AUTO_SYNC_ENABLED, DEFAULT_AUTO_SYNC_ENABLED).toBool();
}

void ConfigManager::setAutoSyncEnabled(bool enabled)
{
    setValue(KEY_AUTO_SYNC_ENABLED, enabled);
}

int ConfigManager::getSyncInterval() const
{
    return getValue(KEY_SYNC_INTERVAL, DEFAULT_SYNC_INTERVAL).toInt();
}

void ConfigManager::setSyncInterval(int intervalSeconds)
{
    setValue(KEY_SYNC_INTERVAL, intervalSeconds);
}

QVariant ConfigManager::getValue(const QString &key, const QVariant &defaultValue) const
{
    if (!m_settings) {
        return defaultValue;
    }
    
    return m_settings->value(key, defaultValue);
}

void ConfigManager::setValue(const QString &key, const QVariant &value)
{
    if (!m_settings) {
        return;
    }
    
    QMutexLocker locker(&m_mutex);
    setValueInternal(key, value);
}

void ConfigManager::setValueInternal(const QString &key, const QVariant &value)
{
    if (!m_settings) {
        return;
    }
    
    QVariant oldValue = m_settings->value(key);
    m_settings->setValue(key, value);
    m_settings->sync();
    
    if (oldValue != value) {
        emit configurationChanged(key, value);
    }
}

void ConfigManager::save()
{
    if (m_settings) {
        QMutexLocker locker(&m_mutex);
        m_settings->sync();
    }
}

void ConfigManager::reload()
{
    if (m_settings) {
        QMutexLocker locker(&m_mutex);
        // Sync to ensure we read the latest file content
        m_settings->sync();

        // Clear the cache and reload from file
        // QSettings automatically reads from file when accessed
        // We don't call loadDefaultValues() here as that would override file values

        validateConfiguration();

        qDebug() << "Configuration reloaded from file";
    }
}

void ConfigManager::resetToDefaults()
{
    if (!m_settings) {
        return;
    }
    
    QMutexLocker locker(&m_mutex);
    
    m_settings->clear();
    loadDefaultValues();
    
    qDebug() << "Configuration reset to defaults";
}

void ConfigManager::onSettingsChanged()
{
    qDebug() << "Configuration file changed externally, reloading...";
    // Note: We don't auto-reload here to avoid conflicts
    // Users should call reload() manually if needed
}