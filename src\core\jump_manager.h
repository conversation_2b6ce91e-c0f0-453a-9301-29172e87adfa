#ifndef JUMP_MANAGER_H
#define JUMP_MANAGER_H

#include <QObject>
#include <QList>
#include <QMap>
#include <QMutex>
#include "models/jump_attempt.h"

class Athlete;
class Competition;

/**
 * @brief Jump management system for High Jump Competition Management
 * 
 * This class provides comprehensive jump recording, tracking, and analysis
 * capabilities for the High Jump Competition Management System. It serves
 * as the central hub for all jump-related operations including recording
 * attempts, calculating statistics, and managing competition state.
 * 
 * The JumpManager implements the Singleton pattern to ensure consistent
 * jump data management throughout the application. It provides:
 * - Jump attempt recording and validation
 * - Athlete performance tracking and statistics
 * - Competition state management and progression
 * - Elimination tracking and height completion detection
 * - Thread-safe operations for multi-threaded environments
 * 
 * The manager maintains jump data in memory with thread-safe access
 * and provides comprehensive querying capabilities for competition
 * analysis and reporting.
 */
class JumpManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief Gets the singleton instance of JumpManager
     * @return Pointer to the JumpManager instance
     * 
     * Returns the single instance of JumpManager, creating it if necessary.
     * Implements the Singleton pattern for centralized jump management.
     */
    static JumpManager* instance();
    
    // Jump recording
    /**
     * @brief Records a jump attempt for an athlete
     * @param athlete Pointer to the athlete making the attempt
     * @param competition Pointer to the competition context
     * @param height The height being attempted (in centimeters)
     * @param result The result of the jump attempt (Pass/Fail/Skip)
     * @param notes Optional notes about the attempt
     * @return true if recording successful, false otherwise
     * 
     * Records a jump attempt with automatic attempt number calculation.
     * Validates the jump parameters and updates competition state.
     * Emits jumpRecorded() signal on successful recording.
     */
    bool recordJump(Athlete *athlete, Competition *competition, int height, JumpAttempt::AttemptResult result, const QString &notes = QString());
    
    /**
     * @brief Records a jump attempt with specific attempt number
     * @param athlete Pointer to the athlete making the attempt
     * @param competition Pointer to the competition context
     * @param height The height being attempted (in centimeters)
     * @param attemptNumber The specific attempt number (1, 2, or 3)
     * @param result The result of the jump attempt (Pass/Fail/Skip)
     * @param notes Optional notes about the attempt
     * @return true if recording successful, false otherwise
     * 
     * Records a jump attempt with a specific attempt number.
     * Useful for correcting or updating existing jump records.
     */
    bool recordJump(Athlete *athlete, Competition *competition, int height, int attemptNumber, JumpAttempt::AttemptResult result, const QString &notes = QString());
    
    // Jump retrieval
    /**
     * @brief Gets all jump attempts for an athlete in a competition
     * @param athlete Pointer to the athlete
     * @param competition Pointer to the competition
     * @return List of all jump attempts for the athlete
     * 
     * Returns all jump attempts made by the specified athlete
     * in the specified competition, sorted by height and attempt number.
     */
    QList<JumpAttempt*> getAthleteJumps(Athlete *athlete, Competition *competition) const;
    
    /**
     * @brief Gets all jump attempts for an athlete at a specific height
     * @param athlete Pointer to the athlete
     * @param competition Pointer to the competition
     * @param height The height to query (in centimeters)
     * @return List of jump attempts at the specified height
     * 
     * Returns all jump attempts made by the athlete at the specified
     * height, sorted by attempt number.
     */
    QList<JumpAttempt*> getAthleteJumpsAtHeight(Athlete *athlete, Competition *competition, int height) const;
    
    /**
     * @brief Gets a specific jump attempt for an athlete
     * @param athlete Pointer to the athlete
     * @param competition Pointer to the competition
     * @param height The height of the attempt (in centimeters)
     * @param attemptNumber The attempt number (1, 2, or 3)
     * @return Pointer to the specific jump attempt, or nullptr if not found
     * 
     * Returns the specific jump attempt made by the athlete at the
     * specified height and attempt number.
     */
    JumpAttempt* getAthleteJumpAtHeight(Athlete *athlete, Competition *competition, int height, int attemptNumber) const;
    
    // Competition results
    /**
     * @brief Gets all athletes who attempted a specific height
     * @param competition Pointer to the competition
     * @param height The height to query (in centimeters)
     * @return List of athletes who attempted the height
     * 
     * Returns all athletes who made at least one attempt at the
     * specified height, regardless of the result.
     */
    QList<Athlete*> getAthletesAtHeight(Competition *competition, int height) const;
    
    /**
     * @brief Gets all eliminated athletes in a competition
     * @param competition Pointer to the competition
     * @return List of eliminated athletes
     * 
     * Returns all athletes who have been eliminated from the competition
     * due to three consecutive failures at any height.
     */
    QList<Athlete*> getEliminatedAthletes(Competition *competition) const;
    
    /**
     * @brief Gets all active athletes in a competition
     * @param competition Pointer to the competition
     * @return List of active athletes
     * 
     * Returns all athletes who are still active in the competition
     * and have not been eliminated.
     */
    QList<Athlete*> getActiveAthletes(Competition *competition) const;
    
    // Statistics
    /**
     * @brief Gets the number of attempts made by an athlete at a height
     * @param athlete Pointer to the athlete
     * @param competition Pointer to the competition
     * @param height The height to query (in centimeters)
     * @return Number of attempts made at the height
     * 
     * Returns the total number of attempts (Pass, Fail, or Skip)
     * made by the athlete at the specified height.
     */
    int getAthleteAttemptsAtHeight(Athlete *athlete, Competition *competition, int height) const;
    
    /**
     * @brief Gets the total number of failures for an athlete
     * @param athlete Pointer to the athlete
     * @param competition Pointer to the competition
     * @return Total number of failed attempts
     * 
     * Returns the total number of failed attempts made by the athlete
     * across all heights in the competition.
     */
    int getAthleteTotalFailures(Athlete *athlete, Competition *competition) const;
    
    /**
     * @brief Gets the highest height cleared by an athlete
     * @param athlete Pointer to the athlete
     * @param competition Pointer to the competition
     * @return Highest height cleared (in centimeters), or 0 if none
     * 
     * Returns the highest height that the athlete successfully
     * cleared (passed) in the competition.
     */
    int getAthleteHighestClearedHeight(Athlete *athlete, Competition *competition) const;
    
    /**
     * @brief Checks if an athlete is eliminated from the competition
     * @param athlete Pointer to the athlete
     * @param competition Pointer to the competition
     * @return true if athlete is eliminated, false otherwise
     * 
     * Determines if the athlete has been eliminated due to three
     * consecutive failures at any height in the competition.
     */
    bool isAthleteEliminated(Athlete *athlete, Competition *competition) const;
    
    // Competition state
    /**
     * @brief Checks if an athlete can make an attempt at a height
     * @param athlete Pointer to the athlete
     * @param competition Pointer to the competition
     * @param height The height to check (in centimeters)
     * @return true if athlete can attempt the height, false otherwise
     * 
     * Validates whether an athlete is eligible to make an attempt
     * at the specified height based on competition rules and state.
     */
    bool canAthleteJumpAtHeight(Athlete *athlete, Competition *competition, int height) const;
    
    /**
     * @brief Gets the next attempt number for an athlete at a height
     * @param athlete Pointer to the athlete
     * @param competition Pointer to the competition
     * @param height The height to check (in centimeters)
     * @return Next attempt number (1, 2, or 3), or 0 if no more attempts allowed
     * 
     * Calculates the next attempt number for an athlete at the specified
     * height based on their previous attempts and competition rules.
     */
    int getNextAttemptNumber(Athlete *athlete, Competition *competition, int height) const;
    
    // Data management
    /**
     * @brief Clears all jump data for a competition
     * @param competition Pointer to the competition
     * 
     * Removes all jump records associated with the specified competition.
     * This operation cannot be undone and should be used with caution.
     */
    void clearCompetitionData(Competition *competition);
    
    /**
     * @brief Removes all jump data for an athlete in a competition
     * @param athlete Pointer to the athlete
     * @param competition Pointer to the competition
     * 
     * Removes all jump records for the specified athlete in the
     * specified competition. This operation cannot be undone.
     */
    void removeAthleteData(Athlete *athlete, Competition *competition);

signals:
    /**
     * @brief Emitted when a jump is successfully recorded
     * @param jump Pointer to the recorded jump attempt
     * 
     * Signal emitted when a jump attempt is successfully recorded.
     * Used for UI updates and event-driven processing.
     */
    void jumpRecorded(JumpAttempt *jump);
    
    /**
     * @brief Emitted when an athlete is eliminated
     * @param athlete Pointer to the eliminated athlete
     * @param competition Pointer to the competition
     * 
     * Signal emitted when an athlete is eliminated from the competition
     * due to three consecutive failures. Used for UI updates and notifications.
     */
    void athleteEliminated(Athlete *athlete, Competition *competition);
    
    /**
     * @brief Emitted when a height is completed
     * @param height The completed height (in centimeters)
     * @param competition Pointer to the competition
     * 
     * Signal emitted when all athletes have completed their attempts
     * at a specific height. Used for competition progression tracking.
     */
    void heightCompleted(int height, Competition *competition);

private:
    /**
     * @brief Private constructor for Singleton pattern
     * @param parent Parent QObject (default: nullptr)
     * 
     * Private constructor to enforce Singleton pattern.
     * Use instance() to get the JumpManager instance.
     */
    explicit JumpManager(QObject *parent = nullptr);
    
    /**
     * @brief Destructor
     * 
     * Ensures proper cleanup of jump data and resources.
     */
    ~JumpManager();
    
    // Helper methods
    /**
     * @brief Validates jump attempt parameters
     * @param athlete Pointer to the athlete
     * @param competition Pointer to the competition
     * @param height The height being attempted
     * @param attemptNumber The attempt number
     * @return true if jump is valid, false otherwise
     * 
     * Validates that a jump attempt meets all competition rules
     * and constraints before recording.
     */
    bool validateJump(Athlete *athlete, Competition *competition, int height, int attemptNumber) const;
    
    /**
     * @brief Checks if an athlete should be eliminated
     * @param athlete Pointer to the athlete to check
     * @param competition Pointer to the competition
     * 
     * Analyzes an athlete's jump history to determine if they
     * should be eliminated due to three consecutive failures.
     */
    void checkAthleteElimination(Athlete *athlete, Competition *competition);
    
    /**
     * @brief Checks if a height is completed
     * @param height The height to check (in centimeters)
     * @param competition Pointer to the competition
     * 
     * Analyzes all athletes' attempts at a height to determine
     * if the height is completed for all participants.
     */
    void checkHeightCompletion(int height, Competition *competition);
    
    // Data storage
    QMap<QPair<int, int>, QList<JumpAttempt*>> m_jumps; ///< Jump data storage: (competitionId, athleteId) -> jumps
    mutable QMutex m_mutex;                              ///< Thread safety mutex
    
    static JumpManager* s_instance;                      ///< Singleton instance
};

#endif // JUMP_MANAGER_H 