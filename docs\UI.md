# 跳高记分系统 - UI/UX规格说明书 V1.0

### **第一章：引言 (Introduction)**

本文档定义了“跳高记分系统”的用户体验目标、信息架构、用户流程和视觉设计规范。它将作为视觉设计和前端开发的基础，确保我们提供一个有凝聚力的、以用户为中心的体验。

#### **1.1 整体UX目标与原则 (Overall UX Goals & Principles)**

  * **目标用户画像 (Target User Personas):**
      * **核心用户：记分员/技术官员。** 他们是熟悉竞赛规则的专业人士，需要在高压、快节奏的环境中准确、高效地完成工作。他们追求的是零错误和高效率，而不是花哨的功能。
  * **可用性目标 (Usability Goals):**
      * **高效性 (Efficiency):** 核心计分流程必须极其迅速，将用户的点击和思考降至最低。
      * **易学性 (Learnability):** 对于了解规则的记分员来说，界面的操作逻辑应该不言自明，首次使用即可上手。
      * **容错性 (Error Prevention):** 界面设计应通过清晰的视觉焦点引导，防止用户在紧张状态下误操作。
  * **设计原则 (Design Principles):**
    1.  **清晰第一 (Clarity First):** 任何时候，信息的清晰传达都优先于美学装饰。
    2.  **专注与可靠 (Focus & Reliability):** 像一个精密的仪器，界面应保持简洁，只提供完成任务所需的核心功能，让用户感到绝对可靠。
    3.  **效率默认 (Efficiency by Default):** 设计应默认用户追求最高效率，通过自动化和智能引导，帮助用户以最少的步骤完成工作。

#### **1.2 变更日志 (Change Log)**

| 日期 | 版本 | 描述 | 作者 |
| :--- | :--- | :--- | :--- |
| 2025-08-06 | 1.0 | 初始文档创建 | Sally (UX) |

-----

### **第二章：信息架构 (IA)**

#### **2.1 界面流程图 (Site Map)**

```mermaid
graph TD
    A[启动应用] --> B(赛事选择界面);
    B -- 选择一场比赛 --> C(主计分界面);
    C -- 比赛结束/点击导出 --> D[成绩导出对话框];
    D -- 完成或取消导出 --> C;
```

#### **2.2 导航结构 (Navigation Structure)**

本应用采用的是一个**线性的任务流**，而非传统的多层级导航结构。用户的核心路径非常清晰：**启动应用 -\> 选择赛事 -\> 进入计分 -\> 完成计分 -\> 导出结果**。所有核心工作都在“主计分界面”完成。

-----

### **第三章：用户流程 (User Flows)**

#### **3.1 核心流程：比赛中实时计分 (Live Scoring a Competition)**

  * **用户目标:** 准确、高效地记录比赛中每一次的试跳结果，并查看实时更新的排名。
  * **入口:** 用户在“赛事选择界面”成功选择一场比赛后，进入此流程。
  * **成功标准:** 整场比赛顺利完成，所有试跳结果都被准确无误地记录下来，最终排名正确无误。
  * **流程图:**

<!-- end list -->

```mermaid
flowchart TD
    A[开始: 主计分界面加载完毕] --> B{焦点自动定位到<br>当前运动员/试跳};
    B --> C[记分员观察试跳];
    C --> D{记分员点击结果按钮};
    D -- o 成功 --> E[系统记录'o'];
    D -- x 失败 --> F[系统记录'x'];
    D -- - 免跳 --> G[系统记录'-'];
    E --> H[更新表格UI和排名面板];
    F --> H;
    G --> H;
    H --> I{比赛是否结束?};
    I -- 否 --> B;
    I -- 是 --> J[流程结束: 可导出成绩];
```

  * **边缘情况与错误处理:**
      * **记分员误操作:** 系统需要提供一个\*\*“撤销上一步操作” (Undo)\*\* 的功能。
      * **运动员中途弃权:** 系统应将该运动员标记为退赛，并从后续的计分轮次中排除。
      * **网络中断:** 应用应能无缝切换到离线模式，所有计分操作正常进行并保存在本地。

-----

### **第四章：线框图与视觉稿 (Wireframes & Mockups)**

#### **4.1 核心界面布局概念 (Key Screen Layout Concept)**

  * **界面名称:** 主计分界面 (Main Scoring Screen)
  * **布局结构:**
      * **顶部区域:** 显示当前赛事信息和当前的横杆高度。
      * **中心主区域:** **计分表格**，占据屏幕最大空间。
      * **右侧边栏:** **实时排名面板**，始终可见。
      * **底部操作栏:** 放置核心的**操作按钮**（成功, 失败, 免跳, 弃权, 撤销）。
      * **底部状态栏:** 显示**网络/数据同步状态**。

-----

### **第五章：组件库 / 设计系统 (Component Library / Design System)**

#### **5.1 设计系统方案 (Design System Approach)**

不使用任何外部的第三方组件库。通过对 **Qt 自带的标准原生组件** 应用自定义的QSS样式表，来构建自己的、小而精的设计系统，以确保应用轻量、高性能。

#### **5.2 核心组件清单 (Core Components)**

  * **操作按钮 (`QPushButton`)**
  * **计分表格 (`QTableView`)**
  * **排名面板 (`QListView` / `QTableView`)**
  * **对话框 (`QDialog`)**

-----

### **第六章：品牌与风格指南 (Branding & Style Guide)**

#### **6.1 视觉识别 (Visual Identity)**

**简约、专业、以功能为导向**的视觉识别。

#### **6.2 调色板 (Color Palette)**

采用深色主题，以减少视觉疲劳，让数据更突出。
| 颜色类型 | Hex色值 | 用途 |
| :--- | :--- | :--- |
| **主色/强调色** | `#0078D7` | 焦点、选中状态、主按钮 |
| **成功色** | `#107C10` | “成功”状态 |
| **失败/危险色**| `#E81123` | “失败”状态、错误提示 |
| **中性色 (深)** | `#2D2D2D` | 主背景色 |
| **中性色 (中)** | `#3C3C3C` | 控件背景色 |
| **中性色 (浅)** | `#E0E0E0` | 主要文字颜色 |

#### **6.3 字体排印 (Typography)**

  * **字体:** `Segoe UI` (西文/数字), `Microsoft YaHei` (中文)
  * **字号:** 大标题: 20px, 正文/表格: 14px

-----

### **第七章：无障碍性要求 (Accessibility Requirements)**

#### **7.1 合规目标 (Compliance Target)**

以网页无障碍内容指南 **(WCAG) 2.1 AA** 的原则作为指导目标。

#### **7.2 关键要求 (Key Requirements)**

  * **视觉:** 颜色对比度至少为 **4.5:1**，所有交互元素有清晰的**视觉焦点指示器**。
  * **交互:** 应用必须**纯键盘可操作**，Tab顺序符合逻辑。

-----

### **第八章：响应式策略 (Responsiveness Strategy)**

#### **8.1 窗口尺寸与行为 (Window Sizes and Behavior)**

采用**灵活和可缩放的布局**，而非基于设备的断点。

  * **最小推荐尺寸:** `1280 x 720` 像素
  * **适配模式:** 主计分表格为主要伸缩区域，右侧排名面板宽度固定。

-----

### **第九章：动画与微交互 (Animation & Micro-interactions)**

#### **9.1 动态设计原则 (Motion Principles)**

  * **功能性优先:** 所有动画都必须有明确的用途。
  * **响应快速:** 动画时长必须极短（\<200ms）。
  * **性能为重:** 动画效果必须流畅。

-----

### **第十章：性能考量 (Performance Considerations)**

#### **10.1 性能目标 (Performance Goals)**

  * **应用启动:** **\< 3秒**
  * **交互响应:** 视觉反馈 **\< 100ms**; 数据更新 **\< 500ms**
  * **动画流畅度:** **60 FPS**

-----

### **第十一章：后续步骤 (Next Steps)**

#### **11.1 立即行动 (Immediate Actions)**

1.  **评审与确认:** 将本文档分发给所有项目干系人进行最终评审。
2.  **创建视觉稿 (Optional):** 在专业设计工具中创建高保真的视觉设计稿。
3.  **移交架构师:** 将此文档正式移交给**架构师（Architect）**。

#### **11.2 设计移交清单 (Design Handoff Checklist)**

  * [x] 核心用户流程已文档化
  * [x] 基础组件清单已完成
  * [x] 无障碍性要求已定义
  * [x] 灵活布局策略已清晰
  * [x] 品牌与风格指南已包含
  * [x] 性能目标已建立

-----
