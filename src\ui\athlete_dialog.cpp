#include "athlete_dialog.h"
#include "../models/athlete.h"
#include <QMessageBox>
#include <QDate>

AthleteDialog::AthleteDialog(QWidget *parent)
    : QDialog(parent)
    , m_athlete(nullptr)
    , m_isEditMode(false)
{
    setupUI();
    setupConnections();
    setWindowTitle("Add Athlete");
}

AthleteDialog::AthleteDialog(Athlete *athlete, QWidget *parent)
    : QDial<PERSON>(parent)
    , m_athlete(athlete)
    , m_isEditMode(true)
{
    setupUI();
    setupConnections();
    loadAthleteData();
    setWindowTitle("Edit Athlete");
}

void AthleteDialog::setupUI()
{
    setModal(true);
    setMinimumWidth(400);
    
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    
    // Form layout
    QFormLayout *formLayout = new QFormLayout();
    
    // First Name
    m_firstNameEdit = new QLineEdit();
    m_firstNameEdit->setPlaceholderText("Enter first name");
    formLayout->addRow("First Name:", m_firstNameEdit);
    
    // Last Name
    m_lastNameEdit = new QLineEdit();
    m_lastNameEdit->setPlaceholderText("Enter last name");
    formLayout->addRow("Last Name:", m_lastNameEdit);
    
    // Country
    m_countryEdit = new QLineEdit();
    m_countryEdit->setPlaceholderText("Enter country");
    formLayout->addRow("Country:", m_countryEdit);
    
    // Club
    m_clubEdit = new QLineEdit();
    m_clubEdit->setPlaceholderText("Enter club (optional)");
    formLayout->addRow("Club:", m_clubEdit);
    
    // Start Number
    m_startNumberSpinBox = new QSpinBox();
    m_startNumberSpinBox->setRange(1, 999);
    m_startNumberSpinBox->setValue(1);
    formLayout->addRow("Start Number:", m_startNumberSpinBox);
    
    // Personal Best
    m_personalBestSpinBox = new QSpinBox();
    m_personalBestSpinBox->setRange(100, 300);
    m_personalBestSpinBox->setSuffix(" cm");
    m_personalBestSpinBox->setValue(150);
    formLayout->addRow("Personal Best:", m_personalBestSpinBox);
    
    // Season Best
    m_seasonBestSpinBox = new QSpinBox();
    m_seasonBestSpinBox->setRange(100, 300);
    m_seasonBestSpinBox->setSuffix(" cm");
    m_seasonBestSpinBox->setValue(150);
    formLayout->addRow("Season Best:", m_seasonBestSpinBox);
    
    // Date of Birth
    m_dateOfBirthEdit = new QDateEdit();
    m_dateOfBirthEdit->setCalendarPopup(true);
    m_dateOfBirthEdit->setDate(QDate::currentDate().addYears(-18)); // Default to 18 years old
    m_dateOfBirthEdit->setDisplayFormat("dd/MM/yyyy");
    formLayout->addRow("Date of Birth:", m_dateOfBirthEdit);
    
    mainLayout->addLayout(formLayout);
    
    // Button box
    m_buttonBox = new QDialogButtonBox(QDialogButtonBox::Ok | QDialogButtonBox::Cancel);
    mainLayout->addWidget(m_buttonBox);
    
    // Set focus to first name field
    m_firstNameEdit->setFocus();
}

void AthleteDialog::setupConnections()
{
    connect(m_buttonBox, &QDialogButtonBox::accepted, this, &AthleteDialog::accept);
    connect(m_buttonBox, &QDialogButtonBox::rejected, this, &QDialog::reject);
    
    // Connect validation signals
    connect(m_firstNameEdit, &QLineEdit::textChanged, this, &AthleteDialog::validateInput);
    connect(m_lastNameEdit, &QLineEdit::textChanged, this, &AthleteDialog::validateInput);
    connect(m_countryEdit, &QLineEdit::textChanged, this, &AthleteDialog::validateInput);
    connect(m_startNumberSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), this, &AthleteDialog::validateInput);
}

void AthleteDialog::loadAthleteData()
{
    if (!m_athlete) {
        return;
    }
    
    m_firstNameEdit->setText(m_athlete->firstName());
    m_lastNameEdit->setText(m_athlete->lastName());
    m_countryEdit->setText(m_athlete->country());
    m_clubEdit->setText(m_athlete->club());
    m_startNumberSpinBox->setValue(m_athlete->startNumber());
    m_personalBestSpinBox->setValue(m_athlete->personalBest());
    m_seasonBestSpinBox->setValue(m_athlete->seasonBest());
    
    if (m_athlete->dateOfBirth().isValid()) {
        m_dateOfBirthEdit->setDate(m_athlete->dateOfBirth().date());
    }
}

void AthleteDialog::setAthlete(Athlete *athlete)
{
    m_athlete = athlete;
    m_isEditMode = true;
    loadAthleteData();
    setWindowTitle("Edit Athlete");
}

Athlete* AthleteDialog::getAthlete() const
{
    return m_athlete;
}

void AthleteDialog::accept()
{
    if (!validateForm()) {
        return;
    }
    
    // Create or update athlete
    if (!m_athlete) {
        m_athlete = new Athlete();
    }
    
    m_athlete->setFirstName(m_firstNameEdit->text().trimmed());
    m_athlete->setLastName(m_lastNameEdit->text().trimmed());
    m_athlete->setCountry(m_countryEdit->text().trimmed());
    m_athlete->setClub(m_clubEdit->text().trimmed());
    m_athlete->setStartNumber(m_startNumberSpinBox->value());
    m_athlete->setPersonalBest(m_personalBestSpinBox->value());
    m_athlete->setSeasonBest(m_seasonBestSpinBox->value());
    m_athlete->setDateOfBirth(QDateTime(m_dateOfBirthEdit->date(), QTime()));
    
    QDialog::accept();
}

void AthleteDialog::validateInput()
{
    bool isValid = validateForm();
    m_buttonBox->button(QDialogButtonBox::Ok)->setEnabled(isValid);
}

bool AthleteDialog::validateForm()
{
    // Check required fields
    if (m_firstNameEdit->text().trimmed().isEmpty()) {
        return false;
    }
    
    if (m_lastNameEdit->text().trimmed().isEmpty()) {
        return false;
    }
    
    if (m_countryEdit->text().trimmed().isEmpty()) {
        return false;
    }
    
    if (m_startNumberSpinBox->value() <= 0) {
        return false;
    }
    
    // Check date of birth is reasonable (not in the future, not too old)
    QDate birthDate = m_dateOfBirthEdit->date();
    QDate currentDate = QDate::currentDate();
    
    if (birthDate > currentDate) {
        return false;
    }
    
    if (birthDate < currentDate.addYears(-100)) {
        return false;
    }
    
    return true;
} 