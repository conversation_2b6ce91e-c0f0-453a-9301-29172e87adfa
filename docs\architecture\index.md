# 高度计分系统 - 架构文档索引

这是一个基于Qt 6的跳高竞赛计分系统的架构文档集合，采用离线优先的设计理念。

## 📋 架构文档结构

本架构文档按照BMad-Method v4标准进行分片组织，确保开发代理能够高效获取所需的技术信息。

### 核心架构文件
- [tech-stack.md](tech-stack.md) - 技术栈选择和版本规范
- [coding-standards.md](coding-standards.md) - 代码规范和开发标准  
- [source-tree.md](source-tree.md) - 项目目录结构和文件组织

### 系统设计
- [frontend-architecture.md](frontend-architecture.md) - 前端架构设计和组件标准
- [data-models.md](data-models.md) - 数据模型和状态管理
- [database-schema.md](database-schema.md) - SQLite数据库设计和持久化层

### 集成和网络
- [rest-api-spec.md](rest-api-spec.md) - API集成规范和网络通信
- [external-apis.md](external-apis.md) - 外部API集成（如有）
- [core-workflows.md](core-workflows.md) - 核心业务流程

### 运维和部署
- [testing-strategy.md](testing-strategy.md) - 测试要求和质量保证
- [offline-sync.md](offline-sync.md) - 离线模式和数据同步架构

## 🏗️ 架构概览

本系统采用**离线优先**的Qt 6桌面应用架构：

- **核心框架**: Qt 6.9.1 + C++17
- **数据层**: SQLite3 本地数据库
- **网络层**: QNetworkAccessManager API客户端
- **状态管理**: Qt Model/View 架构
- **同步策略**: 客户端优先的数据同步

## 📝 变更日志

| 日期 | 版本 | 描述 | 作者 |
|:---|:---|:---|:---|
| 2025-01-XX | 2.0 | 架构文档分片重构，遵循BMad-Method v4标准 | Assistant |
| 2025-08-06 | 1.1 | 增加离线模式与数据同步架构 | Winston (Architect) |
| 2025-08-06 | 1.0 | 初始架构文档创建 | Winston (Architect) |

## 🎯 开发者指南

根据BMad-Method框架，开发代理将自动加载以下关键文件：
- `coding-standards.md` - 代码规范
- `tech-stack.md` - 技术栈规范
- `source-tree.md` - 项目结构

其他架构文档根据需要按需引用。