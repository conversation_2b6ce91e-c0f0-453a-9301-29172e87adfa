# 跳高记分比赛系统 - 产品需求文档 (PRD) V1.1

### **第一章：目标与背景 (Goals and Background Context)**

#### **目标 (Goals)**
* **准确性:** 消除所有计分和排名计算中的人为错误，确保比赛结果100%准确，维护赛事公信力。
* **效率:** 显著提升赛事运营效率，与传统手动流程相比，将总计时长缩短30%以上。
* **专业性:** 为省级及以上的高水平赛事提供一个专业、可靠的计分工具，提升整体赛事形象。
* **用户体验:** 为记分员提供一个响应迅速、操作简单、可极大降低其心智负担和工作压力的用户界面。

#### **背景 (Background Context)**
当前的跳高比赛现场计分工作普遍依赖手动方式，这一过程效率低下且极易出错，尤其是在处理复杂的排名和决胜赛规则时。这种状况不仅拖慢了比赛节奏，更严重的是，任何计算失误都可能直接影响比赛结果的公正性，损害赛事的专业声誉。

本项目旨在解决此核心痛点，通过开发一款内嵌专业竞赛规则引擎的Windows桌面客户端，为记分员提供一个智能化的辅助工具。它将取代不可靠的人工计算，从而保障高水平赛事结果的准确性和专业性。

#### **变更日志 (Change Log)**
| 日期 | 版本 | 描述 | 作者 |
| :--- | :--- | :--- | :--- |
| 2025-08-04 | 1.0 | 初始文档创建 | John (PM) |
| 2025-08-04 | 1.1 | 根据最终架构更新用户故事以包含离线功能 | John (PM) |

---

### **第二章：需求 (Requirements)**

#### **功能性需求 (Functional Requirements)**
* **FR1: 数据同步:** 系统必须能通过HTTP API从服务器获取指定的赛事信息和运动员名单。
* **FR2: 成绩录入:** 系统必须提供一个界面，供记分员对当前运动员的单次试跳结果进行录入。
* **FR3: 结果类型:** 成绩录入操作必须支持“成功(o)”、“失败(x)”、“免跳(-)”以及“弃权/退赛(r)”四种核心结果。
* **FR4: 规则引擎-状态管理:** 系统必须能根据录入结果，并遵循官方规则，准确地跟踪和管理每位运动员的当前状态（包括：在**特定高度**的试跳次数、是否已被淘汰等）。
* **FR5: 规则引擎-实时排名:** 系统必须根据所有运动员的实时状态，自动、即时地计算并更新全场排名。
* **FR6: 成绩导出:** 系统必须支持将最终的官方成绩单导出为PDF和CSV两种文件格式。
* **FR7: 获取高度设置:** 系统必须在比赛开始时，从服务器**获取预先配置好**的起跳高度和升杆高度序列，并以此为依据自动提示每一轮的高度。
* **FR8: 赛后挑战:** 系统必须支持已获得冠军的运动员，在比赛结束后继续挑战新高度。

#### **非功能性需求 (Non-Functional Requirements)**
* **NFR1: 性能:** 所有核心操作（如录入成绩、更新排名）的界面响应必须是即时的（低于500毫秒），确保在高压比赛环境下的流畅体验。
* **NFR2: 准确性:** 规则引擎的所有计算（包括排名、淘汰判断等）必须与官方规则手册100%一致，不容许任何偏差。
* **NFR3: 易用性:** 界面设计必须高度直观，为接受过培训的记分员最大程度地降低操作复杂度和心智负担。
* **NFR4: 平台兼容性:** 应用程序必须能在 Windows 10 及以上版本的操作系统上稳定运行，主要支持x86架构，并尽可能兼容ARM64。
* **NFR5: 集成安全性:** 与服务器的所有数据通信都必须通过安全的HTTPS连接进行。
* **NFR6: 数据权威性:** 服务器端为所有赛事设置数据（包括高度序列）的唯一权威来源。客户端对该数据只读不改。

---

### **第三章：用户界面设计目标 (User Interface Design Goals)**
本章旨在为界面的“感觉”和“行为”设定一个高层次的指导方向。核心愿景是创造一个**简洁、专业、高效**的操作界面，通过**实时记录流**、**自动聚焦**和**键盘优先**的交互模式，最大程度地降低记分员的心智负担。

---

### **第四章：技术假设 (Technical Assumptions)**
* **仓库结构:** 单一仓库 (Single Repository)。
* **服务架构:** 客户端-服务器架构。
* **测试要求:** 单元测试 + 集成测试。
* **核心技术:** 使用 **Qt 6** 框架构建，目标平台为 **Windows 10+** (x86/ARM64)，通过**HTTPS**进行安全通信。

---

### **第五章：史诗列表 (Epic List)**

* **史诗 1: MVP核心计分应用 (MVP Core Scoring Application)**
    * **目标:** 构建Qt 6应用基础，集成服务器API，并交付一个完整、可靠的计分与排名工具，能够独立、准确地完成一场省级比赛的计分工作，满足所有MVP需求（**包含离线功能**）。
* **史诗 2: 高级裁判与报告模块 (Advanced Officiating & Reporting Module)**
    * **目标:** 通过实现全自动的决胜赛（Jump-off）引擎和对特定体育协会报告格式的支持，将应用能力扩展至满足国家级乃至国际级赛事的专业标准。

---

### **第六章：史诗 1: MVP核心计分应用 - 详情 (修订版)**

#### **用户故事 (User Stories):**

**故事 1.1: 项目基础、本地数据库与API连接**
* **作为一个** 开发人员, **我想要** 搭建好一个基础的Qt 6项目，**并初始化本地SQLite数据库**，同时创建一个能连接到服务器API的服务模块, **以便于** 我有一个稳定的**离线优先**应用基础。
* **验收标准:**
    1.  一个空白的、可运行的Qt 6项目已创建。
    2.  应用首次启动时，能自动在本地创建一个`competition_data.sqlite`数据库文件及所需的数据表。
    3.  项目中已建立一个API服务模块（ApiClient）。
    4.  该服务模块能成功调用服务器的一个测试接口并接收到成功的回应。
    5.  API地址等信息通过配置文件管理。

**故事 1.2: 赛事选择与主界面框架**
* **作为一个** 记分员, **我想要** 在启动应用后，能从服务器获取的列表中选择本次比赛, **以便于** 我能为当前执裁的赛事加载正确的数据。
* **验收标准:**
    1.  应用启动时，通过API获取可用的赛事列表。
    2.  赛事列表清晰地展示给用户。
    3.  用户可以选择其中一场比赛。
    4.  选择后，应用加载主窗口，其中包含计分表格和排名面板的UI框架。

**故事 1.3: 填充运动员与高度数据**
* **作为一个** 记分员, **我想要** 在选择赛事后，看到完整的运动员名单和预设的升杆高度序列被填充到主计分表格中, **以便于** 我对比赛全局有一个清晰的概览。
* **验收标准:**
    1.  选择赛事后，通过API获取该赛事的运动员名单和高度序列。
    2.  主计分表格的“行”被正确填充为运动员信息。
    3.  主计分表格的“列”被正确填充为高度序列。
    4.  第一个运动员在起跳高度的单元格被自动高亮。

**故事 1.4: 实现核心计分操作 (离线优先)**
* **作为一个** 记分员, **我想要** 使用简洁的按钮或键盘快捷键（o, x, -, r）来记录当前焦点运动员的试跳结果, **以便于** 结果能**即时、安全地保存在本地电脑上**，不受网络状态影响。
* **验收标准:**
    1.  界面上提供“成功(o)”, “失败(x)”, “免跳(-)”和“弃权(r)”四个核心操作按钮。
    2.  点击按钮或使用快捷键，能将结果**立即写入本地SQLite数据库**。
    3.  该操作同时被添加到一个本地的“待同步队列”中，并标记为“待处理”状态。
    4.  规则引擎能根据**本地数据库**的数据正确处理运动员状态并更新UI。
    5.  每次录入后，操作焦点会自动按规则移至下一个逻辑状态。

**故事 1.5: 实现实时排名计算**
* **作为一个** 记分员, **我想要** 在每一次试跳结果被录入后，排名面板能自动刷新, **以便于** 我随时都能看到最准确的实时赛况。
* **验收标准:**
    1.  任何一次成绩录入后，规则引擎会立刻重新计算所有运动员的排名。
    2.  排名规则遵循官方标准。
    3.  排名面板准确无误地显示每位选手的名次、姓名和最佳成绩。

**故事 1.6 (新增): 实现离线数据同步**
* **作为一个** 记分员, **我想要** 应用能够自动检测网络连接，并在连接恢复时，将我在离线期间保存的所有数据自动同步到服务器, **以便于** 官方记录总能保持最新，而无需我进行任何手动同步操作。
* **验收标准:**
    1.  ApiClient能够检测到与服务器的网络连接状态（在线/离线）。
    2.  当网络恢复在线时，ApiClient会自动处理本地数据库中的“待同步队列”。
    3.  队列中的每一项“待处理”操作都会被发送到对应的服务器API接口。
    4.  服务器成功返回后，该操作在本地队列中的状态被更新为“已同步”。
    5.  如果API调用失败，该操作保留在队列中，以便后续重试。
    6.  UI界面上有一个不打扰用户的状态图标，用于显示当前的同步状态。

**故事 1.7 (原1.6): 最终成绩导出**
* **作为一个** 记分员, **我想要** 在比赛完全结束后，能将最终的官方成绩单导出为PDF和CSV文件, **以便于** 我能进行成绩归档和分发。
* **验收标准:**
    1.  界面上提供一个“导出成绩”的按钮。
    2.  导出的数据**必须**来源于**本地SQLite数据库**。
    3.  导出的PDF格式清晰、专业。
    4.  导出的CSV文件包含所有结构化的原始数据。
    5.  导出的两种文件中的数据都与计分表格的最终状态完全一致。

---

### **第七章：PRD质量审查报告 (PRD Quality Review Report)**
* **审查结论:** **高度自信 (High Confidence)**.
* **总结:** PRD V1.1 在与架构文档对齐后，内容扎实，逻辑清晰，风险可控，需求明确，已完全准备好进入开发阶段。